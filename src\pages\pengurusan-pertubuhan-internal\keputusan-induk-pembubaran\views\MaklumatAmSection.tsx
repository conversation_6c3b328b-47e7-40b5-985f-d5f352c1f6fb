import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { getMainCategories, getSubCategories } from "@/helpers";
import { useKeputusanIndukPembubaranContext } from "../KeputusanIndukPembubaranProvider";

import { Typography, Box, Grid, useMediaQuery, Theme } from "@mui/material";
import DisabledTextField from "../../../../components/input/DisabledTextField";

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
  "& span": {
    color: "red",
  },
};

const subTitleStyle = {
  color: "var(--primary-color)",
  pb: "30px",
  fontSize: "16px",
  fontWeight: "500 !important",
};

const MaklumatAmSection = () => {
  const { t } = useTranslation();
  const { branchId } = useParams();
  const { liquidationDetailData, societyDetailData, branchDetailData } =
    useKeputusanIndukPembubaranContext();
  const { userRo } = liquidationDetailData || {};

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const mainCategory = getMainCategories(
    Number(societyDetailData?.categoryCodeJppm)
  );
  const subCategory = getSubCategories(
    Number(societyDetailData?.subCategoryCode)
  );

  return (
    <Box
      sx={{
        pl: 2,
        p: 3,
        borderRadius: "10px",
        width: "100%",
      }}
    >
      <Box
        sx={{
          p: 3,
          mb: 3,
          border: "1px solid #DADADA",
          borderRadius: "10px",
        }}
      >
        <Typography
          fontSize="14px"
          color="var(--primary-color)"
          fontWeight="500 !important"
          marginBottom="20px"
        >
          {t("organizationInfo")}
        </Typography>

        <Grid container spacing={2} marginBottom={1} alignItems="center">
          <Grid item xs={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography sx={labelStyle}>{t("namaPertubuhan")}</Typography>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <DisabledTextField value={societyDetailData?.societyName ?? "-"} />
          </Grid>
        </Grid>

        <Grid container spacing={2} marginBottom={1} alignItems="center">
          <Grid item xs={4}>
            <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
              <Typography sx={labelStyle}>
                {t("namaRingkasPertubuhan")}
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={8}>
            <DisabledTextField value={societyDetailData?.shortName ?? "-"} />
          </Grid>
        </Grid>

        {branchId && (
          <>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("namaCawangan")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={branchDetailData?.name ?? "-"} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("nomborCawangan")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={branchDetailData?.branchNo ?? "-"} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>{t("branchAddress")}</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={branchDetailData?.address ?? "-"} />
              </Grid>
            </Grid>
          </>
        )}

        {!branchId && (
          <>
            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationLevel")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={societyDetailData?.societyLevel ?? "-"}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationCategory")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={mainCategory?.categoryNameBm ?? "-"}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("organizationSubCategory2")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField value={subCategory?.categoryNameBm ?? "-"} />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>
                    {t("branchOrganization")}
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={societyDetailData?.hasBranch ? "Ya" : "Tidak"}
                />
              </Grid>
            </Grid>

            <Grid container spacing={2} marginBottom={1} alignItems="center">
              <Grid item xs={4}>
                <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
                  <Typography sx={labelStyle}>Jenis Perlembagaan</Typography>
                </Box>
              </Grid>
              <Grid item xs={8}>
                <DisabledTextField
                  value={societyDetailData?.constitutionType ?? "-"}
                />
              </Grid>
            </Grid>
          </>
        )}
      </Box>
    </Box>
  );
};

export default MaklumatAmSection;
