import React, { useState, useEffect } from "react";
import dayjs from "dayjs";
import { useNavigate } from "react-router-dom";
import { FieldValues } from "react-hook-form";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Card,
  Grid,
  TableContainer,
  Paper,
  TablePagination,
} from "@mui/material";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { useForm } from "@refinedev/react-hook-form";
import useQuery from "../../../../helpers/hooks/useQuery";
import Input from "../../../../components/input/Input";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import CloudDownloadIcon from "@mui/icons-material/CloudDownload";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { AdapterDateFns } from "@mui/x-date-pickers/AdapterDateFns";
import { toCapitalCase } from "@/helpers/string";
import { FeedbackStatus } from "@/helpers";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "16px",
  fontWeight: "600 !important",
};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

enum TypeEnum {
  MAKLUM_BALAS = "jenis_MaklumBalas",
  ADUAN = "jenis_Aduan",
  ISU_SISTEM = "jenis_IsuSistem",
  KEPUASAN_PELANGAN = "jenis_KepuasanPelangan",
}

const AduanCadangan: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const [feedbackList, setFeedbackList] = useState<any>([]);

  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const addressData = addressList?.data?.data || [];

  const getStateName = (stateCode: any) => {
    const stateName = addressData.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };

  function getTypeEnumValue(key: string): string | undefined {
    const result = TypeEnum[key as keyof typeof TypeEnum];
    return t(result);
  }

  const typeOptions = Object.entries(TypeEnum).map(([key, value]) => ({
    label: t(value),
    value: key,
  }));

  const stateList = addressData.filter((i: any) => i.pid == "152");
  const stateOptions = stateList.map((item: any) => ({
    label: item.name,
    value: item.id,
  }));

  const newFSList = [
    ...FeedbackStatus.map((item) => ({
      ...item,
      label: t(item.label),
    })),
    {
      label: t("VIEW_ALL"),
      value: null,
    },
  ];

  const getStatusName = (status: any) => {
    const statusName = FeedbackStatus.filter((i: any) => i.value == status);
    return t(statusName[0]?.label);
  };

  const { watch, setValue, reset } = useForm<FieldValues>({
    defaultValues: {
      type: "",
      status: 1,
      state: "",
      page: 0,
      rowsPerPage: 10,
    },
  });

  const handleReset = () => {
    reset({
      type: "",
      status: "",
      state: "",
      page: 0,
      rowsPerPage: 10,
    });

    setStartDate(null);
    setEndDate(null);
  };

  const {
    data,
    isLoading,
    refetch: fetchFeedbackList,
  } = useQuery({
    url: "complaint/feedback/getNewFeedback",
    filters: [
      { field: "feedbackType", operator: "eq", value: watch("type") },
      { field: "feedbackStatus", operator: "eq", value: watch("status") },
      { field: "stateCode", operator: "eq", value: watch("state") },
      { field: "pageSize", operator: "eq", value: watch("rowsPerPage") },
      { field: "pageNo", operator: "eq", value: watch("page") + 1 },
      ...(startDate
        ? [
            {
              field: "fromDate",
              operator: "eq" as const,
              value: dayjs(startDate).format("YYYY-MM-DD"),
            },
          ]
        : []),
      ...(endDate
        ? [
            {
              field: "toDate",
              operator: "eq" as const,
              value: dayjs(endDate).format("YYYY-MM-DD"),
            },
          ]
        : []),
    ],
    autoFetch: false,
  });

  const handleSubmit = () => {
    fetchFeedbackList();
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    setValue("page", newPage);
  };

  const handleEditNavigation = (row: any) => {
    const encodedId = btoa(row?.id?.toString() || ""); //encode id
    navigate(`kemaskini/${encodedId}`, { state: row });
  };

  const handleChangeRowsPerPage = (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const newRowsPerPage = parseInt(event.target.value, 10);
    setValue("rowsPerPage", newRowsPerPage);
    setValue("page", 0);
  };

  useEffect(() => {
    fetchFeedbackList();
  }, []);

  useEffect(() => {
    fetchFeedbackList();
  }, [
    watch("type"),
    watch("status"),
    watch("state"),
    watch("rowsPerPage"),
    watch("page"),
    startDate,
    endDate,
  ]);

  useEffect(() => {
    setFeedbackList(data?.data?.data?.data ?? []);
  }, [data]);

  const totalRecords = data?.data?.data?.total ?? 0;

  return (
    <>
      <Box
        sx={{
          px: 3,
          py: 4,
          mb: 1,
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            px: 2,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Grid container spacing={2} pl={4} pt={6} pb={4}>
            <Typography variant="subtitle1" sx={sectionStyle}>
              {toCapitalCase(t("carianAduanCadangan"))}
            </Typography>
          </Grid>

          <Grid container pl={2}>
            <Input
              label={toCapitalCase(t("jenis"))}
              value={watch("type")}
              options={typeOptions}
              onChange={(e) => setValue("type", e.target.value)}
              type="select"
            />

            <Input
              label={toCapitalCase(t("status"))}
              value={watch("status")}
              options={newFSList}
              optionsNullValue={t("VIEW_ALL")}
              onChange={(e) => setValue("status", e.target.value)}
              type="select"
            />
            <Input
              label={toCapitalCase(t("state"))}
              value={watch("state")}
              options={stateOptions}
              onChange={(e) => setValue("state", e.target.value)}
              type="select"
            />

            <Grid item xs={12} sm={4} my={"auto"}>
              <Typography sx={labelStyle}>
                {toCapitalCase(t("tarikhTerimaAduan"))}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Box
                sx={{
                  display: "flex",
                  gap: 2,
                  ml: 0.5,
                }}
              >
                <DatePicker
                  label={toCapitalCase(t("mula"))}
                  value={startDate}
                  onChange={(newValue) => setStartDate(newValue)}
                  slotProps={{ textField: { fullWidth: true } }} // Use this for TextField props
                />
                <DatePicker
                  label={toCapitalCase(t("akhir"))}
                  value={endDate}
                  onChange={(newValue) => setEndDate(newValue)}
                  slotProps={{ textField: { fullWidth: true } }} // Use this for TextField props
                />
              </Box>
            </Grid>
          </Grid>
          <Box
            sx={{
              my: 3,
              display: "flex",
              justifyContent: "flex-end",
              gap: 2,
            }}
          >
            <ButtonOutline onClick={() => handleReset()}>
              {toCapitalCase(t("semula"))}
            </ButtonOutline>
            <ButtonPrimary onClick={() => handleSubmit()}>
              {toCapitalCase(t("cari"))}
            </ButtonPrimary>
          </Box>
          <Box
            sx={{
              my: 3,
              display: "flex",
              justifyContent: "flex-end",
              gap: 2,
            }}
          >
            <IconButton
              sx={{
                backgroundColor: "var(--primary-color)",
                color: "white",
                "&:hover": { backgroundColor: "#4db6ac" },
                borderRadius: "7px",
                width: "53px",
              }}
            >
              <CloudDownloadIcon />
            </IconButton>
            <ButtonOutline onClick={() => {}}>
              {toCapitalCase(t("muatTurunLaporanAduanCadangan"))}
            </ButtonOutline>
          </Box>
        </Box>
      </Box>

      <Box>
        <Card
          sx={{
            px: 3,
            pt: 1,
            pb: 4,
            borderRadius: "15px",
            boxShadow: "none",
            height: "100%",
            width: "100%",
          }}
        >
          <Box mt={3}>
            <Box
              sx={{
                border: "1px solid #e0e0e0",
                borderRadius: "15px",
                px: 2,
              }}
            >
              <Typography
                variant="subtitle1"
                sx={{
                  color: "var(--primary-color)",
                  fontWeight: 600,
                  px: 4,
                  pt: 3,
                }}
              >
                {t("senaraiAduanCadangan")}
              </Typography>
              <TableContainer
                component={Paper}
                sx={{
                  boxShadow: "none",
                  backgroundColor: "white",
                  borderRadius: 2.5 * 1.5,
                  p: 1,
                  mb: 3,
                }}
              >
                <Table
                  sx={{
                    minWidth: 650,
                    backgroundColor: "white",
                    borderRadius: "4px",
                    overflow: "hidden",
                  }}
                >
                  <TableHead
                    sx={{
                      "& th": {
                        textAlign: "center",
                      },
                    }}
                  >
                    <TableRow>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {toCapitalCase(t("nama"))}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {toCapitalCase(t("referenceNumber"))}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {toCapitalCase(t("jenis"))}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {toCapitalCase(t("tajuk"))}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {toCapitalCase(t("state"))}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {toCapitalCase(t("tarikhAduanDiterima"))}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {toCapitalCase(t("tempohAduanDiterimaHari"))}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }}>
                        {toCapitalCase(t("status"))}
                      </TableCell>
                      <TableCell sx={{ fontWeight: 400 }} />
                    </TableRow>
                  </TableHead>
                  <TableBody
                    sx={{
                      "& td": {
                        textAlign: "center",
                      },
                    }}
                  >
                    {feedbackList.map((row: any, index: any) => {
                      const formattedDate = dayjs(row.createdDate).format(
                        "DD-MM-YYYY"
                      );
                      return (
                        <TableRow key={index}>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {row.name}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {row.id}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {getTypeEnumValue(row.type)}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {row.title}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {getStateName(row.stateCode)}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {formattedDate}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {row.durationOfComplaintsReceived ?? 0}
                          </TableCell>
                          <TableCell sx={{ fontWeight: 300 }}>
                            {getStatusName(row.status)}
                          </TableCell>
                          <TableCell>
                            <IconButton
                              onClick={() => handleEditNavigation(row)}
                            >
                              <img
                                src={"/editIcon.svg"}
                                alt="Filter Icon"
                                width="14"
                                height="14"
                              />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>

                {!feedbackList.length && isLoading && (
                  <Typography
                    fontSize="0.875rem"
                    fontWeight={300}
                    textAlign="center"
                    lineHeight="24px"
                    marginTop="18px"
                    borderBottom={"1px solid rgba(224, 224, 224, 1)"}
                    paddingBottom="16px"
                  >
                    {t("tiadaData")}
                  </Typography>
                )}

                {feedbackList.length > 0 && (
                  <TablePagination
                    component="div"
                    count={totalRecords}
                    page={watch("page")}
                    onPageChange={handleChangePage}
                    rowsPerPage={watch("rowsPerPage")}
                    onRowsPerPageChange={handleChangeRowsPerPage}
                    labelRowsPerPage={t("rowsPerPage")}
                  />
                )}
              </TableContainer>
            </Box>
          </Box>
        </Card>
      </Box>
    </>
  );
};

export default AduanCadangan;
