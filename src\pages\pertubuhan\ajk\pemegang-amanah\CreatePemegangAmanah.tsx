import { Box, FormHelperText, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { API_URL } from "../../../../api";
import { useCustom, useCustomMutation } from "@refinedev/core";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
} from "../../../../helpers/enums";
import { useEffect, useState } from "react";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { Trustee } from "../../pernyata-tahunan/interface";
import { Controller, useForm } from "react-hook-form";
import Input from "@/components/input/Input";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";
import dayjs from "dayjs";
import { autoDOBSetByIC, autoGenderSetByIC, getLocalStorage } from "@/helpers";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";

export const CreatePemegangAmanah: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const { id: societyId } = useParams();
  const location = useLocation();
  const trusteeId = location.state?.trusteeId;
  const view = location.state?.view;
  const occupationList = getLocalStorage("occupation_list", []);
  const handleBack = () => {
    navigate(-1);
  };

  const form = useForm<Trustee | any>();

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    setValue,
    getValues,
    watch,
    reset,
    setError,
    clearErrors,
  } = form;

  const [JPNError, setJPNError] = useState(false);
  const {
    userICCorrect,
    userNameMatchIC,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idType: watch("identificationType"),
    idNumber: watch("identificationNo"),
    fullName: watch("name"),
  });

  useEffect(() => {
    const isMyKad =
      Number(watch("identificationType")) === 1 ||
      Number(watch("identificationType")) === 4;
    const nameReady = watch("name")?.trim() !== "";
    const idReady = watch("identificationNo")?.length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [
    watch("identificationType"),
    watch("identificationNo"),
    watch("name"),
    integrationStatus,
  ]);

  let identificationNoHelperText: string | undefined = undefined;
  if (
    watch("identificationType") === "1" ||
    watch("identificationType") === "4"
  ) {
    if (typeof errors.identificationNo?.message === "string") {
      identificationNoHelperText = errors.identificationNo.message;
    } else if (watch("identificationNo")?.length === 12 && !userICCorrect) {
      identificationNoHelperText = t("IcDoesNotExist");
    }
  } else if (typeof errors.identificationNo?.message === "string") {
    identificationNoHelperText = errors.identificationNo.message;
  }

  let nameHelperText: string | undefined = undefined;
  if (
    watch("identificationType") === "1" ||
    watch("identificationType") === "4"
  ) {
    if (typeof errors.name?.message === "string") {
      nameHelperText = errors.name.message;
    } else if (
      watch("identificationNo")?.length === 12 &&
      watch("name")?.trim() !== undefined &&
      !userNameMatchIC
    ) {
      nameHelperText = t("invalidName");
    }
  } else if (typeof errors.name?.message === "string") {
    nameHelperText = errors.name.message;
  }

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  const [citizenshipTranslatedList, setCitizenshipTranslatedList] = useState<
    { value: number; label: string }[]
  >([]);

  useEffect(() => {
    const newIDTList = IdTypes.filter(
      (item) => item.value === "1" || item.value === "4" || item.value === "5"
    ).map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setIdTypeTranslatedList(newIDTList);

    const newCitizenshipTList = CitizenshipStatus.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setCitizenshipTranslatedList(newCitizenshipTList);
  }, [t]);

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = data?.data?.data || [];

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { trustee, fetchTrustee } = usejawatankuasaContext();

  useEffect(() => {
    if (trusteeId) {
      fetchTrustee();
      if (trustee) {
        Object.entries(trustee).forEach(([key, value]) => {
          setValue(key, value);
        });
      }
    }
  }, [fetchTrustee]);

  const { mutate: saveTrustee, isLoading: isSaveTrustee } = useCustomMutation();

  const onSubmit = (data: any) => {
    saveTrustee(
      {
        url: trusteeId
          ? `${API_URL}/society/trustee/${trusteeId}`
          : `${API_URL}/society/trustee/create`,
        method: trusteeId ? "put" : "post",
        values: {
          ...data,
          societyId: societyId,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: () => {
          navigate(-1);
        },
      }
    );
  };

  useEffect(() => {
    if (!trusteeId) {
      const inputIc = watch("identificationNo");
      const idNoString =
        typeof inputIc === "number" ? String(inputIc) : inputIc ?? "";

      if (idNoString.length >= 6) {
        const parsed = dayjs(idNoString.substring(0, 6), "YYMMDD", true);

        if (parsed.isValid()) {
          const localDateOnly = new Date(
            parsed.year(),
            parsed.month(),
            parsed.date()
          );
          const localDateOnlyString = dayjs(localDateOnly).format("YYYY-MM-DD");
          setValue("dateOfBirth", localDateOnlyString);
        }
      }
    }
  }, [watch("identificationNo")]);

  useEffect(() => {
    if (Number(getValues("identificationType")) === 1) {
      setValue("visaNo", null);
      setValue("visaExpirationDate", null);
      setValue("permitNo", null);
      setValue("permitExpirationDate", null);
    }
  }, [watch("identificationType")]);

  useEffect(() => {
    if (
      Number(getValues("identificationType")) === 1 ||
      Number(getValues("identificationType")) === 4
    ) {
      if (watch("identificationNo")) {
        setValue(
          "gender",
          autoGenderSetByIC(
            Number(getValues("identificationType")),
            trustee?.gender,
            watch("identificationNo")
          )
        );

        setValue(
          "dateOfBirth",
          autoDOBSetByIC(
            Number(getValues("identificationType")),
            trustee?.dateOfBirth,
            watch("identificationNo"),
            true
          )
        );
      }
    }
  }, [watch("identificationNo")]);

  useEffect(() => {
    const type = Number(watch("identificationType"));
    if (type === 1) {
      setValue("nationalityStatus", 1);
    } else {
      setValue("nationalityStatus", 2);
    }
  }, [watch("identificationType")]);

  return (
    <form noValidate onSubmit={handleSubmit(onSubmit)}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("trusteeInformation")}
          </Typography>

          {JPNError ? (
            <Box sx={{ mt: 1, mb: 1 }}>
              <FormHelperText sx={{ color: "var(--error)" }}>
                {t("JPNError")}
              </FormHelperText>
            </Box>
          ) : null}

          <Controller
            name="appointmentDate"
            control={control}
            defaultValue={getValues("appointmentDate")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => {
              return (
                <Input
                  disabled={view}
                  required
                  {...field}
                  label={t("appointmentDate")}
                  type="date"
                  onChange={(e) =>
                    setValue(field.name, e.target.value, {
                      shouldValidate: true,
                    })
                  }
                  value={form.getValues("appointmentDate")}
                  error={!!error}
                  helperText={error?.message}
                />
              );
            }}
          />
          <Controller
            name="identificationType"
            rules={{
              required: t("requiredValidation"),
            }}
            control={control}
            render={({ field, fieldState: { error } }) => (
              <Input
                {...field}
                label={t("idType")}
                required
                type="select"
                disabled={view || JPNError}
                onChange={(e) => {
                  let value = e.target.value;
                  if (value === "1" || value === "4") {
                    setValue("identificationNo", "");
                  }
                  form.setValue(field.name, value);
                }}
                options={IdTypes.filter(
                  (item) =>
                    Number(item.value) === 1 ||
                    Number(item.value) === 4 ||
                    Number(item.value) === 5
                ).map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
                value={getValues("identificationType")}
              />
            )}
          />
          <Controller
            name="identificationNo"
            control={control}
            defaultValue={getValues("identificationNo")}
            rules={{
              required:
                watch("identificationType") === "1"
                  ? t("idNumberOnlyDigits")
                  : t("requiredValidation"),
              minLength: {
                value: watch("identificationType") === "1" ? 5 : 0,
                message:
                  watch("identificationType") === "1"
                    ? t("idNumberOnlyDigits")
                    : t("requiredValidation"),
              },
              validate: (value) =>
                watch("identificationType") === "1"
                  ? /^\d{5,12}$/.test(value) || t("idNumberOnlyDigits")
                  : true,
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view || JPNError}
                {...field}
                error={!!error || !!identificationNoHelperText}
                helperText={error?.message || identificationNoHelperText}
                required
                label={t("idNumberPlaceholder")}
                value={getValues("identificationNo")}
                type="text"
                inputProps={
                  getValues("identificationType") === "1" ||
                  getValues("identificationType") === "4"
                    ? {
                        inputMode: "numeric",
                        pattern: "[0-9]*",
                        maxLength: 12,
                        minLength: 12,
                      }
                    : undefined
                }
                onChange={(e) => {
                  const inputType = getValues("identificationType");
                  let value = e.target.value;

                  if (inputType === "1" || inputType === "4") {
                    value = value.replace(/\D/g, "").slice(0, 12);
                  }

                  setValue(field.name, value);
                }}
              />
            )}
          />
          <Controller
            name="titleCode"
            control={control}
            defaultValue={getValues("titleCode")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("title")}
                type="select"
                options={ListGelaran}
                value={getValues("titleCode")}
                onChange={(e) => {
                  let value = e.target.value;
                  setValue(field.name, value, { shouldValidate: true });
                }}
                error={!!error}
                helperText={error?.message}
              />
            )}
          />
          <Controller
            name="name"
            control={control}
            defaultValue={getValues("name")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("fullName")}
                error={!!error || !!nameHelperText}
                helperText={error?.message || nameHelperText}
                onChange={(e) => {
                  let value = e.target.value;
                  setValue(field.name, value, { shouldValidate: true });
                }}
              />
            )}
          />
          <Controller
            name="gender"
            control={control}
            defaultValue={getValues("gender")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("gender")}
                type="select"
                options={ListGender.map((gender) => ({
                  ...gender,
                  label: t(gender.label),
                }))}
                onChange={(e) => {
                  let value = e.target.value;
                  setValue(field.name, value, { shouldValidate: true });
                }}
                error={!!error}
                helperText={error?.message}
              />
            )}
          />
          <Controller
            name="citizenshipStatus"
            control={control}
            defaultValue={1}
            render={({ field }) => (
              <Input
                disabled
                {...field}
                label={t("citizenship")}
                type="select"
                options={CitizenshipStatus.map((item) => ({
                  ...item,
                  label: t(item.label),
                }))}
                value={
                  getValues("identificationType") === "1"
                    ? 1
                    : getValues("identificationType") === "4" ||
                      getValues("identificationType") === "5"
                    ? 2
                    : Number(getValues("citizenshipStatus"))
                }
              />
            )}
          />
          <Controller
            name="dateOfBirth"
            control={control}
            defaultValue={getValues("dateOfBirth")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => {
              return (
                <Input
                  disabled={view}
                  required
                  {...field}
                  label={t("dateOfBirth")}
                  type="date"
                  onChange={(e) => {
                    let value = e.target.value;
                    setValue(field.name, value, { shouldValidate: true });
                  }}
                  value={getValues("dateOfBirth")}
                  error={!!error}
                  helperText={error?.message}
                />
              );
            }}
          />
          <Controller
            name="placeOfBirth"
            control={control}
            defaultValue={getValues("placeOfBirth")}
            render={({ field }) => (
              <Input disabled={view} {...field} label={t("placeOfBirth")} />
            )}
          />
          {Number(getValues("identificationType")) !== 1 ? (
            <>
              <Controller
                name="visaNo"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      error={!!errors.visaNo?.message}
                      label={t("nomborVisa")}
                    />
                  );
                }}
              />
              <Controller
                name="visaExpirationDate"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      label={t("visaExpiryDate")}
                      type="date"
                      onChange={(newValue) =>
                        setValue("visaExpirationDate", newValue.target.value)
                      }
                      value={getValues("visaExpirationDate")}
                    />
                  );
                }}
              />
              <Controller
                name="permitNo"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      error={!!errors.permitNo?.message}
                      label={t("nomborPermit")}
                    />
                  );
                }}
              />
              <Controller
                name="permitExpirationDate"
                control={control}
                render={({ field }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      label={t("permitExpiryDate")}
                      type="date"
                      onChange={(newValue) =>
                        setValue("permitExpirationDate", newValue.target.value)
                      }
                      value={getValues("permitExpirationDate")}
                    />
                  );
                }}
              />
            </>
          ) : null}

          <Controller
            name="occupationCode"
            defaultValue={getValues("occupationCode")}
            control={control}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field }) => {
              return (
                //@ts-ignore
                <Input
                  disabled={view}
                  required
                  {...field}
                  error={!!errors.occupationCode?.message}
                  helperText={errors.occupationCode?.message}
                  label={t("occupation")}
                  type="select"
                  onChange={(e) => {
                    let value = e.target.value;
                    setValue(field.name, value, { shouldValidate: true });
                  }}
                  value={getValues("occupationCode")}
                  options={occupationList}
                />
              );
            }}
          />
          <Controller
            name="address"
            control={control}
            defaultValue={getValues("address")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("residentialAddress")}
                onChange={(e) => {
                  let value = e.target.value;
                  setValue(field.name, value, { shouldValidate: true });
                }}
                error={!!error}
                helperText={error?.message}
              />
            )}
          />
          <Controller
            name="stateCode"
            control={control}
            defaultValue={getValues("stateCode")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("state")}
                type="select"
                onChange={(e) => {
                  let value = e.target.value;
                  setValue(field.name, value, { shouldValidate: true });
                }}
                value={parseInt(getValues("stateCode"))}
                options={addressData
                  .filter((item: any) => item.pid === MALAYSIA)
                  .map((item: any) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                error={!!error}
                helperText={error?.message}
              />
            )}
          />
          <Controller
            name="districtCode"
            control={control}
            defaultValue={getValues("districtCode")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("district")}
                type="select"
                value={parseInt(getValues("districtCode"))}
                onChange={(e) => {
                  let value = e.target.value;
                  setValue(field.name, value, { shouldValidate: true });
                }}
                error={!!error}
                helperText={error?.message}
                options={addressData
                  .filter(
                    (item: any) => item.pid === parseInt(watch("stateCode"))
                  )
                  .map((item: any) => ({ label: item.name, value: item.id }))}
              />
            )}
          />
          <Controller
            name="city"
            control={control}
            defaultValue={getValues("city")}
            render={({ field }) => (
              <Input disabled={view} {...field} label={t("city")} type="text" />
            )}
          />
          <Controller
            name="postalCode"
            control={control}
            defaultValue={getValues("postalCode")}
            rules={{
              required: t("postcodeValidation"),
              minLength: { value: 5, message: t("postcodeValidation") },
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("poskod")}
                value={getValues("postalCode")}
                type="text"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                  maxLength: 5,
                }}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "").slice(0, 5);
                  setValue(field.name, value, { shouldValidate: true });
                }}
                error={!!error}
                helperText={error?.message}
              />
            )}
          />
          <Controller
            name="email"
            control={control}
            defaultValue={getValues("email")}
            rules={{
              required: t("emailRequired"),
              pattern: {
                value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                message: "Sila masukkan e-mel yang betul.",
              },
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                label={t("email")}
                type="email"
                onChange={(e) => {
                  let value = e.target.value;
                  setValue(field.name, value, { shouldValidate: true });
                }}
                error={!!error}
                helperText={error?.message}
              />
            )}
          />
          <Controller
            name="mobilePhoneNumber"
            control={control}
            defaultValue={getValues("mobilePhoneNumber")}
            rules={{
              required: t("requiredValidation"),
            }}
            render={({ field, fieldState: { error } }) => (
              <Input
                disabled={view}
                {...field}
                required
                error={!!error}
                helperText={error?.message}
                label={t("phoneNumber")}
                inputProps={{
                  inputMode: "numeric",
                }}
                onChange={(e) => {
                  const type = Number(getValues("identificationType"));
                  const input = e.target as HTMLInputElement;
                  let raw = input.value.replace(/[^\d]/g, "");

                  if (type === 1) {
                    // Allow empty input
                    if (!raw) {
                      setValue("mobilePhoneNumber", null);
                      return;
                    }

                    // Remove leading 60 if present
                    if (raw.startsWith("60")) {
                      raw = raw.slice(2);
                    }

                    // Prevent non-numeric input completely
                    if (isNaN(Number(raw))) {
                      return;
                    }

                    const limitedDigits = raw.slice(0, 10);
                    const formatted = "+60" + limitedDigits;

                    if (limitedDigits.length < 8) {
                      setError("mobilePhoneNumber", {
                        type: "manual",
                        message: t("phoneDigitLimitWarning"),
                      });
                    } else {
                      clearErrors("mobilePhoneNumber");
                    }

                    setValue("mobilePhoneNumber", formatted);
                  } else {
                    // For other types, just set the raw value
                    setValue("mobilePhoneNumber", raw);
                  }
                }}
                onKeyDown={(e) => {
                  const type = Number(getValues("identificationType"));
                  if (type === 1) {
                    // Allow only numeric keys, backspace, delete, tab, arrows
                    if (
                      !/[0-9]|Backspace|Delete|ArrowLeft|ArrowRight|Tab/.test(
                        e.key
                      )
                    ) {
                      e.preventDefault();
                      return;
                    }

                    const input = e.target as HTMLInputElement;
                    const pos = input.selectionStart ?? 0;
                    const hasValue = input.value.length > 0;

                    // restrictions
                    if (hasValue) {
                      if (
                        (e.key.length === 1 && pos < 3) || // typing characters in +60
                        (e.key === "Backspace" && pos <= 3) || // backspacing into +60
                        (e.key === "Delete" && pos < 3) // deleting inside +60
                      ) {
                        e.preventDefault();
                      }
                    }
                  }
                }}
                onClick={(e) => {
                  const type = Number(getValues("identificationType"));
                  if (type === 1) {
                    const input = e.target as HTMLInputElement;
                    if (
                      input.value &&
                      input.selectionStart !== null &&
                      input.selectionStart < 3
                    ) {
                      // Move cursor to after +60 if user clicks inside prefix
                      setTimeout(() => {
                        input.setSelectionRange(3, 3);
                      }, 0);
                    }
                  }
                }}
                onFocus={(e) => {
                  const type = Number(getValues("identificationType"));
                  if (type === 1) {
                    const input = e.target as HTMLInputElement;
                    if (
                      input.value &&
                      input.selectionStart !== null &&
                      input.selectionStart < 3
                    ) {
                      // move cursor to after +60 on focus
                      setTimeout(() => {
                        input.setSelectionRange(3, 3);
                      }, 0);
                    }
                  }
                }}
                placeholder={
                  Number(getValues("identificationType")) === 1 ? "+60" : ""
                }
              />
            )}
          />
          <Controller
            name="homePhoneNumber"
            control={control}
            defaultValue={getValues("homePhoneNumber")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                label={t("homeNumber")}
                type="text"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  form.setValue(field.name, value);
                }}
              />
            )}
          />
          <Controller
            name="officePhoneNumber"
            control={control}
            defaultValue={getValues("officePhoneNumber")}
            render={({ field }) => (
              <Input
                disabled={view}
                {...field}
                label={t("officeNumber")}
                type="text"
                inputProps={{
                  inputMode: "numeric",
                  pattern: "[0-9]*",
                }}
                onChange={(e) => {
                  const value = e.target.value.replace(/\D/g, "");
                  form.setValue(field.name, value);
                }}
              />
            )}
          />
        </Box>

        <Box
          sx={{ display: "flex", justifyContent: "flex-end", mt: 2, gap: 2 }}
        >
          <ButtonOutline onClick={handleBack}>{t("back")}</ButtonOutline>
          {!view ? (
            <ButtonPrimary
              disabled={
                JPNError ||
                ((Number(getValues("identificationType")) === 1 ||
                  Number(getValues("identificationType")) === 4) &&
                  watch("identificationNo")?.length !== 12) ||
                userICCorrect === false ||
                userNameMatchIC === false
              }
              type="submit"
            >
              {t("update")}
            </ButtonPrimary>
          ) : null}
        </Box>
      </Box>
    </form>
  );
};

export default CreatePemegangAmanah;
