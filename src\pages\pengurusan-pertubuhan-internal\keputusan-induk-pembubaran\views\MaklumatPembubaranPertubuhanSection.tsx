import { useTranslation } from "react-i18next";
import { useKeputusanIndukPembubaranContext } from "../KeputusanIndukPembubaranProvider";

import { Typography, Box } from "@mui/material";
import Input from "@/components/input/Input";
import { formatDate } from "@/helpers";

const MaklumatPembubaranPertubuhanSection = () => {
  const { t } = useTranslation();
  const { liquidationDetailData } = useKeputusanIndukPembubaranContext();

  return (
    <>
      <Box
        sx={{
          pl: 2,
          p: 3,
          borderRadius: "10px",
          width: "100%",
        }}
      >
        <Box
          sx={{
            p: 3,
            mb: 3,
            border: "1px solid #DADADA",
            borderRadius: "10px",
          }}
        >
          <Typography
            fontSize="14px"
            color="var(--primary-color)"
            fontWeight="500 !important"
            marginBottom="20px"
          >
            {t("informationOnDissolutionOrganizations")}
          </Typography>

          <Input
            value={liquidationDetailData.secretaryName}
            label={t("secretaryName")}
            disabled
          />

          <Input
            value={liquidationDetailData.secretaryContactNo}
            label={t("secretaryPhone")}
            disabled
          />
          <Input
            value={formatDate(liquidationDetailData.transferDate)}
            type="date"
            label={t("tarikhAlir")}
            disabled
          />
        </Box>
      </Box>

      {/* <DialogFeedbackList open={isDialogOpen} onClose={handleDialogClose} /> */}
    </>
  );
};

export default MaklumatPembubaranPertubuhanSection;
