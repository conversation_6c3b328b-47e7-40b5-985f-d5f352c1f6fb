import { DataGridUI } from "@/components/datagrid/UI";
import { useDataGrid } from "@/helpers";
import { PenguatkuasaanSekatanLiabilitiResponseBodyGet } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Base";
import { Box, Grid, IconButton, Popover, Typography, useTheme } from "@mui/material"
import { GridColDef } from "@mui/x-data-grid";
import { useTranslation } from "react-i18next"
import DownloadIcon from "@/assets/svg/icon-download.svg?react"
import { Switch } from "@/components/switch";
import { TextFieldControllerFormik } from "@/components/input";
import { useState } from "react";
import { Info } from "@mui/icons-material";

export interface ComplaintPEMListRequestBodyGet {

}

export const CardAduanSendingPEMNotification = <
  /**
     * @todo use endpoint data for aduan instead of sekatan liabiliti one.
     */
    // TableData extends ComplaintPEMListRequestBodyGet = ComplaintPEMListRequestBodyGet,
    TableData extends PenguatkuasaanSekatanLiabilitiResponseBodyGet = PenguatkuasaanSekatanLiabilitiResponseBodyGet
>() => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [buttonEl, setButtonEl] = useState<HTMLButtonElement | null>(null);
  const { dataGridProps: { sx, ...dataGridProps } } = useDataGrid<TableData>({
    /**
     * @todo use endpoint data for aduan instead of sekatan liabiliti one.
     */
    resource: "society/blacklist/getAll",
  });

  const error = theme.palette.error.main;
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const columns: GridColDef<TableData>[] = [
    {
      field: "effectiveDate",
      headerName: t("date"),
      renderCell: ({ row }) => row?.effectiveDate ?? "-",
      align: "center",
      minWidth: 51.6 * 3
    },
    {
      field: "name",
      headerName: t("PEMType"),
      headerAlign: "center",
      align: "center",
      minWidth: 51.6 * 3,
      renderCell: ({ id }) => {
        const code = generateStatusFromRandomData(id as number);
        return t("PEMWithNumber", { number: code })
      }
    },
    {
      field: "societyNo",
      headerName: t("status"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => row?.branchNo ?? row?.societyNo ?? "-",
      minWidth: 51.6 * 4
    },
    {
      field: "actions",
      headerName: t("action"),
      headerAlign: "center",
      align: "center",
      minWidth: 51.6 * 3,
      renderCell: ({ id }) => {
        const code = generateStatusFromRandomData(id as number);
        return (
          <div style={{
            display: "flex",
            width: "100%",
            paddingLeft: "1rem",
            alignItems: "center",
            columnGap: "0.5rem"
          }}>
            <Switch />
            <IconButton
              color="primary"
              sx={{ width: "3rem", height: "3rem" }}
            >
              <DownloadIcon width="1.5rem" height="1.5rem" />
            </IconButton>
          </div>
        )
      },
    },
  ];
  const popoverPEMIINoteHelperOpened = Boolean(buttonEl);
  const popoverPEMIINoteHelperId = popoverPEMIINoteHelperOpened ? "popover-pem-note-helper" : undefined;

  const generateStatusFromRandomData = (id: number) => {
    const number = id % 3;
    switch (number) {
      case 2:
        return "III";
      case 1:
        return "II";
      case 0:
      default:
        return "I";
    }
  }

  return (
    <Box
      sx={{
        borderRadius: "1rem",
        backgroundColor: "white",
        boxShadow: "0 0 0.75rem rgba(234, 232, 232, 0.4)",
        padding: "1rem",
        display: "flex",
        flexDirection: "column",
        rowGap: "1rem",
        marginBottom: "0.5rem"
      }}
    >
      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "0.5rem",
          padding: "1.5rem"
        }}
      >
        <Typography className="title" mb="1.25rem">
          {t("sendingPEMNotification")}
        </Typography>
        <DataGridUI
          {...dataGridProps}
          autoHeight
          columns={columns}
          noResultMessage={t("noData")}
        />
        <Grid container spacing={2}>
          <Grid item md={4} xs={12}>
            <Typography sx={labelStyle}>
              {t("PEMNoteWithNumber", { number: "II" })}
              <IconButton
                aria-describedby={popoverPEMIINoteHelperId}
                color="info"
                sx={{ marginLeft: "0.5rem" }}
                onClick={(e) => setButtonEl(e.currentTarget)}
              >
                <Info />
              </IconButton>
              <Popover
                id={popoverPEMIINoteHelperId}
                open={popoverPEMIINoteHelperOpened}
                anchorEl={buttonEl}
                onClose={() => setButtonEl(null)}
                sx={{ maxWidth: "50%" }}
              >
                <Typography sx={{ p: 2, fontSize: 12 }} color={error}>{t("PEMIINotePopoverText")}</Typography>
              </Popover>
            </Typography>
          </Grid>
          <Grid item md={8} xs={12}>
            <TextFieldControllerFormik
              name="branchName"
              helperTextComponentPlacement="INSIDE"
              helperTextFallbackValue=""
              multiline
              rows={4}
            />
          </Grid>
          <Grid item md={4} xs={12}>
            <Typography sx={labelStyle}>
              {t("PEMNoteWithNumber", { number: "III" })}
            </Typography>
          </Grid>
          <Grid item md={8} xs={12}>
            <TextFieldControllerFormik
              name="branchName"
              helperTextComponentPlacement="INSIDE"
              helperTextFallbackValue=""
              multiline
              rows={4}
            />
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}
