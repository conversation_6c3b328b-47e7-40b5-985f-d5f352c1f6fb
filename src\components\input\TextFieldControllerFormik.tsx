import { ChangeEvent, KeyboardEvent, ReactNode, useEffect, useRef, useState, WheelEvent } from "react";
import {
  FormControl,
  FormHelperText,
  TextField,
} from "@mui/material";
import { TextFieldControllerProps } from "./TextFieldController";
import { useField, useFormikContext } from "formik";

export interface TextFieldControllerFormikProps<
  NewValue = any
> extends Omit<TextFieldControllerProps, "control"> {
  /**
   * @default null
   */
  onValueChange?: null | ((newValue: NewValue) => void | Promise<void>)

  /**
   * @default false
   */
  isPostcode?: boolean;

  /**
   * @default " "
   */
  helperTextFallbackValue?: ReactNode;

  /**
   * @default false
   */
  onlyAcceptNumber?: boolean;
}

export const TextFieldControllerFormik = <
  NewValue = any,
  PayloadVal = any,
  PropType extends TextFieldControllerFormikProps<NewValue> = TextFieldControllerFormikProps<NewValue>
>({
  name,
  required = false,
  rules,
  sxControl,
  sxInput,
  onChange,
  type,
  limitWord,
  helperTextComponentPlacement = "OUTSIDE",
  size = "small",
  onValueChange = null,
  isPostcode = false,
  helperTextFallbackValue = " ",
  multiline = false,
  onlyAcceptNumber = false,
  ...otherProps
}: PropType) => {
  const [triggerExecuteOnValueChange, setTriggerExecuteOnValueChange] = useState(false)
  const [field, meta] = useField<PayloadVal>(name);
  const textFieldRef = useRef<HTMLDivElement>(null);
  const { isSubmitting  } = useFormikContext<PayloadVal>();
  const { error } = meta;

  const isValidField = !!error;

  const handlePhoneKeyDown = (
    event: KeyboardEvent<HTMLInputElement>
  ) => {
    if (type === "tel" || type === "telCode") {
      const allowedKeys = [
        "Backspace",
        "Delete",
        "ArrowLeft",
        "ArrowRight",
        "Tab",
      ];
      const allowedChars = /^[0-9+()\s./]*$/;

      if (
        !allowedKeys.includes(event.key) &&
        !allowedChars.test(event.key)
      ) {
        event.preventDefault();
      }
    }
  };

  const countWords = (text: string): number => {
    return text.trim().split(/\s+/).filter(Boolean).length;
  };

  const executeOnValueChange = async () => {
    if (onValueChange !== null) {
      // @ts-expect-error
      await onValueChange(field.value)
      setTriggerExecuteOnValueChange(false)
    }
  }

  useEffect(() => {
    if (onValueChange !== null) {
      setTriggerExecuteOnValueChange(true)
    }
  }, [field.value])

  useEffect(() => {
    if (triggerExecuteOnValueChange) {
      executeOnValueChange()
    }
  }, [triggerExecuteOnValueChange])

  return (
    // <FormControl fullWidth error={isValidField} sx={{ ...sxControl, backgroundColor: otherProps.disabled ? "#66666626" : "#FFF", }}>
    <FormControl
      fullWidth
      error={isValidField}
      sx={{
        ...sxControl,
        ...(multiline && { backgroundColor: otherProps?.disabled ? "#66666626" : "#FFF" })
      }}>
      <TextField
        {...field}
        {...otherProps}
        ref={textFieldRef}
        {...{ type, required, size, multiline }}
        error={!!error}
        disabled={otherProps?.disabled || isSubmitting}
        sx={{
          "& .MuiInputBase-input": {
            ...(!multiline && {
              backgroundColor: otherProps.disabled ? "#66666626" : "#FFF",
            }),
            fontSize: "14px",
            borderRadius: "4px",
            ...sxInput,
          },
          "& .MuiOutlinedInput-notchedOutline": {
            borderColor: "#DADADA !important",
          },
          "& .MuiTextField-root": {
            borderRadius: "4px",
          },
          [`& input[type="number"]`]: {
            MozAppearance: "textfield",
            appearance: "textfield",
          },
          '& input[type=number]::-webkit-outer-spin-button': {
            WebkitAppearance: 'none',
            margin: 0,
          },
          '& input[type=number]::-webkit-inner-spin-button': {
            WebkitAppearance: 'none',
            margin: 0,
          },
          ...otherProps.sx,
        }}
        // value={field.value}
        onKeyDown={
          type === "tel" || type === "telCode"
            ? handlePhoneKeyDown
            : undefined
        }
        // onChange={(e) => handleInputChange(e, field.onChange)}
        {...(helperTextComponentPlacement === "INSIDE" && {
          helperText: error ?? otherProps?.helperText ?? helperTextFallbackValue
        })}
        /**
         * this resolve the issue of input[type=number"] in various browsers
         */
        {...(onlyAcceptNumber  && {
          onInput: (e: ChangeEvent<HTMLInputElement>) => {
            e.target.value = isPostcode
              ? e.target.value
                  .replace(/\D/g, "")
                  .slice(0, 5)
              : e.target.value
                  .replace(/\D/g, "");
            otherProps?.onInput?.(e);
          },
          onWheel: (e: WheelEvent<HTMLInputElement>) => {
            e.preventDefault();
            textFieldRef.current?.blur();
            otherProps?.onWheel?.(e);
          },
          onWheelCapture: (e: WheelEvent<HTMLInputElement>) => {
            e.preventDefault();
            textFieldRef.current?.blur();
            otherProps?.onWheelCapture?.(e);
          },
          inputProps: {
            pattern: isPostcode ? /^\d{5}$/ : /^\d$/,
            ...otherProps?.inputProps
          }
        })}
      />
      {helperTextComponentPlacement === "OUTSIDE" && error && (
        <FormHelperText>
          {error}
        </FormHelperText>
      )}
    </FormControl>
  )
};

TextFieldControllerFormik.displayName = "TextFieldControllerFormik";

export default TextFieldControllerFormik;
