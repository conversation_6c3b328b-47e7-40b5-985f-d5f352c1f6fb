import React, {
  createContext,
  useContext,
  PropsWithChildren,
  useState,
  SetStateAction,
  Dispatch,
} from "react";
import { Outlet, useLocation, useParams } from "react-router-dom";
import { ApplicationStatusList, useQuery } from "@/helpers";
import {
  setAliranTugasAccessRedux,
  setUserPermissionRedux,
} from "@/redux/userReducer";
import { useDispatch } from "react-redux";
import { Box, CircularProgress } from "@mui/material";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import { useSelector } from "react-redux";
import { AddressList, AliranTugas } from "./pernyata-tahunan/interface";

interface branchContextProps {
  isAuthorized: boolean;
  checkIsAuthorizedLoading: boolean;
  isBlackListed: boolean;
  branchData: any;
  branchId: number | string | undefined;
  societyId: number | string | undefined;
  fetchAliranTugasAccess: (module: string) => void;
  isAliranModuleAccess: boolean;
  aliranTugasList: AliranTugas[];
  fetchAliranTugasListWithModule: (module: string) => void;
  isAliranModuleStatus: boolean;
  fetchAliranTugasStatus: (module: string) => void;
  addressList: AddressList[];
  fetchAddressList: () => void;
}

const BranchContext = createContext<branchContextProps | undefined>(undefined);

export function useBranchContext() {
  const context = useContext(BranchContext);

  if (!context) {
    throw new Error("useBranchContext must be used within a BranchProvider");
  }
  return context;
}

const BranchProvider: React.FC<PropsWithChildren> = ({ children }) => {
  // GET SOCIETY ID AND BRANCH ID
  const { id: societyId } = useParams();
  // const decodeBranchId = atob(branchId || "");

  // @ts-ignore
  const branchRedux = useSelector((state) => state.branchData.data);
  //temp solution//
  const decodeBranchId = branchRedux.id || "";
  /////////////////////////

  const [isAuthorized, setIsAuthorized] = useState(false);
  const [isBlackListed, setIsBlackListed] = useState(false);

  const [isAliranModuleAccess, setIsAliranModuleAccess] = useState(false);
  const [isAliranModuleStatus, setIsAliranModuleStatus] =
    useState<boolean>(false);

  const [aliranTugasList, setAliranTugasList] = useState<AliranTugas[]>([]);

  const [branchData, setBranchData] = useState([]);
  const [addressList, setAddressList] = useState<AddressList[]>([]);

  const { data, isLoading: checkisManageAuthorizedIsLoading } = useCustom<any>({
    url: `${API_URL}/society/isManageAuthorized`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      query: {
        societyId: societyId,
        branchId: decodeBranchId,
      },
    },
    queryOptions: {
      // enabled: societyId,
      // retry: false,
      // cacheTime: 0,
      onSuccess(data) {
        setIsAuthorized(data?.data?.data);
      },
    },
  });

  const { data: branchApiData, isLoading: isLoadingBranchApiData } =
    useCustom<any>({
      url: `${API_URL}/society/branch/getById/${decodeBranchId}`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      queryOptions: {
        enabled: decodeBranchId !== null,
        // retry: false,
        // cacheTime: 0,
        onSuccess: (data) => {
          const branchData = data?.data?.data || [];
          setBranchData(branchData);
          setIsBlackListed(data?.data?.data?.subStatusCode === "003" || false);
        },
      },
    });

  ///////////////////
  const { refetch: fetchAliranTugasAccess } = useQuery({
    url: `society/committee-task/me`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: decodeBranchId },
      { field: "module", operator: "eq", value: "" },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setIsAliranModuleAccess(data?.data?.data?.enabled || false);
    },
  });
  const fetchAliranTugasAccessWithModule = (module: string) => {
    fetchAliranTugasAccess({
      filters: [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "branchId", operator: "eq", value: decodeBranchId },
        { field: "module", operator: "eq", value: module }, // Pass the dynamic module here
      ],
    });
  };

  //////////////////
  const { refetch: fetchAliranTugasList } = useQuery({
    url: `society/committee-task/list`,
    filters: [
      { field: "module", operator: "eq", value: "PENGURUSAN_AJK" },
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: decodeBranchId },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAliranTugasList(data?.data?.data || undefined);
    },
  });

  const fetchAliranTugasListWithModule = (module: string) => {
    fetchAliranTugasList({
      filters: [
        { field: "societyId", operator: "eq", value: societyId },
        { field: "branchId", operator: "eq", value: decodeBranchId },
        { field: "module", operator: "eq", value: module }, // Pass the dynamic module here
      ],
    });
  };

  ///////////////
  const { refetch: fetchAliranTugasStatus } = useQuery({ 
    url: `society/branch/${decodeBranchId}/committee-task/status`,
    filters: [ 
      { field: "branchId", operator: "eq", value: decodeBranchId },
      { field: "module", operator: "eq", value: "PENGURUSAN_AJK" },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setIsAliranModuleStatus(data?.data?.data.enabled || false);
    },
  });

  const fetchAliranTugasStatusWithModule = (module: string) => { 
    fetchAliranTugasStatus({
      filters: [ 
        { field: "branchId", operator: "eq", value: decodeBranchId },
        { field: "module", operator: "eq", value: module }, // Pass the dynamic module here
      ],
    });
  };

  ////////
  const { refetch: fetchAddressList } = useQuery({
    url: `society/admin/address/list`,
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      setAddressList(data?.data?.data || []);
    },
  });

  //
  const apiIsLoading =
    checkisManageAuthorizedIsLoading || isLoadingBranchApiData;
  //

  if (apiIsLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "300px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return (
    <BranchContext.Provider
      value={{
        isAuthorized,
        checkIsAuthorizedLoading: checkisManageAuthorizedIsLoading,
        isBlackListed,
        branchData,
        branchId: decodeBranchId,
        societyId,
        fetchAliranTugasAccess: fetchAliranTugasAccessWithModule,
        isAliranModuleAccess,
        aliranTugasList,
        fetchAliranTugasListWithModule,
        isAliranModuleStatus,
        fetchAliranTugasStatus: fetchAliranTugasStatusWithModule,
        addressList,
        fetchAddressList,
      }}
    >
      <Outlet />
    </BranchContext.Provider>
  );
};

export default BranchProvider;
