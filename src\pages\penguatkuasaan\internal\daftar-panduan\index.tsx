import { useEffect, useState } from "react";
import { useForm, FieldValues } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  globalStyles,
  useQuery,
  useMutation,
  useQueryFilterForm,
  DocumentCategoryOptions,
  getDocumentCategoryLabel,
} from "@/helpers";

import {
  Box,
  Typography,
  IconButton,
  Button,
  CircularProgress,
} from "@mui/material";
import {
  ButtonPrimary,
  FormFieldRow,
  Label,
  DataTable,
  IColumn,
  SelectFieldController,
  DialogConfirmation,
} from "@/components";

import {
  IApiPaginatedResponse,
  IApiResponse,
  IDocumentGuidance,
} from "@/types";

import { EditIcon, TrashIcon } from "@/components/icons";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";

const statusOptions = [
  {
    value: 1,
    label: "Aktif",
  },
  {
    value: 2,
    label: "Inaktif",
  },
];

const DaftarPanduan: React.FC = () => {
  const { t, i18n } = useTranslation();
  const classes = globalStyles();
  const navigate = useNavigate();

  const isMyLanguage = i18n.language === "my";

  const [selectedDocument, setSelectedDocument] = useState<{
    id: null | number;
    status: null | number;
  }>({
    id: null,
    status: null,
  });
  const [isSuccess, setIsSuccess] = useState(false);

  const formMethods = useForm<FieldValues>({
    defaultValues: {
      pageNo: 1,
      pageSize: 5,
      categories: "",
      activationStatus: "",
    },
  });
  const { buildFilters } = useQueryFilterForm({
    formMethods,
    pageKey: "pageNo",
  });
  const { control, handleSubmit, watch, setValue } = formMethods;

  const pageNo = watch("pageNo");
  const pageSize = watch("pageSize");

  const { fetch: deleteDocument, isLoading: isDeletingDocument } = useMutation<
    IApiResponse<number>
  >({
    url: `document/external/${selectedDocument.id}`,
    method: "delete",
    onSuccess: () => {
      const filters = buildFilters(pageSize, 1);

      setIsSuccess(true);
      fetchDocuments({ filters });
    },
  });

  const columns: IColumn<IDocumentGuidance>[] = [
    {
      field: "",
      headerName: "No",
      flex: 1,
      align: "center",
      renderCell: ({ rowIndex }) => {
        const number = pageNo * pageSize + rowIndex + 1 - pageSize;
        return <>{number}</>;
      },
    },
    {
      field: "name",
      headerName: "Nama Dokumen",
      flex: 1,
      align: "center",
    },
    {
      field: "referenceNumber",
      headerName: "No Rujukan",
      flex: 1,
      align: "center",
    },
    {
      field: "category",
      headerName: "Kategori",
      flex: 1,
      align: "center",
      renderCell: ({ row }) => getDocumentCategoryLabel(row.category),
    },
    {
      field: "",
      headerName: "Status Kuatkuasa",
      flex: 1,
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Typography
            className={classes.statusBadge}
            sx={{
              border:
                row.activationStatus === 1
                  ? "1px solid var(--primary-color)"
                  : "1px solid #FF0000",
            }}
          >
            {row.activationStatus === 1 ? t("active") : t("inactive")}
          </Typography>
        );
      },
    },
    {
      field: "uploadedBy",
      headerName: "Nama Pegawai Muatnaik",
      flex: 1,
      align: "center",
    },
    {
      field: "",
      headerName: t("action"),
      align: "center",
      renderCell: ({ row }) => {
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <IconButton
              onClick={() => {
                navigate(`./${row.id}`);
              }}
            >
              <EditIcon color="#1DC1C1" />
            </IconButton>

            <IconButton
              sx={{ color: "red" }}
              onClick={() => {
                setIsSuccess(false);
                setSelectedDocument({
                  id: row.id,
                  status: row.activationStatus,
                });
              }}
            >
              <TrashIcon />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const {
    data: documentListResponse,
    refetch: fetchDocuments,
    isLoading: isLoadingDocuments,
  } = useQuery<IApiPaginatedResponse<IDocumentGuidance>>({
    url: "document/external/allDocument",
    autoFetch: false,
  });

  const documentList = documentListResponse?.data.data?.data ?? [];
  const totalDocuments = documentListResponse?.data?.data?.total ?? 0;

  const applyFilters = (pageSize: number, page: number) => {
    const filters = buildFilters(pageSize, page);

    fetchDocuments({ filters });
  };

  const handleSearchSociety = () => {
    setValue("pageNo", 1);
    applyFilters(pageSize, 1);
  };

  const handleChangePage = (newPage: number) => {
    setValue("pageNo", newPage);
    applyFilters(pageSize, newPage);
  };

  const handleChangePageSize = (newPageSize: number) => {
    setValue("pageSize", newPageSize);
    setValue("pageNo", 1);
    applyFilters(newPageSize, 1);
  };

  useEffect(() => {
    applyFilters(pageSize, 1);
  }, []);

  return (
    <>
      <form onSubmit={handleSubmit(handleSearchSociety)}>
        <Box className={classes.section} mb={2}>
          <Box className={classes.sectionBox} mb={2}>
            <Typography className="title" mb={1}>
              {isMyLanguage ? "Carian Dokumen" : "Document Search"}
            </Typography>

            <FormFieldRow
              label={<Label text={isMyLanguage ? "Kategori" : "Category"} />}
              value={
                <SelectFieldController
                  options={DocumentCategoryOptions}
                  control={control}
                  name="categories"
                />
              }
            />

            <FormFieldRow
              label={
                <Label
                  text={isMyLanguage ? "Status Kuatkuasa" : "Status of Force"}
                />
              }
              value={
                <SelectFieldController
                  options={statusOptions}
                  control={control}
                  name="activationStatus"
                />
              }
            />
          </Box>

          <ButtonPrimary
            type="submit"
            sx={{ marginLeft: "auto", display: "block" }}
          >
            {isMyLanguage ? "Cari Dokumen" : "Find Document"}
          </ButtonPrimary>
        </Box>
      </form>

      <Box className={classes.countBadge} mb={2}>
        <Typography fontSize="34px">
          {isLoadingDocuments ? (
            <CircularProgress size={25} sx={{ color: "#fff" }} />
          ) : (
            totalDocuments
          )}
        </Typography>
        <Typography fontSize="20px">
          {isMyLanguage
            ? "Jumlah Keseleruhan Dokumen"
            : "Total Number of Documents"}
        </Typography>
      </Box>

      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={2}>
          <Typography className="title" mb={1}>
            {isMyLanguage ? "Senarai Pertubuhan" : "Organization List"}
          </Typography>

          <Box sx={{ width: "90%", marginInline: "auto" }}>
            <DataTable
              columns={columns}
              rows={documentList}
              page={pageNo}
              rowsPerPage={pageSize}
              pagination={pageSize > 5}
              totalCount={totalDocuments}
              isLoading={isLoadingDocuments}
              onPageChange={handleChangePage}
              onPageSizeChange={handleChangePageSize}
            />

            {!(pageSize > 5) && (
              <Box
                style={{
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "center",
                  marginTop: "2rem",
                }}
              >
                <Button
                  onClick={() => handleChangePageSize(10)}
                  className={classes.btnOutline}
                  sx={{
                    mt: 3,
                    marginInline: "auto",
                  }}
                >
                  {t("seeFullView")}
                  <ArrowRightAltIcon sx={{ color: "#666666B2" }} />
                </Button>
              </Box>
            )}
          </Box>
        </Box>
      </Box>

      <DialogConfirmation
        isMutating={isDeletingDocument}
        open={!!selectedDocument.id}
        onClose={() =>
          setSelectedDocument({
            id: null,
            status: null,
          })
        }
        onAction={() => deleteDocument()}
        onConfirmationText={
          selectedDocument.status === 1
            ? `Dokumen Ini Berstatus Aktif. Anda Pasti Untuk Padam Dari Dilihat Pengguna?`
            : `Adakah Anda Pasti Untuk Padam Dokumen Ini ?`
        }
        isSuccess={isSuccess}
        onSuccessText={
          isMyLanguage
            ? "Dokumen Anda Berjaya Dipadamkan"
            : "Your Document Has Been Successfully Deleted"
        }
      />
    </>
  );
};

export default DaftarPanduan;
