import React, { useEffect, useState } from "react";
import { Box, Typography, TextField, useTheme } from "@mui/material";
import { useForm } from "@refinedev/react-hook-form";
import { useTranslation } from "react-i18next";
import { Controller } from "react-hook-form";
import { useNavigate } from "react-router-dom";
import { useCreate, useCustom } from "@refinedev/core";
import { ButtonPrimary, ButtonText } from "@/components/button";
import { useQuery, ValidationTimeTwoMin } from "@/helpers";
import { API_URL } from "@/api";
import { MaskEmail } from "@/components/mask/maskEmail";

export const Otp = ({
  onClose,
  onPassOTP,
  onBackToForgotPassword,
}: {
  onClose?: () => void;
  onPassOTP?: () => void;
  onBackToForgotPassword?: () => void;
}) => {
  const { t } = useTranslation();

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm({
    defaultValues: {
      verificationCode: "",
    },
  });
  const navigate = useNavigate();
  const verificationCode = watch("verificationCode");
  const isVerificationCodeComplete = verificationCode.length === 6;
  const [isOtpSent, setIsOtpSent] = useState(false);
  const [resubmit, setResubmit] = useState(false);
  const [timeLeft, setTimeLeft] = useState(ValidationTimeTwoMin);
  const IC = Number(
    localStorage.getItem("identificationNo")?.replace(/"/g, "") || 0
  );

  useEffect(() => {
    // exit early when we reach 0
    if (timeLeft <= 0) {
      return;
    }

    // save intervalId to clear the interval when the
    // component re-renders
    const intervalId = setInterval(() => {
      setTimeLeft((prevTimeLeft) => prevTimeLeft - 1);
    }, 1000);

    // clear interval on re-render to avoid memory leaks
    return () => clearInterval(intervalId);
    // add timeLeft as a dependency to re-rerun the effect
    // when we update it
  }, [timeLeft]);

  const { mutate: verifyOtp, isLoading: isVerifyLoading } = useCreate();

  const onSubmit = (data: any) => {
    // Handle password reset logic here
    console.log(data);

    const verificationCode = data.verificationCode;

    verifyOtp({
      resource: "user/otp/verifyOtp",
      values: {
        otp: verificationCode,
        moduleType: "FORGOT_PASSWORD",
        identifier: JSON.parse(localStorage.getItem("forgotPassword") as any),
      },
      meta: {
        method: "post",
      },
      successNotification(data, values, resource) {
        if (!data?.data?.data?.otpVerified) {
          return {
            message: t(data?.data?.data?.message),
            type: "error",
          };
        }

        if (localStorage.getItem("otp")) {
          localStorage.removeItem("otp");
        }

        localStorage.setItem("otp", JSON.stringify(verificationCode));
        setIsOtpSent(true);

        if (data?.data?.data?.otpVerified) {
          onPassOTP?.();
        }

        return {
          message: t(data?.data?.data?.message),
          type: "success",
        };
      },
      errorNotification(error, values, resource) {
        return {
          message: t("error"),
          type: "error",
        };
      },
    });
  };

  const { data, isLoading: isForgotPasswordLoading } = useCustom<any>({
    url: `${API_URL}/user/auth/forgotPassword`,
    method: "get",
    config: {
      query: {
        identificationNo: IC,
      },
    },
    queryOptions: {
      enabled: resubmit,
      retry: false,
      cacheTime: 0,
    },
    successNotification(data, values, resource) {
      console.log("success", data);
      setResubmit(false);

      return {
        message: t("success"),
        type: "success",
      };
    },
    errorNotification(error, values, resource) {
      console.log("error", error);
      setResubmit(false);
      return {
        message: t("error"),
        type: "error",
      };
    },
  });

  const formatTimeLeft = (seconds: number) => {
    const minutes = String(Math.floor(seconds / 60)).padStart(2, "0");
    const secs = String(seconds % 60).padStart(2, "0");
    return `${minutes}:${secs}`;
  };

  return (
    <Box>
      <Box
        sx={{
          display: "grid",
          pl: { xs: 2, sm: 6, md: 6 },
          pr: { xs: 2, sm: 6, md: 6 },
          pt: 4,
        }}
        component="form"
        noValidate
        onSubmit={handleSubmit(onSubmit)}
      >
        <Typography
          sx={{ mb: { sm: 2, md: 3, lg: 3, xl: 6 } }}
          className="title-login"
        >
          {t("OTPVerification")}
        </Typography>
        <Typography
          className="label"
          sx={{
            fontSize: "14px !important",
            color: "#55556D",
            mt: 4,
            mb: 5,
          }}
        >
          {t("otpSendToEmail", {
            email: MaskEmail(
              JSON.parse(localStorage.getItem("forgotPassword") as any)
            ),
          })}
        </Typography>

        <Controller
          name="verificationCode"
          control={control}
          rules={{
            required: t("VerificationCodeIsRequired"),
            validate: (value) =>
              value.length === 6 || t("VerificationCodeMustBe6Digits"),
          }}
          render={({ field }) => (
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                gap: { xs: 0.5, sm: 1, md: 1.5 },
                overflow: "hidden",
              }}
            >
              {[0, 1, 2, 3, 4, 5].map((index) => (
                <TextField
                  key={index}
                  {...field}
                  value={field.value[index] || ""}
                  inputProps={{
                    inputMode: "numeric",
                    pattern: "[0-9]*",
                  }}
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^0-9]/g, "");
                    const currentValue = field.value || "";
                    const newValue = currentValue.split("");

                    if (value.length > 1) {
                      const pastedValues = value.split("").slice(0, 6);
                      pastedValues.forEach((char, i) => {
                        newValue[i] = char;
                      });
                      field.onChange(newValue.join(""));
                      return;
                    }

                    if (
                      (e.nativeEvent as any).inputType ===
                      "deleteContentBackward"
                    ) {
                      newValue[index] = "";
                      if (index > 0) {
                        const prevInput =
                          e.target.parentElement?.parentElement?.previousElementSibling?.querySelector(
                            "input"
                          );
                        if (prevInput) {
                          prevInput.focus();
                        }
                      }
                    } else if (value) {
                      newValue[index] = value.slice(-1);
                      if (index < 5) {
                        const nextInput =
                          e.target.parentElement?.parentElement?.nextElementSibling?.querySelector(
                            "input"
                          );
                        if (nextInput) {
                          nextInput.focus();
                        }
                      }
                    }

                    field.onChange(newValue.join(""));
                  }}
                  onKeyDown={(e) => {
                    if (
                      e.key === "Backspace" &&
                      !field.value[index] &&
                      index > 0
                    ) {
                      const prevInput = (
                        e.target as any
                      ).parentElement?.parentElement?.previousElementSibling?.querySelector(
                        "input"
                      );
                      if (prevInput) {
                        prevInput.focus();
                      }
                    }
                  }}
                  sx={{
                    width: { xs: "35px", sm: "40px", md: "50px" },
                    height: { xs: "45px", sm: "50px", md: "60px" },
                    color: "#666666",
                    "& .MuiOutlinedInput-root": {
                      height: "100%",
                      "& fieldset": {
                        borderColor: "#DADADA",
                        borderRadius: "10px",
                      },
                    },
                    "& .MuiInputBase-input": {
                      textAlign: "center",
                      fontSize: { xs: "18px", sm: "20px", md: "24px" },
                      p: 0,
                      height: "100%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    },
                  }}
                />
              ))}
            </Box>
          )}
        />

        <ButtonPrimary
          type="submit"
          fullWidth
          variant="contained"
          disabled={!isVerificationCodeComplete || isOtpSent || isVerifyLoading}
          sx={{
            fontFamily: "Poppins, sans-serif",
            mt: 15,
            padding: "11px 16px 11px 16px",
            borderRadius: "10px",
            fontWeight: 400,
            fontSize: "14px",
          }}
        >
          {t("Continue")}
        </ButtonPrimary>
        <Box
          onClick={timeLeft === 0 ? () => setResubmit(true) : undefined}
          sx={{
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
            mt: 2,
            mb: 4,
            cursor: timeLeft === 0 ? "pointer" : "default",
          }}
        >
          <Typography
            sx={{
              fontFamily: "Poppins",
              fontSize: "14px",
              fontWeight: 600,
              lineHeight: "17.5px",
              textAlign: "left",
              color: "#000000B2",
              opacity: timeLeft === 0 ? 0.7 : 0.4,
              mr: 1,
            }}
          >
            {t("ResendCodeIn")}
          </Typography>
          <Typography
            sx={{
              fontFamily: "Arial, sans-serif",
              fontSize: "15px",
              fontWeight: 400,
              lineHeight: "18.75px",
              textAlign: "left",
              color: "#000000B2",
              opacity: 0.7,
            }}
          >
            <span style={{ fontFamily: "Arial, sans-serif" }}>
              {formatTimeLeft(timeLeft)}
            </span>
          </Typography>
        </Box>
      </Box>
    </Box>
  );
};
