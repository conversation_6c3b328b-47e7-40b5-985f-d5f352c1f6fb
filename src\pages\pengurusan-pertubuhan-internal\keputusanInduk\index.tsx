import React, { useEffect, useMemo, useState } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../api";

import { Box, Typography } from "@mui/material";
import WrapContent from "../View/WrapContent";
import PembubaranTab from "./PembubaranTab";
import KeputusanIndukKelulusan from "./KelulusanTab";
import PegawaiAwamTab from "./PegaiAwamTab";
import PegawaiHartaTab from "./PegawaiHartaTab";
import PembaharuanSetiausahaTab from "./PembaharuanSetiausahaTab";
import PermohonanBukanWargaNegaraTab from "./PermohonanBukanWarganegaraTab";
import PembaharuanSetiausahaMigrasiTab from "./PembaharuanSetiausahaMigrasiTab";
import PindaanUndangUndangIndukTab from "./PindaanUndangUndangInduk";
import PendaftaranIndukMenugguUlasanLuarTab from "./PendaftaranIndukMenugguUlasanLuarTab";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";

type Props = {
  title?: string;
};

interface KeputusanBoxesProps {
  data: {
    name: string;
    number: number;
  };
  isActive: boolean;
  onClick: () => void;
}

const KeputusanBoxes: React.FC<KeputusanBoxesProps> = React.memo(
  ({ data, isActive, onClick }) => {
    return (
      <Box
        onClick={onClick}
        sx={{
          position: "relative",
          padding: "12px 20px 20px 5px",
          border: isActive ? "1px solid #00B69B" : "1px solid #00B69B",
          borderRadius: "8px",
          backgroundColor: isActive ? "var(--primary-color)" : "transparent",
          color: isActive ? "#fff" : "var(--primary-color)",
          textAlign: "center",
          height: "80px",
          cursor: "pointer",
          transition: "all 0.3s",
          "&:hover": {
            backgroundColor: isActive ? "var(--primary-color)" : "#fff",
          },
        }}
      >
        <Box
          sx={{
            color: isActive ? "#fff" : "var(--primary-color)",
            fontWeight: 400,
            fontSize: "14px",
          }}
        >
          {data.name}
        </Box>
        <Typography
          sx={{
            position: "absolute",
            bottom: 2,
            right: "10px",
            color: isActive ? "#fff" : "var(--primary-color)",
            fontWeight: 500,
          }}
        >
          {data.number}
        </Typography>
      </Box>
    );
  }
);

const KeputusanInduk = ({ title }: Props) => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  //PERMISSION FOR KEPUTUSAN PERTUBUHAN MAIN TAB
  const hasKaunterPermission = AuthHelper.hasPageAccess(
    PermissionNames.KELULUSAN.label,
    pageAccessEnum.Read
  );

  useEffect(() => {
    if (!hasKaunterPermission) {
      navigate("/pengurus-pertubuhan/maklumat-pertubuhan");
    }
  }, [hasKaunterPermission, navigate]);

  const { data: pendingSociety, isLoading } = useCustom<any>({
    url: `${API_URL}/society/roDecision/getAllPendingCount/society`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const [searchParams, setSearchParams] = useSearchParams();
  const defaultTab = searchParams.get("tab") || "pendaftaran-pertubuhan-induk";
  const [activeTab, setActiveTab] = useState(defaultTab);

  const setActiveTabContent = (slug: string) => {
    setActiveTab(slug);
    setSearchParams({ tab: slug });
  };

  const tab = [
    {
      name: "Pendaftaran pertubuhan Induk",
      slug: "pendaftaran-pertubuhan-induk",
      number: pendingSociety?.data?.data?.societyRegistrationPendingCount || 0,
    },
    {
      name: "Pendaftaran Induk-menunggu ulasan luar",
      slug: "pendaftaran-induk-menuggu-ulasan-luar",
      number:
        pendingSociety?.data?.data?.societyExternalAgencyReviewPendingCount ||
        0,
    },
    {
      name: "Pindaan Perlembagaan",
      slug: "pindaan-Perlembagaan",
      number: pendingSociety?.data?.data?.societyAmendmentPendingCount || 0,
    },
    {
      name: "Pembubaran",
      slug: "pembubaran",
      number: pendingSociety?.data?.data?.societyLiquidationPendingCount || 0,
    },
    {
      name: "Permohonan bukan Warganegara",
      slug: "permohonan-bukan-warganegara",
      number: pendingSociety?.data?.data?.societyNonCitizenPendingCount || 0,
    },
    {
      name: "Pembaharuan Setiausaha",
      slug: "pembaharuan-setiausaha",
      number:
        pendingSociety?.data?.data?.societyPrincipalSecretaryPendingCount || 0,
    },
    {
      name: "Pembaharuan Setiausaha-Migrasi",
      slug: "pembaharuan-setiausaha-migrasi",
      number: 67,
    },
    {
      name: "Pegawai Awam",
      slug: "pegawai-awam",
      number: pendingSociety?.data?.data?.societyPublicOfficerPendingCount || 0,
    },
    {
      name: "Pegawai Harta",
      slug: "pegawai-harta",
      number:
        pendingSociety?.data?.data?.societyPropertyOfficerPendingCount || 0,
    },
  ];

  const renderTab = () => {
    switch (activeTab) {
      case "pembubaran":
        return (
          <PembubaranTab
            number={
              pendingSociety?.data?.data?.societyLiquidationPendingCount || 0
            }
          />
        );
      case "pendaftaran-pertubuhan-induk":
        return (
          <KeputusanIndukKelulusan
            number={
              pendingSociety?.data?.data?.societyRegistrationPendingCount || 0
            }
          />
        );
      case "pendaftaran-induk-menuggu-ulasan-luar":
        return <PendaftaranIndukMenugguUlasanLuarTab />;
      case "permohonan-bukan-warganegara":
        return (
          <PermohonanBukanWargaNegaraTab
            number={
              pendingSociety?.data?.data?.societyNonCitizenPendingCount || 0
            }
          />
        );
      case "pindaan-Perlembagaan":
        return (
          <PindaanUndangUndangIndukTab
            number={
              pendingSociety?.data?.data?.societyAmendmentPendingCount || 0
            }
          />
        );
      case "pegawai-awam":
        return (
          <PegawaiAwamTab
            number={
              pendingSociety?.data?.data?.societyPublicOfficerPendingCount
            }
          />
        );
      case "pegawai-harta":
        return (
          <PegawaiHartaTab
            number={
              pendingSociety?.data?.data?.societyPropertyOfficerPendingCount
            }
          />
        );
      case "pembaharuan-setiausaha":
        return (
          <PembaharuanSetiausahaTab
            number={
              pendingSociety?.data?.data
                ?.societyPrincipalSecretaryPendingCount || 0
            }
          />
        );
      case "pembaharuan-setiausaha-migrasi":
        return <PembaharuanSetiausahaMigrasiTab />;
      default:
        return null;
    }
  };

  if (hasKaunterPermission) {
    return (
      <>
        <Box>
          <WrapContent title="Keputusan Induk">
            <Box
              sx={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
                gap: "16px",
              }}
            >
              {tab.map((data, index) => {
                return (
                  <KeputusanBoxes
                    key={index}
                    data={data}
                    isActive={data.slug === activeTab}
                    onClick={() => setActiveTabContent(data.slug)}
                  />
                );
              })}
            </Box>
          </WrapContent>

          {renderTab()}
        </Box>
      </>
    );
  }
};

export default KeputusanInduk;
