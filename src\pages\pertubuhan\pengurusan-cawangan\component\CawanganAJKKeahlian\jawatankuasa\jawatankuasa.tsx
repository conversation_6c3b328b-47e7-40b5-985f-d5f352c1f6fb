import { useEffect, useState } from "react";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, IconButton } from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import { EyeIcon } from "@/components/icons";
import { OrganisationPositions } from "@/helpers/enums";
import { useQuery } from "@/helpers";
import { Ajk } from "../../CawanganPenyataTahunan/interface";
import { useDispatch } from "react-redux";
import {
  resetJawatankuasaAJKFormValues,
  setJawatankuasaAJKFormValues,
} from "@/redux/slices/jawatankuasaAJKFormSlice";
import { DataTable, IColumn } from "@/components";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";

interface BranchCommitteeBySocietyIdResponseBodyGet {
  id: number;
  email: string;
  designationCode: number;
  name: string;
  stateCode: number;
  residentialStateCode: string;
}

const JawatankuasaCawangan = <
  BranchCommitteeType extends BranchCommitteeBySocietyIdResponseBodyGet = BranchCommitteeBySocietyIdResponseBodyGet
>() => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id: societyId } = useParams();
  const location = useLocation();
  // const branchId = location.state?.branchId ?? null;
  const disabled = location.state?.disabled ?? false;

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const [pageSize, setPageSize] = useState(5);
  const [page, setPage] = useState(1);

  const {
    branchId,
    isBlackListed,
    isAuthorized,
    isAliranModuleAccess,
    fetchAddressList,
    addressList,
    branchData,
    // fetchAliranTugasAccess,
    // isAliranModuleStatus,
    // fetchAliranTugasStatus,
  } = useBranchContext();
  useEffect(() => {
    fetchAjkList();
    fetchAddressList();
  }, []);

  const dispatch = useDispatch();

  const {
    data: ajks,
    isLoading: isLoadingAJKs,
    refetch: fetchAjkList,
  } = useQuery({
    url: `society/branch/committee/listAjk`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "status", operator: "eq", value: branchData?.status || "008" },
      { field: "branchId", operator: "eq", value: branchId },
      { field: "pageNo", operator: "eq", value: page },
      { field: "pageSize", operator: "eq", value: pageSize },
    ],
    autoFetch: true,
  });

  const handleViewAjk = (ajk: Ajk) => {
    navigate(`../jawatankuasa/create-ajk`, {
      state: {
        ajk: ajk,
        view: true,
        branchId,
      },
    });
  };

  useEffect(() => {
    dispatch(
      setJawatankuasaAJKFormValues({
        type: null,
        savedMeetingDate: null,
        savedMeetingDetail: null,
        appointmentDate: null,
        uploadedIds: [],
        citizenAJKs: [],
      })
    );
  }, []);

  const isAccessible = !isBlackListed && (isAuthorized || isAliranModuleAccess);

  const columns: IColumn[] = [
    {
      field: "designationCode",
      headerName: t("position"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {t(
              OrganisationPositions.find(
                (item) => item.value === Number(row.designationCode)
              )?.label || "-"
            )}
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "committeeName",
      headerName: t("name"),
      align: "center",
      flex: 1,
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "email",
      headerName: t("email"),
      align: "center",
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "phoneNumber",
      headerName: t("phoneNumber"),
      align: "center",
      cellClassName: "custom-cell",
    },
    {
      field: "residentialStateCode",
      headerName: t("state"),
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <Box>
            {
              addressList.find(
                (item: any) => item.id.toString() === row.residentialStateCode
              )?.name
            }
          </Box>
        );
      },
      cellClassName: "custom-cell",
    },
    {
      flex: 1,
      field: "actions",
      headerName: "",
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return (
          <>
            <IconButton onClick={() => handleViewAjk(row)}>
              <EyeIcon />
            </IconButton>
          </>
        );
      },
    },
  ];

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            textAlign: "center",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {t("bilanganAhliJawatankuasaTerkini")}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {ajks?.data?.data?.total} Orang
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("ajkList")}
          </Typography>

          <DataTable
            columns={columns}
            rows={ajks?.data?.data?.data || []}
            page={page}
            rowsPerPage={pageSize}
            isLoading={isLoadingAJKs}
            totalCount={ajks?.data?.data?.total}
            onPageChange={(newPage: number) => setPage(newPage)}
            onPageSizeChange={(newRowsPerPage: number) =>
              setPageSize(newRowsPerPage)
            }
          />
        </Box>
      </Box>
      {isAccessible ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("update")} {t("ahliJawatanKuasa")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("update")} {t("ahliJawatanKuasa")}
                </Typography>

                <ButtonPrimary
                  disabled={disabled}
                  onClick={() => {
                    dispatch(resetJawatankuasaAJKFormValues());
                    navigate(`update-ajk`);
                  }}
                >
                  {t("update")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>
        </>
      ) : (
        <></>
      )}
    </>
    // :
    //  <></>
    // }
    // </>
  );
};

export default JawatankuasaCawangan;
