import { API_URL } from "@/api";
import { IApiResponse } from "@/types/api";

// Report Embed URL Request Interface
export interface IRegisteredUserEmbedUrlRequest {
  dashboardId: string;
}

// Report Embed URL Response Interface
export interface IRegisteredUserEmbedUrlResponse {
  embedUrl: string;
  requestId?: string;
  sessionTimeoutInMinutes?: number;
  dashboardId?: string;
  expiresAt?: string;
}

// Dashboard Item Interface
export interface IDashboardItem {
  id: number;
  dashboardId: string;
  code: string;
  name: string;
  type: string;
  module: string;
  category: string | null;
  permission: string | null;
  orderSequence: number;
  createdDate: string;
  createdBy: number;
  modifiedDate: string | null;
  modifiedBy: number | null;
}

// Dashboard Query Parameters Interface
export interface IDashboardQueryParams {
  type?: string;
  module?: string;
  category?: string;
}

// Report Service State Interface
interface ReportServiceState {
  data: any;
  loading: boolean;
  error: string | null;
}

class ReportService {
  private state: ReportServiceState = {
    data: null,
    loading: false,
    error: null,
  };

  private baseEndpoint = `${API_URL}/society/report`;

  // State getters
  getData = () => this.state.data;
  getLoading = () => this.state.loading;
  getError = () => this.state.error;

  private setState = (newState: Partial<ReportServiceState>) => {
    this.state = { ...this.state, ...newState };
  };

  // Get authentication headers
  private getAuthHeaders = () => ({
    portal: localStorage.getItem("portal") || "",
    authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
    "Content-Type": "application/json",
  });

  /**
   * Get registered user embed URL
   * @param request - Request parameters for generating embed URL
   * @returns Promise<IRegisteredUserEmbedUrlResponse>
   */
  getRegisteredUserEmbedUrl = async (
    request: IRegisteredUserEmbedUrlRequest
  ): Promise<IApiResponse<IRegisteredUserEmbedUrlResponse>> => {
    this.setState({ loading: true, error: null });

    try {
      const response = await fetch(`${this.baseEndpoint}/registeredUserEmbedUrl`, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(request),
      });

      const result: IApiResponse<IRegisteredUserEmbedUrlResponse> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to get registered user embed URL");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to get registered user embed URL";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  /**
   * Get dashboards with query parameters
   * @param params - Query parameters for filtering dashboards
   * @returns Promise<IDashboardItem[]>
   */
  getDashboards = async (
    params: IDashboardQueryParams
  ): Promise<IApiResponse<IDashboardItem[]>> => {
    this.setState({ loading: true, error: null });

    try {
      // Build query string from parameters
      const queryParams = new URLSearchParams();
      if (params.type) queryParams.append('type', params.type);
      if (params.module) queryParams.append('module', params.module);
      if (params.category) queryParams.append('category', params.category);

      const queryString = queryParams.toString();
      const url = `${this.baseEndpoint}/dashboards${queryString ? `?${queryString}` : ''}`;

      const response = await fetch(url, {
        method: "GET",
        headers: this.getAuthHeaders(),
      });

      const result: IApiResponse<IDashboardItem[]> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to get dashboards");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to get dashboards";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  /**
   * Reset service state
   */
  resetState = () => {
    this.setState({
      data: null,
      loading: false,
      error: null,
    });
  };
}

// Export singleton instance
export const reportService = new ReportService();
export default reportService;
