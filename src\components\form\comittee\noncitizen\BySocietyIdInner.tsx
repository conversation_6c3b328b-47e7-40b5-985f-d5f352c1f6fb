import { ButtonOutline, ButtonPrimary, Label } from "@/components";
import { InputControllerFormik } from "@/components/input";
import FileUploader from "@/components/input/fileUpload";
import { DocumentUploadType, useQuery } from "@/helpers";
import {
  CitizenshipStatus,
  DurationOptions,
  GenderType,
  IdTypes,
  OrganisationPositions,
} from "@/helpers/enums";
import { useICValidation } from "@/helpers/hooks/useICValidation.ts";
import {
  autoGenderSetByIC,
  capitalizeWords,
  getAddressList,
} from "@/helpers/utils";
import { useFormCommitteeNonCitizenBySocietyIdContext } from "@/pages/pertubuhan/ajk/jawatankuasa/createAJKBukanWn";
import { usejawatankuasaContext } from "@/pages/pertubuhan/ajk/jawatankuasa/jawatankuasaProvider";
import { Box, FormHelperText, Grid, Typography } from "@mui/material";
import { useBack, useNotification } from "@refinedev/core";
import { Form, useFormikContext } from "formik";
import { useCallback, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useLocation, useParams } from "react-router-dom";

export interface CommitteeNonCitizenBySocietyRequestBody {
  id: number | null;
  societyNo: string;
  societyName: string;
  name: string;
  citizenshipStatus: number;
  identificationType: string;
  identificationNo: number;
  applicantCountryCode: number;
  gender: string;
  residentialAddress: string;
  residentialCity?: string | null;
  visaNo?: string | null;
  visaExpirationDate: Date | null | string;
  permitNo?: string | null;
  permitExpirationDate: Date | null | string;
  tujuanDMalaysia: string;
  stayDurationDigit: string;
  stayDurationUnit: string;
  designationCode: number;
  activeCommitteeId: string;
  otherDesignationCode: string;
}

export interface FormCommitteeNonCitizenBySocietyIdInnerProps<
  RequestBody extends CommitteeNonCitizenBySocietyRequestBody = CommitteeNonCitizenBySocietyRequestBody
> {
  initialValue: RequestBody;
  createdId: string | null;
}

export const FormCommitteeNonCitizenBySocietyIdInner = <
  RequestBody extends CommitteeNonCitizenBySocietyRequestBody = CommitteeNonCitizenBySocietyRequestBody,
  PropType extends FormCommitteeNonCitizenBySocietyIdInnerProps<RequestBody> = FormCommitteeNonCitizenBySocietyIdInnerProps<RequestBody>
>({
  initialValue,
  createdId,
}: PropType) => {
  const { t } = useTranslation();
  const location = useLocation();
  const { open } = useNotification();
  const {
    values,
    setFieldValue,
    resetForm,
    isValid,
    isSubmitting: isSubmittingFormik,
  } = useFormikContext<RequestBody>();
  const {
    addressList: addressListFromNetwork,
    appointmentDateG,
    savedMeetingDate,
  } = usejawatankuasaContext();
  const [nameHelperText, setNameHelperText] = useState<string | undefined>(
    undefined
  );
  const [uploadedFiles, setUploadedFiles] = useState<any[]>([]);
  const [updateDoc, setUpdateDoc] = useState<boolean>(false);
  const [idNoHelperText, setIdNoHelperText] = useState<string | undefined>(
    undefined
  );
  const [secondOldFiles, setSecondOldFiles] = useState<any>(null);
  const [callCount, setCallCount] = useState(0);
  const back = useBack();
  const { id } = useParams();
  const {
    societyNonCitizenCommitteeId,
    isUploadingFilesAfterCreateAJK,
    setIsUploadingFilesAfterCreateAJK,
    redirectToAJKLists,
  } = useFormCommitteeNonCitizenBySocietyIdContext();
  const [ajkId, setAjkId] = useState<string | undefined>(undefined);
  const isSubmitting =
    isSubmittingFormik || (!initialValue?.id && isUploadingFilesAfterCreateAJK);
  const addressListFallback = getAddressList();
  const addressList =
    addressListFromNetwork.length > 0
      ? addressListFromNetwork
      : addressListFallback;
  const [oldFiles, setOldFiles] = useState<any[]>([]);
  const [positionList, setPositionList] = useState<
    { value: number; label: string; designationCode?: string }[]
  >([]);
  const [JPNError, setJPNError] = useState(false);
  const {
    userICCorrect,
    userNameMatchIC,
    triggerICValidation,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    integrationStatus,
  } = useICValidation({
    idNumber: values.identificationNo,
    idType: values.identificationType,
    fullName: values.name,
  });

  useEffect(() => {
    const isMyKad =
      Number(values.identificationType) === 1 ||
      Number(values.identificationType) === 4;
    const nameReady = values.name.trim() !== "";
    const idReady = values.identificationNo.toString().length === 12;
    if (integrationStatus === 0 && isMyKad) {
      setJPNError(true);
    } else {
      setJPNError(false);
    }
    if (integrationStatus === 1 && isMyKad && nameReady && idReady) {
      triggerICValidation();
    }
  }, [
    values.identificationType,
    values.identificationNo,
    values.name,
    integrationStatus,
  ]);

  const viewMode: boolean = location.state?.view ?? false;
  const isInputDisabled = viewMode || isSubmitting;

  useEffect(() => {
    if (values.identificationType === "4") {
      setIdNoHelperText(
        values.identificationNo.toString().length === 12 && !userICCorrect
          ? t("IcDoesNotExist")
          : undefined
      );
      setNameHelperText(
        values.identificationNo.toString()?.length === 12 &&
          values.name?.trim() !== undefined &&
          !userNameMatchIC
          ? t("invalidName")
          : undefined
      );
    }
  }, [
    values.identificationType,
    values.identificationNo,
    values.name,
    userICCorrect,
    userNameMatchIC,
  ]);

  const { isLoading: isLoadingPositionListRes } = useQuery({
    url: "society/nonCitizenCommittee/getPositionsList",
    filters: [
      {
        field: "societyId",
        value: parseInt(id ? id : ""),
        operator: "eq",
      },
      {
        field: "appointedDate",
        value: savedMeetingDate ? savedMeetingDate : appointmentDateG,
        operator: "eq",
      },
    ],
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        const newList = data?.data?.data?.map((item: any) => {
          const position =
            OrganisationPositions?.find(
              (p) => p.value === Number(item.designationCode)
            ) ?? null;
          const label = position?.label
            ? `${t(position.label)}${
                item.positionHolder ? ` - ${item.positionHolder}` : ""
              }`
            : item.designationCode;

          return {
            label,
            value: Number(item?.activeCommitteeId),
            designationCode: item.designationCode,
          };
        });
        setPositionList(newList);
      }
    },
  });

  useEffect(() => {
    if (Number(values?.identificationType) === 4) {
      setFieldValue(
        "gender",
        autoGenderSetByIC(
          Number(values?.identificationType),
          initialValue?.gender,
          values?.identificationNo?.toString()
        )
      );
    }
  }, [values?.identificationNo]);

  useEffect(() => {
    if (createdId) {
      console.log("societyNonCitizenCommitteeId", societyNonCitizenCommitteeId);
      setAjkId(createdId);
    }
  }, [createdId]);

  const handleOldUploadedFiles = useCallback(
    (files: any) => {
      setCallCount((prevCount) => {
        const newCount = prevCount + 1;
        console.log("newCount", newCount);

        if (newCount < 3 && files.length > 0) {
          console.log("recorded", files, "in secondOldFiles");
          setSecondOldFiles(files); // save the second set only
        }

        if (newCount > 1 && secondOldFiles) {
          console.log("secondOldFiles", secondOldFiles);
          console.log("files", files);
          const isEqual =
            JSON.stringify(files) === JSON.stringify(secondOldFiles);

          console.log("isEqual", isEqual);
          setUpdateDoc(!isEqual);
        }
        return newCount;
      });
      setOldFiles(files);
    },
    [secondOldFiles]
  );

  const handleFilesChanged = useCallback((files: any[]) => {
    // defer to next tick to avoid setting state during render
    setTimeout(() => {
      setUploadedFiles(files);
    }, 0);
  }, []);

  return (
    <Form style={{ display: "grid", gap: 16 }}>
      <Box
        sx={{
          px: 2,
          py: 1,
          mb: 3,
          borderRadius: "14px",
        }}
      >
        <Typography
          variant="h6"
          component="h2"
          sx={{
            fontWeight: "bold",
            fontSize: 14,
            color: "var(--primary-color)",
          }}
        >
          {t("nonCitizenAJKInfo")}
        </Typography>
        {JPNError ? (
          <Box sx={{ mt: 2 }}>
            <FormHelperText sx={{ color: "var(--error)" }}>
              {t("JPNError")}
            </FormHelperText>
          </Box>
        ) : null}
      </Box>

      <Box sx={{ pl: 2 }}>
        <InputControllerFormik
          name="societyNo"
          label={t("organizationNumber")}
          disabled
        />
        <InputControllerFormik
          name="societyName"
          label={t("organizationName")}
          disabled
        />
        <InputControllerFormik
          name="name"
          label={t("name")}
          disabled={isInputDisabled || JPNError}
          helperText={nameHelperText}
          required
        />
        <InputControllerFormik
          name="citizenshipStatus"
          label={t("citizenship")}
          disabled
          type="select"
          options={CitizenshipStatus.map((item) => ({
            ...item,
            label: t(item.label),
          }))}
        />
        <InputControllerFormik
          name="identificationType"
          required
          label={t("idType")}
          disabled={isInputDisabled}
          type="select"
          trimHelperText={false}
          options={IdTypes.filter(
            (item) => Number(item.value) === 4 || Number(item.value) === 5
          ).map((item) => ({
            ...item,
            label: t(item.label),
          }))}
          onChange={(e) => {
            if (e?.target?.value === "4") {
              setFieldValue("identificationNo", "");
            } else {
              resetICValidation();
              setIdNoHelperText(undefined);
              setNameHelperText(undefined);
            }
          }}
        />
        <InputControllerFormik
          name="identificationNo"
          required
          disabled={isInputDisabled || JPNError}
          label={t("idNumber")}
          {...(values.identificationType === "4" && {
            digitsLimit: 12,
            inputProps: {
              inputMode: "numeric",
              pattern: "[0-9]*",
              maxLength: 12,
              minLength: 12,
            },
          })}
          helperText={idNoHelperText}
        />
        <InputControllerFormik
          name="applicantCountryCode"
          required
          disabled={isInputDisabled}
          label={capitalizeWords(t("originCountry"))}
          type="select"
          trimHelperText={false}
          options={
            addressList
              ?.filter((item) => typeof item.pid === "number" && item.pid === 0)
              ?.map((item) => ({
                label: capitalizeWords(item.name, null, true),
                value: parseInt(item.id as unknown as string),
              })) ?? []
          }
        />
        <InputControllerFormik
          name="gender"
          required
          disabled={isInputDisabled}
          label={t("gender")}
          type="select"
          trimHelperText={false}
          options={GenderType.map((item) => ({
            label: t(item.translateKey),
            value: item.code,
          }))}
        />
        <InputControllerFormik
          name="residentialAddress"
          required
          disabled={isInputDisabled}
          label={t("residentialAddress")}
        />
        <InputControllerFormik
          name="residentialCity"
          disabled={isInputDisabled}
          label={t("city")}
        />
        <InputControllerFormik
          name="visaNo"
          disabled={isInputDisabled}
          label={t("nomborVisa")}
        />
        <InputControllerFormik
          name="visaExpirationDate"
          plainDate={true}
          disabled={isInputDisabled}
          type="date"
          label={t("visaExpiryDate")}
          // dateInputProps={{ disablePast: true }}
        />
        <InputControllerFormik
          name="permitNo"
          disabled={isInputDisabled}
          label={t("permitNumber")}
        />
        <InputControllerFormik
          name="permitExpirationDate"
          plainDate={true}
          disabled={isInputDisabled}
          type="date"
          label={t("permitExpiryDate")}
          // dateInputProps={{ disablePast: true }}
        />
        <InputControllerFormik
          name="tujuanDMalaysia"
          required
          disabled={isInputDisabled}
          label={capitalizeWords(t("purposeInMalaysia"))}
        />
        <Grid
          container
          spacing={2}
          alignItems={"flex-start"}
          sx={{ alignItems: "flex-start" }}
        >
          <Grid item xs={12} sm={4} flexDirection="column" display="flex">
            <Label
              text={capitalizeWords(t("durationInMalaysia"))}
              required={true}
            />
          </Grid>
          <Grid
            item
            xs={12}
            sm={8}
            sx={{
              display: "flex",
              gap: 1,
              alignItems: "flex-start",
              mb: 1,
            }}
          >
            <InputControllerFormik
              name="stayDurationDigit"
              disabled={isInputDisabled}
              required
              isLabelNoSpace={false}
              isLabel={false}
              type="text"
              inputMode="numeric"
              onlyAcceptNumber={true}
            />
            <InputControllerFormik
              name="stayDurationUnit"
              disabled={isInputDisabled}
              required
              isLabelNoSpace={false}
              type="select"
              isLabel={false}
              trimHelperText={false}
              options={DurationOptions.map((position) => ({
                ...position,
                label: t(position.label),
              }))}
            />
          </Grid>
        </Grid>
        <InputControllerFormik
          name="activeCommitteeId"
          disabled={isInputDisabled}
          required
          type="select"
          label={t("position")}
          isLoadingData={isLoadingPositionListRes}
          options={positionList}
          onChange={(event) => {
            const { value } = event.target;
            const selectedOption =
              positionList?.find((opt) => opt.value === value) ?? null;
            if (selectedOption?.designationCode) {
              setFieldValue("designationCode", selectedOption.designationCode);
            }
            setFieldValue("activeCommitteeId", value);
          }}
        />
        <InputControllerFormik
          name="otherDesignationCode"
          required
          disabled={isInputDisabled}
          multiline
          rows={4}
          label={t("importanceOfPosition2")}
        />
      </Box>
      <FileUploader
        required
        title="ajkEligibilityCheck"
        type={DocumentUploadType.NON_CITIZEN_COMMITTEE}
        uploadAfterSubmitIndicator={ajkId}
        uploadAfterSubmit={initialValue?.id ? false : true}
        validTypes={[
          "application/pdf",
          "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
          "application/msword",
          "text/plain",
        ]}
        maxFileSize={25 * 1024 * 1024}
        disabled={viewMode}
        societyId={
          ajkId || initialValue?.id ? (id ? Number(id) : undefined) : undefined
        }
        societyNonCitizenCommitteeId={
          ajkId
            ? ajkId
            : initialValue?.id
            ? initialValue?.id?.toString()
            : undefined
        }
        showSuccessUploadNotification={false}
        icNo={values.identificationNo}
        hasOldFiles={handleOldUploadedFiles}
        onUploadedFilesChanged={handleFilesChanged}
        onDeleteFile={() => setUpdateDoc(true)}
        onUploadComplete={() => setUpdateDoc(true)}
        onUploadAfterSubmitSuccessfully={() => {
          open?.({
            message: t("nonCitizenCommitteeSuccessfullyCreated"),
            type: "success",
          });
        }}
      />
      <Box
        sx={{
          display: "flex",
          justifyContent: "flex-end",
          mt: 2,
          gap: 2,
        }}
      >
        <ButtonOutline onClick={back}>{t("back")}</ButtonOutline>
        {!viewMode ? (
          <>
            <ButtonPrimary
              disabled={
                (uploadedFiles.length === 0 && oldFiles.length === 0) ||
                (initialValue?.id && updateDoc ? false : !isValid) ||
                isSubmitting ||
                (values.identificationType === "4" &&
                  (JPNError ||
                    values.identificationNo?.toString().length < 12 ||
                    !userICCorrect ||
                    !userNameMatchIC))
              }
              type="submit"
            >
              {t("update")}
            </ButtonPrimary>
          </>
        ) : null}
      </Box>
    </Form>
  );
};
