import {
  Box,
  Divider,
  Grid,
  IconButton,
  InputBase,
  Menu,
  MenuItem,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import SearchIcon from "@mui/icons-material/Search";
import TuneIcon from "@mui/icons-material/Tune";
import AddIcon from "@mui/icons-material/Add";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import {
  ApplicationStatusEnum,
  ApplicationStatusList,
  HideOrDisplayInherit,
  JenisJuruaudit,
  JenisJuruauditType,
} from "../../../../helpers/enums";
import { usejawatankuasaContext } from "../jawatankuasa/jawatankuasaProvider";
import { EditIcon, EyeIcon, TrashIcon } from "../../../../components/icons";
import ConfirmationDialog from "../../../../components/dialog/confirm";
import { useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { Auditor } from "../../pernyata-tahunan/interface";
import useQuery from "../../../../helpers/hooks/useQuery";
import { useSelector } from "react-redux";
import { getAliranTugasAccess, getUserPermission } from "@/redux/userReducer";
import NewAlertDialog from "@/components/dialog/newAlert";
import { DataTable, IColumn } from "@/components";
import { FieldValues, useForm } from "react-hook-form";

const JuruAudit: React.FC = () => {
  const { t } = useTranslation();

  const { id: societyId } = useParams();
  const [isBlackListed, setIsBlackListed] = useState(false);
  const navigate = useNavigate();
  const { auditorList, fetchAuditorList, fetchAuditorIsLoading } =
    usejawatankuasaContext();

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const { refetch: fetchSociety, isLoading: fetchSocietyIsLoading } = useQuery({
    url: `society/${societyId}`,
    onSuccess: (data) => {
      setIsBlackListed(data?.data?.data?.subStatusCode === "003" || false);
    },
  });

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  useEffect(() => {
    fetchAuditorList();
    setShouldFetch(false);
  }, [shouldFetch]);

  const [openConfirm, setOpenConfirm] = useState<boolean>(false);

  const handleDaftarJuruaudit = () => {
    navigate("create");
  };

  const handleEditJuruaudit = (auditorId: number) => {
    navigate("create", {
      state: {
        auditorId: auditorId,
      },
    });
  };

  const handlViewJuruaudit = (auditorId: number) => {
    navigate("view", {
      state: {
        auditorId: auditorId,
        view: true,
      },
    });
  };

  const { mutate: deleteJuruaudit, isLoading: isDeleting } =
    useCustomMutation();

  const confirmDeleteJuruaudit = () => {
    deleteJuruaudit(
      {
        url: `${API_URL}/society/statement/auditor/${auditorId}/delete`,
        method: "put",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          setOpenConfirm(false);
          setDialogAlertSaveOpen(true);
          setShouldFetch(true);
        },
      }
    );
  };

  const [auditorId, setAuditorId] = useState<number>();

  const handleDeleteJuruaudit = (auditorId: number) => {
    setAuditorId(auditorId);
    setOpenConfirm(true);
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const isManager = useSelector(getUserPermission);
  const isAliranTugasAccess = useSelector(getAliranTugasAccess);
  const isAccessible = !isBlackListed && (isManager || isAliranTugasAccess);

  const [dialogSaveOpen, setDialogSaveOpen] = useState(false);
  const [dialogAlertSaveOpen, setDialogAlertSaveOpen] = useState(false);

  const closeSuccess = () => {
    setDialogAlertSaveOpen(false);
  };

  const columns: IColumn[] = [
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>{row?.name}</Box>
        );
      },
    },
    {
      field: "auditorType",
      headerName: t("auditorType"),
      flex: 1,
      renderCell: ({ row }: any) => {
        const auditor = row?.auditorType as keyof JenisJuruauditType;
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {JenisJuruaudit[auditor]}
          </Box>
        );
      },
    },
    {
      field: "mykad/lesen",
      headerName: t("mykad/lesen"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.licenseNo}
          </Box>
        );
      },
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.email}
          </Box>
        );
      },
    },
    {
      field: "tarikhLantik",
      headerName: t("tarikhLantik"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row.appointmentDate
              .map((part: any, index: number) =>
                index > 0 ? part.toString().padStart(2, "0") : part
              ) // Pad month and day to 2 digits
              .join("-")}
          </Box>
        );
      },
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {t(
              ApplicationStatusList.find((item) => item.id === row.status)
                ?.value || "-"
            )}
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      align: "center",
      flex: 1,
      renderCell: (params: any) => {
        const row = params.row;
        return (
          <Box sx={{ display: "flex", justifyContent: "center" }}>
            {row.status === "001" ? (
              isAccessible ? (
                <>
                  <IconButton onClick={() => handleEditJuruaudit(row.id)}>
                    <EditIcon
                      sx={{
                        color: "var(--primary-color)",
                        width: "1rem",
                        height: "1rem",
                      }}
                    />
                  </IconButton>
                  <IconButton onClick={() => handleDeleteJuruaudit(row.id)}>
                    <TrashIcon
                      sx={{
                        color: "var(--error)",
                        width: "1rem",
                        height: "1rem",
                      }}
                    />
                  </IconButton>
                </>
              ) : (
                <></>
              )
            ) : (
              <IconButton onClick={() => handlViewJuruaudit(row.id)}>
                <EyeIcon
                  sx={{
                    color: "#666666",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            )}
          </Box>
        );
      },
    },
  ];

  //

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            sx={{
              color: "#666666",
              fontSize: 14,
              fontWeight: "400 !important",
            }}
          >
            <span style={{ color: "red", fontWeight: "bold" }}>
              {t("peringatan")} :
            </span>{" "}
            Sila pastikan bilangan Juruaudit yang berstatus aktif mengikut
            bilangan di dalam perlembagaan.
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            textAlign: "center",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {t("bilanganJuruauditTerkini")}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {auditorList.length} Orang
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("auditorList")}
          </Typography>
          <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
            {isAccessible ? (
              <ButtonOutline onClick={handleDaftarJuruaudit}>
                {t("createAuditor")}
              </ButtonOutline>
            ) : (
              <></>
            )}
          </Box>

          <DataTable
            columns={columns}
            rows={auditorList}
            page={page}
            rowsPerPage={pageSize}
            totalCount={auditorList.length || 0}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
            isLoading={fetchAuditorIsLoading}
            customNoDataText={t("noRecordForStatus")}
          />
        </Box>
      </Box>

      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDelete")}
        message={`${t("confirmDeleteAudit")}?`}
        onConfirm={confirmDeleteJuruaudit}
        onCancel={() => setOpenConfirm(false)}
        turn={true}
        buttonPrimaryLabel={t("return")}
        isMutation={isDeleting}
      />

      <NewAlertDialog
        open={dialogAlertSaveOpen}
        onClose={() => closeSuccess()}
        message={t("auditorDeletedSuccessfully")}
      />
    </>
  );
};

export default JuruAudit;
