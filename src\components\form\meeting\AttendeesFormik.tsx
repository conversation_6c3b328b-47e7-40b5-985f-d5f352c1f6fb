import {
  OrganizationManagementMeetingRequestBodyCreateOnlyAttendees,
  useOrganizationManagementMeetingRequestBodyCreateOnlyAttendeesValidationSchema,
} from "@/types";
import { Grid, useTheme } from "@mui/material";
import Box from "@mui/material/Box/Box";
import Typography from "@mui/material/Typography/Typography";
import {
  forwardRef,
  ReactNode,
  Ref,
  useImperativeHandle,
  useRef,
} from "react";
import { useTranslation } from "react-i18next";

import { FormMeetingAttendeesInnerBaseProps } from "./Attendees";
import {
  Formik,
  FormikErrors,
  useFormikContext,
} from "formik";
import { TextFieldControllerFormik } from "@/components/input";

export interface FormMeetingAttendeesFormikInnerBaseProps
  extends FormMeetingAttendeesInnerBaseProps {}

type FormMeetingAttendeesInnerBaseRef<
  RequestBody extends OrganizationManagementMeetingRequestBodyCreateOnlyAttendees = OrganizationManagementMeetingRequestBodyCreateOnlyAttendees
> = HTMLDivElement & {
  getValue: () => RequestBody;
  getErrors: () => FormikErrors<RequestBody>;
  validate: () => Promise<FormikErrors<RequestBody>>;
};

const FormMeetingAttendeesInnerBase = <
  RequestBody extends OrganizationManagementMeetingRequestBodyCreateOnlyAttendees = OrganizationManagementMeetingRequestBodyCreateOnlyAttendees,
  PropType extends FormMeetingAttendeesInnerBaseProps = FormMeetingAttendeesInnerBaseProps,
  RefType extends FormMeetingAttendeesInnerBaseRef<RequestBody> = FormMeetingAttendeesInnerBaseRef<RequestBody>
>({
  innerRef,
  viewOnly = false,
  views = ["TITLE_SECTION", "TOTAL_ATTENDEES"],
  totalAttendeesLabel: initialTotalAttendeesLabel,
}: PropType) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { values, errors, validateForm } =
    useFormikContext<RequestBody>();

  const totalAttendeesLabel =
    initialTotalAttendeesLabel ?? t("jumlahKehadiranMesyuarat");
  const sectionStyle = {
    color: theme.palette.primary.main,
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  useImperativeHandle(
    innerRef,
    () =>
      ({
        getValue: () => {
          const totalAttendees =
            typeof values.totalAttendees === "string"
              ? parseInt(values.totalAttendees)
              : values.totalAttendees;
          return {
            ...values,
            totalAttendees,
          };
        },
        getErrors: () => errors,
        validate: async () => await validateForm(),
      } as unknown as RefType),
    []
  );

  const initialResult = views.includes("TOTAL_ATTENDEES") && (
    <>
      <Grid item xs={12} sm={4}>
        <Typography sx={labelStyle}>
          {totalAttendeesLabel} <span style={{ color: "red" }}>*</span>
        </Typography>
      </Grid>
      <Grid item xs={12} sm={8}>
        <TextFieldControllerFormik
          fullWidth
          required
          name="totalAttendees"
          type="text"
          onlyAcceptNumber={true}
          disabled={viewOnly}
          helperTextComponentPlacement="INSIDE"
        />
      </Grid>
    </>
  );
  if (!views.includes("TITLE_SECTION")) {
    return initialResult;
  }
  return (
    <Box
      ref={innerRef}
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        border: "1px solid #D9D9D9",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Typography variant="subtitle1" sx={sectionStyle}>
        {t("kehadiranAhliMesyuarat")}
      </Typography>
      <Grid container spacing={2}>
        {initialResult}
      </Grid>
    </Box>
  );
};

export const FormMeetingAttendeesFormikInner =
  forwardRef<FormMeetingAttendeesInnerBaseRef, FormMeetingAttendeesInnerBaseProps>((props, ref) => (
    <FormMeetingAttendeesInnerBase innerRef={ref} {...props} />
  ));

interface FormMeetingAttendeesFormikBaseProps {
  innerRef?: Ref<HTMLFormElement>;
  defaultValues: any;
  /**
   * @default false
   */
  viewOnly?: boolean;

  views?: ("TITLE_SECTION" | "TOTAL_ATTENDEES")[];

  totalAttendeesLabel?: string;

  memberAttendanceLabel?: string;

  /**
   * @default false
   */
  withPopover?: boolean;

  /**
   * @description this props only applied if {@link withPopover} is true
   * @default null
   */
  popoverComponent?: ReactNode;
}

export type FormMeetingAttendeesFormikBaseRef<
  RequestBody extends OrganizationManagementMeetingRequestBodyCreateOnlyAttendees = OrganizationManagementMeetingRequestBodyCreateOnlyAttendees
> = HTMLFormElement & {
  getValue: () => RequestBody | null;
  getErrors: () => FormikErrors<RequestBody> | null;
  validate: () => Promise<FormikErrors<RequestBody>>;
};

const FormMeetingAttendeesFormikBase = <
  PropType extends FormMeetingAttendeesFormikBaseProps = FormMeetingAttendeesFormikBaseProps,
  RequestBody extends OrganizationManagementMeetingRequestBodyCreateOnlyAttendees = OrganizationManagementMeetingRequestBodyCreateOnlyAttendees,
  RefType extends FormMeetingAttendeesFormikBaseRef<RequestBody> = FormMeetingAttendeesFormikBaseRef<RequestBody>
>({
  innerRef,
  defaultValues,
  viewOnly = false,
  views = ["TITLE_SECTION", "TOTAL_ATTENDEES"],
  withPopover = false,
  totalAttendeesLabel,
  memberAttendanceLabel,
  popoverComponent = null,
}: PropType) => {
  const formInnerRef =
    useRef<FormMeetingAttendeesInnerBaseRef<RequestBody> | null>();
  const resolver =
    useOrganizationManagementMeetingRequestBodyCreateOnlyAttendeesValidationSchema(
      {
        withResolverWrapper: false,
      }
    );

  useImperativeHandle(
    innerRef,
    () =>
      ({
        getValue: () => formInnerRef.current?.getValue() ?? null,
        getErrors: () => formInnerRef.current?.getErrors() ?? null,
        validate: async () => await formInnerRef.current?.validate(),
      } as RefType),
    []
  );

  return (
    <Formik
      ref={innerRef}
      initialValues={defaultValues}
      onSubmit={() => {}}
      validationSchema={resolver}
    >
      <FormMeetingAttendeesFormikInner
        // @ts-expect-error
        ref={formInnerRef}
        {...{
          viewOnly,
          views,
          withPopover,
          popoverComponent,
          totalAttendeesLabel,
          memberAttendanceLabel,
        }}
      />
    </Formik>
  );
};

export const FormMeetingAttendeesFormik = forwardRef<
  FormMeetingAttendeesFormikBaseRef,
  FormMeetingAttendeesFormikBaseProps
>((props, ref) => <FormMeetingAttendeesFormikBase innerRef={ref} {...props} />);
