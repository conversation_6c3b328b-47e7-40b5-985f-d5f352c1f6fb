import { Box, CircularProgress, Icon<PERSON>utton, Typography } from "@mui/material";
import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate, useParams } from "react-router-dom";
import { ButtonOutline } from "../../../../components/button";
import {
  ApplicationStatusList,
  COMMITTEE_TASK_TYPE,
} from "../../../../helpers/enums";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import { API_URL } from "@/api";
import { useCustomMutation } from "@refinedev/core";
import ConfirmationDialog from "@/components/dialog/confirm";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import { formatDate, useQuery } from "@/helpers";
import { useDispatch } from "react-redux";
import { fetchIsManagerBySocietyId } from "@/redux/APIcalls/isManagerBySocietyThunks";
import { AppDispatch } from "@/redux/store";
import { DataTable } from "@/components";
import { FieldValues, useForm } from "react-hook-form";

const sectionStyle = {
  color: "#0CA6A6",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const MaklumatPemegangAmanah: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [isBlackListed, setIsBlackListed] = useState(false);
  const [trusteeId, setTrusteeId] = useState<number | null>(null);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [isAliranModuleAccess, setIsAliranModuleAccess] = useState(false);

  const dispatch: AppDispatch = useDispatch();
  const { id: societyId } = useParams();

  const handleDaftarPemegangAmanah = () => {
    navigate("create");
  };

  const { refetch: fetchSociety, isLoading: fetchSocietyIsLoading } = useQuery({
    url: `society/${societyId}`,
    onSuccess: (data) => {
      setIsBlackListed(data?.data?.data?.subStatusCode === "003" || false);
    },
  });

  useEffect(() => {
    if (societyId) {
      dispatch(fetchIsManagerBySocietyId({ id: societyId }));
    }
  }, [societyId]);

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
    },
  });
  const page = watch("page");
  const pageSize = watch("pageSize");

  const {
    data: trusteeList,
    isLoading,
    refetch,
  } = useQuery({
    url: `society/trustee/getAll`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
    ],
  });

  const totalList = trusteeList?.data?.data?.total ?? 0;
  const rowData = trusteeList?.data?.data?.data ?? [];

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  useEffect(() => {
    refetch();
    setShouldFetch(false);
  }, [shouldFetch]);

  const handleEditTrustee = (trusteeId: number) => {
    navigate("create", {
      state: {
        trusteeId: trusteeId,
      },
    });
  };

  const handleViewTrustee = (trusteeId: number) => {
    navigate("create", {
      state: {
        trusteeId: trusteeId,
        view: true,
      },
    });
  };

  const { mutate: deleteTrustee, isLoading: isUpdateAJK } = useCustomMutation();

  const handleConfirmDeleteTrustee = (id: number) => {
    setTrusteeId(id);
    setOpenConfirm(true);
  };

  const handleDeleteTrustee = () => {
    deleteTrustee(
      {
        url: `${API_URL}/society/trustee/${trusteeId}`,
        method: "delete",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          setShouldFetch(true);
          setOpenConfirm(false);
        },
      }
    );
  };

  const {
    refetch: fetchAliranTugasAccess,
    isLoading: fetchAliranTugasAccessIsLoading,
  } = useQuery({
    url: `society/committee-task/me`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      {
        field: "module",
        operator: "eq",
        value: COMMITTEE_TASK_TYPE.PENGURUSAN_AJK,
      },
    ],
    onSuccess: (data) => {
      const responseData = data?.data?.data?.enabled;
      setIsAliranModuleAccess(responseData);
    },
  });

  const isManager = useSelector(getUserPermission);

  const apiIsLoading = fetchAliranTugasAccessIsLoading;
  const isAccessible = !isBlackListed && (isManager || isAliranModuleAccess);

  if (apiIsLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "300px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  const columns = [
    {
      field: "name",
      headerName: t("name"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Box
            style={{
              textDecoration: "none",
            }}
          >
            {params?.row?.name ?? "-"}
          </Box>
        );
      },
    },
    {
      field: "identificationNo",
      headerName: t("idNumber"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Box
            style={{
              textDecoration: "none",
            }}
          >
            {params?.row?.identificationNo ?? "-"}
          </Box>
        );
      },
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        return (
          <Box
            style={{
              textDecoration: "none",
            }}
          >
            {params?.row?.email ?? "-"}
          </Box>
        );
      },
    },
    {
      field: "createdDate",
      headerName: t("tarikhLantik"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        return formatDate(params?.row?.appointmentDate)
          ? formatDate(params?.row?.appointmentDate)
          : "-";
      },
      cellClassName: "custom-cell",
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;

        if (row.status === 43) {
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Typography
                className="status-pertubuhan-text"
                sx={{
                  backgroundColor: "#fff",
                  border: "2px solid var(--error)",
                }}
              >
                {t("Tidak Aktif")}
              </Typography>
            </Box>
          );
        } else {
          return (
            <Box
              sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
              }}
            >
              <Typography
                className="status-pertubuhan-text"
                sx={{
                  backgroundColor: "#fff",
                  border: "2px solid var(--success)",
                }}
              >
                {t(
                  ApplicationStatusList.find((item) => item.id === row.status)
                    ?.value || "-"
                )}
              </Typography>
            </Box>
          );
        }
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        if (row.status === 11) {
          if (isAccessible) {
            return (
              <>
                <IconButton
                  color="primary"
                  onClick={() => handleEditTrustee(row.id)}
                >
                  <EditIcon />
                </IconButton>
                <IconButton
                  color="error"
                  onClick={() => handleConfirmDeleteTrustee(row.id)}
                >
                  <TrashIcon />
                </IconButton>
              </>
            );
          } else {
            return <></>;
          }
        } else {
          return (
            <IconButton onClick={() => handleViewTrustee(row.id)}>
              <EyeIcon />
            </IconButton>
          );
        }
      },
    },
  ];

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        backgroundColor: "white",
        borderRadius: "14px",
        mb: 2,
      }}
    >
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          textAlign: "center",
          p: 3,
          mb: 2,
        }}
      >
        <Typography
          variant="subtitle1"
          sx={{
            color: "#0CA6A6",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {t("bilanganPemegangAmanahTerkini")}
        </Typography>
        <Typography
          sx={{
            color: "#666666",
            fontSize: 18,
            fontWeight: "500 !important",
          }}
        >
          {totalList} Orang
        </Typography>
      </Box>
      <Box
        sx={{
          border: "1px solid rgba(0, 0, 0, 0.12)",
          borderRadius: "14px",
          p: 3,
          mb: 2,
        }}
      >
        <Typography variant="subtitle1" sx={sectionStyle}>
          {t("trusteeList")}
        </Typography>
        <Box sx={{ my: 3 }} display={"flex"} justifyContent={"flex-end"}>
          {isAccessible ? (
            <ButtonOutline onClick={handleDaftarPemegangAmanah}>
              {t("registerTrustee")}
            </ButtonOutline>
          ) : (
            <></>
          )}
        </Box>
        <DataTable
          columns={columns as any}
          rows={rowData}
          page={page}
          rowsPerPage={pageSize}
          totalCount={totalList}
          onPageChange={(newPage) => setValue("page", newPage)}
          onPageSizeChange={(newPageSize) => {
            setValue("page", 1);
            setValue("pageSize", newPageSize);
          }}
          isLoading={isLoading}
        />
      </Box>
      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDelete")}
        message={t("areYouSureDeleteTrusteeRecord")}
        onConfirm={handleDeleteTrustee}
        onCancel={() => setOpenConfirm(false)}
      />
    </Box>
  );
};

export default MaklumatPemegangAmanah;
