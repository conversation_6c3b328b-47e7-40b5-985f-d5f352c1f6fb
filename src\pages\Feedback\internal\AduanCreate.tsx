import { Formik, Formik<PERSON>el<PERSON> } from "formik";
import { useLocation } from "react-router-dom";

import { FeedbackAduanInternalCreateRequestBody, FormFeedbackAduanInternalCreate } from "@/components/form/feedback/aduan/InternalCreate";

/**
 * @description this component can handle both aduan creation and update on internal side.
 */
export const FeedbackInternalAduanCreate = <
  ReqBody extends FeedbackAduanInternalCreateRequestBody = FeedbackAduanInternalCreateRequestBody
>() => {
  const location = useLocation();

  const initialValue = {
    ...location.state?.prevData
  } as ReqBody

  const handleSubmit = async (payload: ReqBody, { setSubmitting }: FormikHelpers<ReqBody>) => {
    const { promise, resolve } = Promise.withResolvers();
    setSubmitting(true);
    setTimeout(() => {
      resolve(payload);
      setSubmitting(false);
    }, 3000);
    await promise;
    // try {
    // } finally {
    //   setSubmitting(false);
    // }
  }

  return (
    <Formik<ReqBody>
      initialValues={{ ...location.state?.prevData } as ReqBody}
      onSubmit={handleSubmit}
    >
      <FormFeedbackAduanInternalCreate initialValue={initialValue} />
    </Formik>
  )
}
