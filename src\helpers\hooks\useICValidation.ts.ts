import { useEffect, useState } from "react";
import useQuery from './useQuery';
import { useCustom } from '@refinedev/core';
import { API_URL } from '@/api';


export interface IIntegrationData {
  createdBy: number;
  createdDate: string;
  modifiedBy: number | null;
  modifiedDate: string | null;
  id: number;
  name: string;
  type: string;
  description: string;
  status: number;
}

interface Props {
  idType?: string | number;
  idNumber: string | number;
  fullName: string | null;
  userGroup?: string | number | null
}

export const useICValidation = ({
  idType,
  idNumber,
  fullName,
  userGroup,
}: Props) => {
  const [userICCorrect, setUserICCorrect] = useState(true);
  const [userNameMatchIC, setUserNameMatchIC] = useState(true);
  const [userExists, setUserExists] = useState(false);

  const {
    data: integrationData,
    isLoading: isLoadingIntegration,
    refetch
  } = useQuery<{ data: IIntegrationData[] }>({
    url: "society/admin/integration/list",
    autoFetch: false,
  });

  useEffect(() => {
    if (idType || userGroup) {
      refetch();
    }
  }, [idType, userGroup, refetch]);

  const integrationList: IIntegrationData[] = integrationData?.data?.data ?? [];

  const {
    refetch: triggerICValidation,
    isLoading: isLoadingICValidation,
  } = useCustom<any>({
    url: `${API_URL}/user/auth/validateId`,
    method: "get",
    config: {
      query: {
        identificationNo: idNumber,
        name: fullName?.trim().toUpperCase(),
        sessionIdentificationNo: idNumber,
        userGroup: userGroup ? userGroup : userGroup
      },
    },
    queryOptions: {
      enabled: false, // manual trigger only
      retry: false,
      cacheTime: 0,
    },
    successNotification(data) {
      const { name, message, status, userExist, integrationOff } =
        data?.data?.data || {};

      setUserICCorrect(false);
      setUserNameMatchIC(false);
      if (userExist === false) {
        setUserExists(false);
      } else {
        setUserExists(true);
      }
      if (!integrationOff) {
        if (status === "Y") {
          setUserICCorrect(true);
          if (name) {
            setUserNameMatchIC(true);
          }
        } else {
          setUserICCorrect(false);
          setUserNameMatchIC(true);
        }
      }

      return false;
    },
    errorNotification() {
      setUserICCorrect(false);
      setUserNameMatchIC(false);
      setUserExists(false)
      return false;
    },
  });

  const resetICValidation = () => {
    setUserICCorrect(true);
    setUserNameMatchIC(true);
    setUserExists(true)
  };

  return {
    userICCorrect,
    userNameMatchIC,
    userExists,
    isLoadingICValidation,
    isLoadingIntegration,
    integrationStatus: integrationList?.[0]?.status,
    setUserICCorrect,
    setUserNameMatchIC,
    resetICValidation,
    triggerICValidation,
  };
};
