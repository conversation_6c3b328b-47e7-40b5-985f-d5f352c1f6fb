import {
  Box,
  FormControl,
  FormHelperText,
  Grid,
  IconButton,
  MenuItem,
  Select,
  TextField,
  Typography,
} from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import { getLocalStorage, getMalaysiaAddressList } from "@/helpers/utils";
import { useNavigate } from "react-router-dom";
import {
  ApplicationStatusEnum,
  PermissionNames,
  StatusPermohonan,
  pageAccessEnum,
} from "@/helpers/enums";
import ButtonPrevious from "@/components/button/ButtonPrevious";
import { ButtonPrimary } from "@/components/button";
import { FieldValues, useForm } from "react-hook-form";
import { EditIcon, EyeIcon } from "@/components/icons";
import AuthHelper from "@/helpers/authHelper";
import { useQuery } from "@/helpers";
import DataTable, { IColumn } from "@/components/datatable";
import { <PERSON><PERSON><PERSON>ilter } from "@refinedev/core";
import { SelectFieldController, TextFieldController } from "@/components";
interface FormValues {
  userId?: number | null;
  negeri?: string | null;
  statusPertubuhan?: string | null;
  jenisPemegangJawatan?: string | null;
  carian?: string | null;
}

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "30px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

function PenyataTahunanTab() {
  const navigate = useNavigate();

  const { control, setValue, watch, getValues, reset, handleSubmit } =
    useForm<FieldValues>({
      defaultValues: {
        pageNo: 1,
        pageSize: 10,
        searchQuery: "",
        applicationStatusCode: "",
        stateCode: "",
        statementYear: "",
      },
    });

  const [total, setTotal] = useState<number>(0);
  const [statementList, setStatementList] = useState([]);
  const [years, setYears] = useState<{ value: number; label: string }[]>([]);

  const malaysiaAddressList = getMalaysiaAddressList() ?? [];
  const stateOptions = useMemo(() => {
    return malaysiaAddressList.map((item: any) => ({
      label: item.name,
      value: item.id,
    }));
  }, []);

  const getStateName = (stateCode: any) => {
    const stateName = malaysiaAddressList.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name ? stateName[0]?.name : "-";
  };

  const statusPermohonan = Object.entries(ApplicationStatusEnum)
    .filter(([key, value]) => key === "1" || key === "17" || key === "0")
    .map(([key, value]) => ({
      value: key,
      label: value,
    }));

  const handleClearSearch = () => {
    reset();
    refetchStatementList();
  };

  const handleSearch = () => {
    const filters: CrudFilter[] = [
      { field: "pageSize", operator: "eq", value: getValues("pageSize") },
      { field: "pageNo", operator: "eq", value: getValues("pageNo") },
      {
        field: "isBranch",
        value: true,
        operator: "eq",
      },
      { field: "searchQuery", operator: "eq", value: getValues("searchQuery") },
      {
        field: "statementYear",
        operator: "eq",
        value: getValues("statementYear"),
      },
      {
        field: "applicationStatusCode",
        operator: "eq",
        value: getValues("applicationStatusCode"),
      },
      { field: "stateCode", operator: "eq", value: getValues("stateCode") },
    ];
    refetchStatementList({ filters });
  };

  const columns: IColumn[] = [
    {
      field: "branchName",
      headerName: `${t("organization_name") + ":" + t("namaCawangan")}`,
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.branchName}
          </Box>
        );
      },
    },
    {
      field: "branchNo",
      headerName: t("ppmCawangan"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.branchNo ? row?.branchNo : "-"}
          </Box>
        );
      },
    },
    {
      field: "statementApplicationStatusCode",
      headerName: t("statusTahunPenyata"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {t(
              ApplicationStatusEnum[
                (row?.statementApplicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                  "0"
              ]
            )}
          </Box>
        );
      },
    },
    {
      field: "statementYear",
      headerName: t("tahunPenyata"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.statementYear}
          </Box>
        );
      },
    },
    {
      field: "submissionDate",
      headerName: t("submissionDate"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {row?.submissionDate ? row?.submissionDate : "-"}
          </Box>
        );
      },
    },
    {
      field: "negeri",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {getStateName(row?.branchStateCode)}
          </Box>
        );
      },
    },
    {
      field: "statusPertubuhan",
      headerName: t("organizationStatus"),
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return (
          <Box sx={{ fontWeight: "500", textAlign: "center" }}>
            {t(
              ApplicationStatusEnum[
                (row?.branchApplicationStatusCode as keyof typeof ApplicationStatusEnum) ||
                  "0"
              ]
            )}
          </Box>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "right",
      renderCell: ({ row }: any) => {
        return (
          <Box
            sx={{
              fontWeight: "500",
              textAlign: "center",
              display: "flex",
            }}
          >
            <IconButton
              color="primary"
              onClick={() => goDetail(row, false)}
              sx={{ width: "2rem", height: "2rem" }}
            >
              <EditIcon sx={{ width: "1rem", height: "1rem" }} />
            </IconButton>
            <IconButton
              onClick={() => goDetail(row, true)}
              sx={{ width: "2rem", height: "2rem" }}
            >
              <EyeIcon sx={{ width: "1rem", height: "1rem" }} />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const goDetail = (data: any, isView: boolean) => {
    navigate("pertubuhan/penyata-tahunan", {
      state: { data: data, isView: isView },
    });
  };

  const handleChangePage = (newPage: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: watch("pageSize"), operator: "eq" },
      { field: "pageNo", value: newPage, operator: "eq" },
      {
        field: "isBranch",
        value: true,
        operator: "eq",
      },
    ];
    setValue("pageNo", newPage);
    refetchStatementList({ filters });
  };

  const handleChangePageSize = (newPageSize: number) => {
    const filters: CrudFilter[] = [
      { field: "pageSize", value: newPageSize, operator: "eq" },
      { field: "pageNo", value: watch("pageNo"), operator: "eq" },
      {
        field: "isBranch",
        value: true,
        operator: "eq",
      },
    ];
    setValue("pageSize", newPageSize);
    refetchStatementList({ filters });
  };

  const {
    data: statementListData,
    isLoading: statementListIsLoading,
    refetch: refetchStatementList,
  } = useQuery({
    url: `society/statement/internal/getStatement`,
    filters: [
      {
        field: "pageSize",
        value: watch("pageSize"),
        operator: "eq",
      },
      {
        field: "pageNo",
        value: watch("pageNo"),
        operator: "eq",
      },
      {
        field: "isBranch",
        value: true,
        operator: "eq",
      },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const statementData = data?.data?.data?.data || [];
      const total = data?.data?.data?.total;
      setTotal(total);
      setStatementList(statementData);

      //set available year
      const allYears = [
        ...new Set(statementData.map((item: any) => item.statementYear)),
      ];
      const sortedYears = allYears.sort((a: any, b: any) => b - a);
      const formattedYears = sortedYears.map((year: any) => ({
        value: year,
        label: year.toString(),
      }));
      setYears(formattedYears);
    },
  });

  useEffect(() => {
    const filters: CrudFilter[] = [
      {
        field: "pageSize",
        value: watch("pageSize"),
        operator: "eq",
      },
      {
        field: "pageNo",
        value: watch("pageNo"),
        operator: "eq",
      },
      {
        field: "isBranch",
        value: true,
        operator: "eq",
      },
    ];
    refetchStatementList({ filters });
  }, []);

  return (
    <>
      <form onSubmit={handleSubmit(handleSearch)}>
        <Box>
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("senaraiPertubuhan")}
              </Typography>
              <Grid container spacing={2}>
                {/* state */}
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("Negeri")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="stateCode"
                    control={control}
                    options={stateOptions}
                    placeholder={t("selectPlaceholder")}
                    onChange={() => setValue("districtCode", "")}
                    requiredCondition={() => true}
                  />
                </Grid>
                {/* status permohonan */}
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>
                    {t("Status permohonan")}
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="applicationStatusCode"
                    control={control}
                    options={statusPermohonan}
                    placeholder={t("selectPlaceholder")}
                    onChange={() => setValue("districtCode", "")}
                    requiredCondition={() => true}
                  />
                </Grid>
                {/* tahun penyata */}
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("tahunPenyata")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <SelectFieldController
                    name="statementYear"
                    disabled={statementListIsLoading}
                    control={control}
                    options={years}
                    placeholder={t("selectPlaceholder")}
                    onChange={() => setValue("districtCode", "")}
                    requiredCondition={() => true}
                  />
                </Grid>
                {/* finding/carian */}
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("Carian")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextFieldController name="searchQuery" control={control} />
                </Grid>
              </Grid>
              <Grid container mt={3} spacing={2}>
                <Grid
                  item
                  xs={12}
                  sx={{
                    mt: 2,
                    display: "flex",
                    justifyContent: "flex-end",
                    gap: 1,
                  }}
                >
                  <ButtonPrevious
                    variant="outlined"
                    sx={{
                      bgcolor: "white",
                      "&:hover": { bgcolor: "white" },
                    }}
                    onClick={handleClearSearch}
                  >
                    {t("previous")}
                  </ButtonPrevious>
                  <ButtonPrimary
                    type="submit"
                    variant="contained"
                    sx={{
                      boxShadow: "none",
                    }}
                  >
                    {t("search")}
                  </ButtonPrimary>
                </Grid>
              </Grid>
            </Box>
          </Box>
          {/* ============= */}
          <Box
            sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
          >
            <Box
              sx={{
                textAlign: "center",
                color: "#fff",
                borderRadius: "13px",
                backgroundColor: "var(--primary-color)",
                py: 2,
              }}
            >
              <Typography variant="h5" gutterBottom>
                {total}
              </Typography>
              <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
                {t("rekodDijumpai")}
              </Typography>
            </Box>

            <Box
              sx={{
                p: { xs: 1, sm: 2, md: 3 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
                mt: 2,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("")}
              </Typography>
              <DataTable
                columns={columns}
                rows={statementList}
                page={watch("pageNo")}
                rowsPerPage={watch("pageSize")}
                totalCount={total}
                onPageChange={handleChangePage}
                onPageSizeChange={handleChangePageSize}
                isLoading={statementListIsLoading}
                customNoDataText={t("noRecordForStatusBranch")}
              />
            </Box>
          </Box>
        </Box>
      </form>
    </>
  );
}

export default PenyataTahunanTab;
