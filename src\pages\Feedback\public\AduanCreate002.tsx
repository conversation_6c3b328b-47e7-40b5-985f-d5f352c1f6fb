import { Formik, type <PERSON>ik<PERSON><PERSON><PERSON> } from "formik";
import { useLocation } from "react-router-dom";

import { type FeedbackAduanPublicCreateRequestBody, FormFeedbackAduanPublicCreate } from "@/components/form/feedback/aduan/PublicCreate";

export const FeedbackPublicAduanCreate002 = <
  ReqBody extends FeedbackAduanPublicCreateRequestBody = FeedbackAduanPublicCreateRequestBody
>() => {
  const location = useLocation();

  const initialValue = {
    ...location.state?.prevData
  } as ReqBody

  const handleSubmit = async (payload: ReqBody, { setSubmitting }: FormikHelpers<ReqBody>) => {
    const { promise, resolve } = Promise.withResolvers();
    setSubmitting(true);
    setTimeout(() => {
      resolve(payload);
      setSubmitting(false);
    }, 3000);
    await promise;
    // try {
    // } finally {
    //   setSubmitting(false);
    // }
  }

  return (
    <Formik<ReqBody>
      initialValues={{ ...location.state?.prevData } as ReqB<PERSON>}
      onSubmit={handleSubmit}
    >
      <FormFeedbackAduanPublicCreate initialValue={initialValue} />
    </Formik>
  )
}
