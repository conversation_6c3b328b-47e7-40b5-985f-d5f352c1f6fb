import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useOutlet } from "react-router-dom";

import { CustomTabsPanel } from "@/components";

export const PenguatkuasaanInternalAduan = () => {
  const outlet = useOutlet();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { pathname } = useLocation();

  const initialTab = pathname.endsWith("aduan-saya") ? 1 : 0;

  const tabs = [
    {
      label: t("complaintList"),
      content: outlet
    },
    {
      label: t("myComplaint"),
      content: outlet
    }
  ];

  const handleTabChange = (index: number) => {
    switch (index) {
      case 1:
        navigate("/penguatkuasaan/aduan/aduan-saya")
        break;
      case 0:
      default:
        navigate("/penguatkuasaan/aduan")
        break;
    }
  }

  return (
    <CustomTabsPanel {...{ tabs, initialTab, onTabChange: handleTabChange }} />
  );
}
