import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { Box, Grid, TextField, Typography } from "@mui/material";
import { useCustom } from "@refinedev/core";
import { API_URL } from "../../../../../api";
import { useLocation, useParams } from "react-router-dom";
import { MALAYSIA } from "../../../../../helpers/enums";
import { setAddressDataRedux } from "../../../../../redux/addressDataReducer";
import { useSelector } from "react-redux";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ile<PERSON>ayer } from "react-leaflet";
import { setBranchDataRedux } from "@/redux/branchDataReducer";
import { useDispatch } from "react-redux";
import { capitalizeWords, formatDate, useQuery } from "@/helpers";
import { setUserPermissionRedux } from "@/redux/userReducer";
import dayjs from "dayjs";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";

interface ListItem {
  value: any;
  label: any;
}

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const CawanganMaklumat: React.FC = () => {
  const { t } = useTranslation();
  const dispatch = useDispatch();

  const [districtList, setDistrictList] = useState<ListItem[]>([]);
  const [mailingDistrictList, setMailingDistrictList] = useState<ListItem[]>(
    []
  );

  const { isAuthorized, checkIsAuthorizedLoading, branchData } =
    useBranchContext();

  // @ts-ignore
  const addressDataRedux = useSelector((state) => state?.addressData?.data);

  const StateList = addressDataRedux
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({
      value: item.id,
      label: capitalizeWords(item.name, null, true),
    }));

  const { data: addressList } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const addressData = addressList?.data?.data || [];

  const formatText = (text: string) => {
    return text.charAt(0).toUpperCase() + text.slice(1).toLowerCase();
  };

  const getStatusDesc = (status: string) => {
    return status === "001"
      ? t("active")
      : status === "008"
      ? t("inactive")
      : "";
  };

  const getDatePeriod = (date: string) => {
    const today = new Date();
    const futureDate = new Date(date);
    const diffTime = futureDate.getTime() - today.getTime();
    return Math.abs(Math.ceil(diffTime / (1000 * 60 * 60 * 24)));
  };

  useEffect(() => {
    if (branchData?.stateCode) {
      setDistrictList(
        addressDataRedux
          .filter((item: any) => item.pid === Number(branchData?.stateCode))
          .map((item: any) => ({
            value: item.id,
            label: capitalizeWords(item.name, null, true),
          }))
      );
    }
    if (branchData?.mailingStateCode) {
      setMailingDistrictList(
        addressDataRedux
          .filter(
            (item: any) => item.pid === Number(branchData?.mailingStateCode)
          )
          .map((item: any) => ({
            value: item.id,
            label: capitalizeWords(item.name, null, true),
          }))
      );
    }
  }, [branchData]);

  const showNBID =
    branchData?.applicationStatusCode === 5 ||
    branchData?.applicationStatusCode === 6 ||
    branchData?.applicationStatusCode === 9;

  const submissionDate = branchData?.submissionDate
    ? dayjs(branchData?.submissionDate)
    : null;
  const expirationDate = branchData?.applicationExpirationDate
    ? dayjs(branchData?.applicationExpirationDate)
    : null;

  const daysLeft =
    submissionDate && expirationDate
      ? expirationDate.diff(submissionDate, "day")
      : "-";

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatCawangan")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("branchNameDetails")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                value={branchData?.name}
                disabled
              />
            </Grid>
            {showNBID && (
              <>
                <Grid item xs={12} sm={4}>
                  <Typography sx={labelStyle}>{t("noNBIDCawangan")}</Typography>
                </Grid>
                <Grid item xs={12} sm={8}>
                  <TextField
                    size="small"
                    sx={{ backgroundColor: "#E8E9E8" }}
                    fullWidth
                    value={
                      branchData?.branchNo
                        ? branchData?.branchNo
                        : branchData?.branchApplicationNo
                    }
                    disabled
                    InputProps={{ readOnly: true }}
                  />
                </Grid>
              </>
            )}
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("tarafPenubuhan")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                disabled
                value={getStatusDesc(branchData.status)}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {formatText(t("tarikhPemohonan"))}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={formatDate(branchData?.createdDate)}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {formatText(t("tempohMasa"))}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={daysLeft === 0 ? t("LUPUT") : `${daysLeft} ${t("day")}`}
                disabled
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("branchBusinessAddress")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("alamatTempatUrusanCawangan")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                value={branchData?.address}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("negeri")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                disabled
                value={
                  StateList.find(
                    (item: any) => item.value === Number(branchData?.stateCode)
                  )?.label || ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("daerah")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                disabled
                value={
                  districtList.find(
                    (item: any) =>
                      item.value === Number(branchData?.districtCode)
                  )?.label || ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("bandar")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={branchData?.city}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("poskod")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={branchData?.postcode}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingLocation")}
                <Typography
                  sx={{ color: "red", display: "inline" }}
                  component={"span"}
                >
                  *
                </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Box>
                <MapContainer
                  /**
                   * @todo change meetingLocation with data from backend
                   */
                  center={[2.745564, 101.707021]}
                  zoom={13}
                  style={{
                    height: "10rem",
                    width: "100%",
                    borderRadius: "0.5rem",
                  }}
                >
                  <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                  {/**
                   * @todo change meetingLocation with data from backend
                   */}
                  <Marker position={[2.745564, 101.707021]} />
                </MapContainer>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("alamatSuratMenyuratCawangan")}
          </Typography>

          <Grid container spacing={2}>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("alamatSuratMenyuratCawangan")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                value={branchData?.mailingAddress}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("negeri")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                disabled
                value={
                  StateList.find(
                    (item: any) =>
                      item.value === Number(branchData?.mailingStateCode)
                  )?.label || ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("daerah")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                required
                disabled
                value={
                  mailingDistrictList.find(
                    (item: any) =>
                      item.value === Number(branchData?.mailingDistrictCode)
                  )?.label || ""
                }
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("bandar")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={branchData?.city}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>{t("poskod")}</Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <TextField
                size="small"
                sx={{ backgroundColor: "#E8E9E8" }}
                fullWidth
                value={branchData?.mailingPostcode}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <Typography sx={labelStyle}>
                {t("meetingLocation")}
                <Typography
                  sx={{ color: "red", display: "inline" }}
                  component={"span"}
                >
                  *
                </Typography>
              </Typography>
            </Grid>
            <Grid item xs={12} sm={8}>
              <Box>
                <MapContainer
                  /**
                   * @todo change meetingLocation with data from backend
                   */
                  center={[2.745564, 101.707021]}
                  zoom={13}
                  style={{
                    height: "10rem",
                    width: "100%",
                    borderRadius: "0.5rem",
                  }}
                >
                  <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                  {/**
                   * @todo change meetingLocation with data from backend
                   */}
                  <Marker position={[2.745564, 101.707021]} />
                </MapContainer>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </Box>
    </>
  );
};

export default CawanganMaklumat;
