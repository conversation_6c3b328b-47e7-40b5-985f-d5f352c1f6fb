// import { yupResolver } from "@hookform/resolvers/yup";
import { DefaultMeetingCreateBySocietyIdInitialValue } from "./initial-value";
import { mixed, number, object, ObjectSchema, string } from "yup";
import { MeetingMethods } from "@/helpers";
import { useTranslation } from "react-i18next";
import { useFormManagementMeetingDateTimeValidation } from "../date-time-validation";

export function useFormManagementMeetingCreateBySocietyIdValidationSchema<
  Payload extends DefaultMeetingCreateBySocietyIdInitialValue = DefaultMeetingCreateBySocietyIdInitialValue
>() {
  const { t } = useTranslation();

  const { getValidationObject } = useFormManagementMeetingDateTimeValidation()

  const getValidationSchema = () => {
    return object({
      meetingType: number()
        .label(t("meetingType"))
        .test({
          // name: "",
          test: (val) => val && val !== 0 ? true : false,
          message: t("fieldRequired")
        })
        .required(),
      meetingMethod: number()
        .label(t("meetingMethod"))
        .test({
          // name: "",
          test: (val) => val && val !== 0 ? true : false,
          message: t("fieldRequired")
        })
        .required(),
      platformType: number()
        .label(t("platformType"))
        .when({
          is: (val: Payload | null) => val?.meetingMethod && [MeetingMethods.ATAS_TALIAN, MeetingMethods.HYBRID]
            .map(parseInt)
            .includes(val.meetingMethod),
          then: (schema) => schema
            .test({
              // name: "",
              test: (val) => val && val !== 0 ? true : false,
              message: t("fieldRequired")
            })
            .required(),
          otherwise: (schema) => schema.nullable().notRequired()
        }),
      meetingPurpose: string()
        .label(t("tujuanMesyuarat"))
        .required(),

      ...getValidationObject({
        meetingDateAttribute: "meetingDate",
        meetingTimeFromAttribute: "meetingTime",
        meetingTimeToAttribute: "meetingTimeTo"
      }),

      meetingPlace: string()
        .label(t("meetingPlace"))
        .when({
          is: (val: Payload | null) => val?.meetingMethod && [MeetingMethods.BERSEMUKA, MeetingMethods.HYBRID]
            .map(parseInt)
            .includes(val.meetingMethod),
          then: (schema) => schema.required(),
          otherwise: (schema) => schema.nullable().notRequired()
        }),
      /**
       * @todo add meetingLocation validation schema
       */

      meetingAddress: string()
        .label(t("meetingPlaceAddress"))
        .when({
          is: (val: Payload | null) => val?.meetingMethod && [MeetingMethods.BERSEMUKA, MeetingMethods.HYBRID]
            .map(parseInt)
            .includes(val.meetingMethod),
          then: (schema) => schema.required(),
          otherwise: (schema) => schema.nullable().notRequired()
        }),
      state: number()
        .label(t("state"))
        .when({
          is: (val: Payload | null) => val?.meetingMethod && [MeetingMethods.BERSEMUKA, MeetingMethods.HYBRID]
            .map(parseInt)
            .includes(val.meetingMethod),
          then: (schema) => schema.required(),
          otherwise: (schema) => schema.nullable().notRequired()
        }),
      district: number()
        .label(t("district"))
        .when({
          is: (val: Payload | null) => val?.meetingMethod && [MeetingMethods.BERSEMUKA, MeetingMethods.HYBRID]
            .map(parseInt)
            .includes(val.meetingMethod),
          then: (schema) => schema.required(),
          otherwise: (schema) => schema.nullable().notRequired()
        }),
      city: string()
        .label(t("city"))
        .nullable()
        .notRequired(),
      postcode: string()
        .label(t("postcode"))
        .test({
          name: "postcode_validation",
          test: (val: string | undefined, context) => {
            const allValue = context.parent as Payload
            if (allValue.meetingMethod && [MeetingMethods.BERSEMUKA, MeetingMethods.HYBRID]
              .map(parseInt)
              .includes(allValue.meetingMethod)
            ) {
              return typeof val === "string" && !Number.isNaN(parseInt(val)) && val?.length === 5
            }
            return true
          },
          message: t("postcodeValidation")
        })
        .when({
          is: (val: Payload | null) => val?.meetingMethod && [MeetingMethods.BERSEMUKA, MeetingMethods.HYBRID]
            .map(parseInt)
            .includes(val.meetingMethod),
          then: (schema) => schema.required(),
          otherwise: (schema) => schema.nullable().notRequired()
        }),
      /**
       * @todo add attendees
       */
      totalAttendees: number()
        .label(t("totalAttendMember"))
        .positive()
        .integer()
        .min(7, t("inputValidationErrorTotalAttendeesBelowMin", { value: 7 }))
        .required(),

      meetingMinute: string()
        .label(t("minitMesyuarat")),

      meetingMinuteAttachment: mixed()
        .label(t("minitMesyuarat"))
        .required(t("fieldRequired"))
        .test({
          name: "validate_file_extensions",
          test: (val) => {
            if (val instanceof File) {
              const extension = val.name.split(".").pop();
              return extension === "pdf";
            }
            return false;
          },
          message: t("error"),
        })
        .test({
          name: "validate_file_size",
          test: (val) => val instanceof File && val.size <= 25 * 1024 * 1024,
          message: t("inputValidationErrorFileSizeExceedsLimitDynamic", {
            limit: "25MB",
          }),
        })
        .test({
          name: "validate_file_type",
          test: (val) => val instanceof File,
          message: t("fieldRequired"),
        }),
    })
      .required() as unknown as ObjectSchema<Payload>
  }

  return {
    getValidationSchema
  }
}
