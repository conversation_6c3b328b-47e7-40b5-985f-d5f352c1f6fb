import React, { useState } from "react";
import useQuery from "../../helpers/hooks/useQuery";
import {
  Box,
  Card,
  Grid,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Button,
  TextField,
  InputAdornment,
  IconButton,
  DialogActions,
  TableContainer,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import CloseIcon from "@mui/icons-material/Close";
import { API_URL } from "../../api";
import {
  ApplicationStatusList,
  HideOrDisplayInherit,
  NewSocietyBranchStatus,
  OrganisationPositions,
} from "../../helpers/enums";
import ConfirmationDialog from "../../components/dialog/confirm";
import { useCreate, useCustomMutation, useNotification } from "@refinedev/core";
import { ComingSoon } from "../../components/header/comingSoonAuth";
import SearchIcon from "@mui/icons-material/Search";
import ArrowRightAltIcon from "@mui/icons-material/ArrowRightAlt";
import { ButtonPrimary, ButtonText } from "../../components/button";
import { EditIcon, EyeIcon } from "@/components/icons";
import TrashIcon from "@/assets/svg/icon-trash.svg?react";

import { capitalizeWords, useMutation } from "@/helpers";
import { ArrowBackground2SVG } from "@/components/icons/arrowBackground2";
// import { DashboardChatBox } from "./chatBox";
import { FieldValues, useForm } from "react-hook-form";
import DataTable from "@/components/datatable";
import NotificationListDashboard from "./notification";
import { TickAddBackgroundSVG } from "@/components/icons/tickAddBackground";
import { AddBackgroundSVG } from "@/components/icons/addBackground";

export interface tempSocietyData {
  id: 0;
  societyName: "";
  societyNo: "";
  committeeStatus: any;
}

export const ExternalUser = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { open } = useNotification();
  const getUserDetails = localStorage.getItem("user-details");
  const userName = getUserDetails ? JSON.parse(getUserDetails).name : "";

  const handleDaftarPertubuhan = () => {
    navigate("/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am");
  };

  const gotoComingSoon = () => {
    setShowComingSoon(true);
  };

  const [openSertaModal, setOpenSertaModal] = useState(false);
  const [societyList, setSocietyList] = useState<tempSocietyData[]>([]);
  const [searchSertaPertubuhan, setSearchSertaPertubuhan] = useState("");
  const [showNoResults, setShowNoResults] = useState(true);
  const [isSerta, setIsSerta] = useState(true);
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [selectedSocietyId, setSelectedSocietyId] = useState<string | null>(
    null
  );

  const [showComingSoon, setShowComingSoon] = useState(false);

  const { mutate: joinSociety } = useCustomMutation();

  const handleOpenSertaModal = () => {
    setOpenSertaModal(true);
  };

  const handleCloseSertaModal = () => {
    setOpenSertaModal(false);
    setShowNoResults(true);
  };
  interface Params {
    [key: string]: string; // Allows any string key with a string value
  }

  const handleSearchChange = async () => {
    try {
      const params: Params = {
        searchQuery: searchSertaPertubuhan,
      };
      const query = Object.keys(params)
        .map((k) => `${k}=${encodeURIComponent(params[k])}`)
        .join("&");
      const response = await fetch(
        `${API_URL}/society/searchJoinSociety?${query}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();

      if (data.status.toUpperCase() === "SUCCESS") {
        if (data.code === 204 && data.msg) {
          open?.({
            message: `${t(data?.msg)} ${searchSertaPertubuhan}`,
            type: "success",
          });
        }
        if (searchSertaPertubuhan) {
          setSocietyList(data?.data);
          setShowNoResults(false);
        } else {
          setSocietyList([]);
          setShowNoResults(true);
        }
      } else {
        setSocietyList([]);
        setShowNoResults(true);
        setRefreshKey((prev) => prev + 1);
      }
    } catch (error) {
      console.error("Error fetching society data:", error);
      return false;
    }
  };

  const handleSertaClick = (i: number, val: boolean) => {
    const join = val ? "Join" : "Cancel";
    console.log("societyId", i);
    joinSociety(
      {
        method: "post",
        url: `${API_URL}/society/${i}/join?joinOrCancel=${join}`,
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: {
          message: t("success"),
          type: "success",
        },
        errorNotification: {
          message: t("error"),
          type: "error",
        },
      },
      {
        onSuccess: () => {
          handleSearchChange();
        },
      }
    );
    setIsSerta(!isSerta);
  };

  const active = [NewSocietyBranchStatus.AKTIF_1];

  // useQuery({
  //   url: "user/auth/me",
  //   onSuccess: (data) => {
  //     const userId = data?.data?.data?.identificationNo;
  //   },
  // });

  const columns = [
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        const isActive = active.includes(params?.row?.statusCode);
        return (
          <Box
            onClick={() => {
              isActive &&
                navigate(`/pertubuhan/society/${row.id}/senarai/maklumat`);
            }}
            style={{
              textDecoration: "none",
              cursor: isActive ? "pointer" : "not-allowed",
              pointerEvents: isActive ? "auto" : "none",
            }}
          >
            {params?.row?.societyName}
          </Box>
        );
      },
    },
    {
      field: "societyNo",
      headerName: t("OrganizationNo"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const societyNo = params?.row?.societyNo;
        const applicationNo = params?.row?.applicationNo;

        return societyNo ?? applicationNo ?? t("-");
      },
      cellClassName: "custom-cell",
    },
    {
      field: "applicationStatusCode",
      headerName: t("applicationStatus"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const status = ApplicationStatusList.find(
          (item) => item.id === params?.row?.applicationStatusCode
        );
        return status
          ? capitalizeWords(t(status.value), (word, sentenceIndex) =>
              sentenceIndex > 0 ? word.toLowerCase() : null
            )
          : t("-");
      },
      cellClassName: "custom-cell",
    },
    {
      field: "statusCode",
      headerName: t("organizationStatus"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const isActive = active.includes(params?.row?.statusCode);
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Typography
              className="status-pertubuhan-text"
              sx={{
                backgroundColor: "#fff",
                border: `2px solid ${
                  isActive ? "var(--success)" : "var(--error)"
                }`,
              }}
            >
              {isActive ? t("active") : t("inactive")}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "createdDate",
      headerName: t("dateApplied"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        return params?.row?.createdDate;
      },
      cellClassName: "custom-cell",
    },
    {
      field: "designationCode",
      headerName: t("position"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return t(
          `${
            OrganisationPositions.find(
              (item) => item?.value === Number(row?.designationCode)
            )?.label || "-"
          }`
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        const isActive = active.includes(row?.statusCode);

        if (!isActive) {
          switch (row?.applicationStatusCode) {
            case 2:
            case 40:
            case 4:
            case 3:
              return (
                <IconButton
                  // size="small"
                  sx={{
                    width: "3rem",
                    height: "3rem",
                  }}
                  onClick={() =>
                    navigate(`/pertubuhan/society/${row?.id}/senarai/maklumat`)
                  }
                >
                  <EyeIcon
                    color="#147C7C"
                    style={{ width: "1rem", height: "1rem" }}
                  />
                </IconButton>
              );
            case 36:
            case 1:
              if (row?.isManageAuthorized) {
                return (
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "right",
                      justifyContent: "flex-end",
                    }}
                  >
                    <IconButton
                      sx={{
                        width: "3rem",
                        height: "3rem",
                        color: "var(--primary-color)",
                      }}
                      onClick={() =>
                        navigate(
                          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am?disabled=true&id=${btoa(
                            row.id
                          )}`
                        )
                      }
                    >
                      <EditIcon
                        style={{
                          color: "#147C7C !important",
                          width: "1rem",
                          height: "1rem",
                        }}
                      />
                    </IconButton>
                    <IconButton
                      sx={{
                        width: "3rem",
                        height: "3rem",
                        display: HideOrDisplayInherit,
                      }}
                      onClick={() => handleDeleteClick(row.id)}
                    >
                      <TrashIcon
                        style={{
                          width: "1rem",
                          height: "1rem",
                          display: HideOrDisplayInherit,
                        }}
                      />
                    </IconButton>
                  </Box>
                );
              }
              break;
            case 5:
            case 6:
              if (row?.isManageAuthorized) {
                return (
                  <IconButton
                    sx={{
                      width: "3rem",
                      height: "3rem",
                      color: "var(--primary-color)",
                    }}
                    onClick={() =>
                      navigate(
                        `/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am?disabled=true&id=${btoa(
                          row.id
                        )}`
                      )
                    }
                  >
                    <EditIcon
                      style={{
                        color: "#147C7C !important",
                        width: "1rem",
                        height: "1rem",
                      }}
                    />
                  </IconButton>
                );
              }
              break;
            default:
              return null;
          }
        } else {
          if (row?.applicationStatusCode === 3) {
            return (
              <IconButton
                sx={{
                  width: "3rem",
                  height: "3rem",
                }}
                onClick={() =>
                  navigate(`/pertubuhan/society/${row?.id}/senarai/maklumat`)
                }
              >
                <EyeIcon
                  sx={{
                    color: "#147C7C !important",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            );
          }
        }
      },
    },
  ];

  const handleDeleteClick = (societyId: string) => {
    setSelectedSocietyId(societyId);
    setOpenConfirmDialog(true);
  };

  const [refreshKey, setRefreshKey] = useState(0);

  const { fetch: deleteSociety, isLoading: isDeletingSociety } = useMutation({
    url: `society/${selectedSocietyId}`,
    method: "delete",
    onSuccess: (data) => {
      setRefreshKey((prev) => prev + 1);
      setOpenConfirmDialog(false);
      refetchSocietyList();
    },
  });

  const handleConfirmDelete = () => {
    if (selectedSocietyId) {
      deleteSociety();
    }
  };

  const renderButtons = (e: tempSocietyData) => {
    if (
      e.committeeStatus === null ||
      e.committeeStatus == "-1" ||
      e.committeeStatus == "43"
    ) {
      return (
        <ButtonPrimary onClick={() => handleSertaClick(e.id, true)}>
          {t("join")}
        </ButtonPrimary>
      );
    }
    if (e.committeeStatus == "2") {
      return (
        <ButtonPrimary
          onClick={() => handleSertaClick(e.id, false)}
          sx={{
            backgroundColor: "#FF0000",
            "&:hover": { backgroundColor: "#FF0000" },
          }}
        >
          {t("cancel")}
        </ButtonPrimary>
      );
    }
    return null;
  };

  const handleSertaPertubuhanKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      const value = (event.target as HTMLInputElement).value;
      handleSearchChange();
    }
  };

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 3,
    },
  });

  const handleGoToNotifications = () => {
    navigate("/notifikasi");
  };

  const page = watch("page");
  const pageSize = watch("pageSize");
  const {
    data: societyListDataResponse,
    isLoading: isLoadingSocietyListData,
    refetch: refetchSocietyList,
  } = useQuery({
    url: "society/getUserSociety",
    filters: [
      {
        field: "pageSize",
        value: 5,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
    ],
  });

  const totalList = societyListDataResponse?.data?.data?.total ?? 0;
  const rowData = societyListDataResponse?.data?.data?.data ?? [];

  const handleChangePage = (newPage: number) => setValue("page", newPage);

  return (
    <>
      {showComingSoon ? (
        <ComingSoon onBack={() => setShowComingSoon(false)} />
      ) : (
        <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
          {/* #1 BIG SCREEN  */}
          <Grid
            container
            spacing={2}
            sx={{
              display: {
                xs: "none",
                sm: "none",
                md: "none",
                lg: "none",
                xl: "flex",
              },
            }}
          >
            <Grid item md={9.5} sx={{ display: "grid", gap: 1 }}>
              <Box
                sx={{
                  borderRadius: "15px",
                  backgroundImage: 'url("/bg-feedback.jpg")',
                  backgroundSize: "cover",
                  backgroundRepeat: "no-repeat",
                  backgroundPosition: "center center",
                }}
              >
                <Box
                  sx={{
                    zIndex: 1,
                    backgroundColor: "rgba(161, 161, 161, 0.5)",
                    borderRadius: "15px",
                    height: 260,
                    display: "grid",
                    // justifyContent: "center",
                    alignItems: "center",
                    p: 5,
                    gap: 0,
                  }}
                >
                  <Box>
                    <Typography
                      variant="h6"
                      sx={{
                        fontFamily: "Poppins",
                        fontSize: "25px",
                        fontWeight: 300,
                        color: "white",
                        textShadow: "2px 2px 4px rgba(0, 0, 0, 0.3)",
                      }}
                    >
                      {t("organizationManagementSystem")}
                    </Typography>
                  </Box>
                </Box>
              </Box>
              <Box
                sx={{
                  display: "flex",
                  gap: 1,
                }}
              >
                <Grid container spacing={1}>
                  <Grid item md={5}>
                    <Box
                      sx={{
                        bgcolor: "#fff",
                        borderRadius: "15px",
                        display: "grid",
                        gap: 1,
                        p: 2,
                        height: "100%",
                      }}
                    >
                      <Typography
                        sx={{
                          mb: 0.5,
                        }}
                        className="sub-step-login"
                      >
                        {t("sertaPertubuhan")}
                      </Typography>
                      <Grid container spacing={1}>
                        <Grid item xs={2} sm={4} md={4}>
                          <Box
                            sx={{
                              backgroundImage: `url("data:image/svg+xml;utf8,${encodeURIComponent(
                                TickAddBackgroundSVG
                              )}")`,
                              p: 1,
                              borderRadius: "10px",
                              backgroundColor: "var(--primary-color)",
                              backgroundRepeat: "no-repeat",
                              backgroundPosition: "5px bottom",
                              backgroundSize: "40px",
                              cursor: "pointer",
                              textAlign: "right",
                              // pb: 2,
                              height: "100%",
                              boxShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                            }}
                            onClick={handleOpenSertaModal}
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "top",
                                justifyContent: "flex-end",
                              }}
                            >
                              <Typography
                                sx={{
                                  fontSize: {
                                    xs: "14px",
                                    sm: "14px",
                                    md: "14px",
                                    lg: "14px",
                                  },
                                  textShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                                  color: "white",
                                  width: "50%",
                                }}
                              >
                                {t("memberRegister")}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={2} sm={4} md={4}>
                          <Box
                            sx={{
                              backgroundImage: `url("data:image/svg+xml;utf8,${encodeURIComponent(
                                AddBackgroundSVG
                              )}")`,
                              p: 1,
                              borderRadius: "10px",
                              backgroundColor: "var(--primary-color)",
                              textAlign: "right",
                              backgroundRepeat: "no-repeat",
                              backgroundPosition: "5px bottom",
                              backgroundSize: "40px",
                              cursor: "pointer",
                              pb: 4,
                              height: "100%",
                              boxShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                            }}
                            onClick={
                              //@ts-ignore
                              HideOrDisplayInherit === "none"
                                ? gotoComingSoon
                                : handleDaftarPertubuhan
                            }
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "top",
                                justifyContent: "flex-end",
                              }}
                            >
                              <Typography
                                sx={{
                                  fontSize: {
                                    xs: "14px",
                                    sm: "14px",
                                    md: "14px",
                                    lg: "14px",
                                  },
                                  width: {
                                    md: "100%",
                                    lg: "100%",
                                    xl: "90%",
                                  },
                                  textShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                                  color: "white",
                                }}
                              >
                                {t("registerOrg")}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                        <Grid item xs={2} sm={4} md={4}>
                          <Box
                            sx={{
                              backgroundImage: `url("data:image/svg+xml;utf8,${encodeURIComponent(
                                ArrowBackground2SVG
                              )}")`,
                              p: 1,
                              borderRadius: "10px",
                              backgroundColor: "var(--primary-color)",
                              backgroundRepeat: "no-repeat",
                              backgroundPosition: "5px bottom",
                              backgroundSize: "40px",
                              cursor: "pointer",
                              textAlign: "right",
                              pb: 4,
                              height: "100%",
                              boxShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                            }}
                            onClick={() =>
                              navigate("/pertubuhan/pembaharuan-setiausaha")
                            }
                          >
                            <Box
                              sx={{
                                display: "flex",
                                alignItems: "top",
                                justifyContent: "flex-end",
                              }}
                            >
                              <Typography
                                sx={{
                                  fontSize: {
                                    xs: "14px",
                                    sm: "14px",
                                    md: "14px",
                                    lg: "14px",
                                  },
                                  width: {
                                    md: "100%",
                                    lg: "100%",
                                    xl: "80%",
                                  },
                                  textShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                                  color: "white",
                                }}
                              >
                                {t("tukarSetiausaha")}
                              </Typography>
                            </Box>
                          </Box>
                        </Grid>
                      </Grid>
                    </Box>
                  </Grid>
                  {/* Pengumuman */}
                  <Grid item md={7}>
                    <Box
                      sx={{
                        display: "flex",
                        flexDirection: "column",
                        gap: 1,
                        height: "100%",
                      }}
                    >
                      <Box sx={{ display: "flex", gap: 1 }}>
                        <Box
                          sx={{
                            px: 2,
                            py: 3,
                            display: "flex",
                            color: "white",
                            bgcolor: "#fff",
                            borderRadius: "15px",
                            justifyContent: "center",
                            alignItems: "center",
                            flex: 1,
                            gap: 2,
                          }}
                        >
                          <Box
                            sx={{
                              display: "grid",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <img
                              // width={50}
                              height={45}
                              src={"/exclamation_dialog.png"}
                            />
                            <Typography className="error-text-login">
                              {t("peringatanCapFirst")}
                            </Typography>
                          </Box>

                          <Typography className="label">
                            {t("payOnlineRenewIn24")}
                          </Typography>
                        </Box>
                        <Box
                          sx={{
                            px: 2,
                            py: 3,
                            flex: 1,
                            display: "flex",
                            color: "white",
                            borderRadius: "15px",
                            justifyContent: "center",
                            alignItems: "center",
                            bgcolor: "#fff",
                            gap: 2,
                          }}
                        >
                          <Box
                            sx={{
                              display: "grid",
                              justifyContent: "center",
                              alignItems: "center",
                            }}
                          >
                            <img
                              // width={50}
                              height={45}
                              src={"/exclamation_dialog.png"}
                            />
                            <Typography className="error-text-login">
                              {t("peringatanCapFirst")}
                            </Typography>
                          </Box>

                          <Typography className="label">
                            {t("amendmentsConstitutionAlert")}
                          </Typography>
                        </Box>
                      </Box>
                    </Box>
                  </Grid>
                </Grid>
              </Box>
            </Grid>

            {/* NOTIFICATION SECTION */}
            <Grid item md={2.5} sx={{ height: "50vh" }}>
              <Box
                sx={{
                  height: "100%",
                  bgcolor: "white",
                  borderRadius: "15px",
                  display: "grid",
                  gap: 2,
                }}
              >
                <NotificationListDashboard />

                <Button
                  sx={{
                    m: "1rem",
                    display: "flex",
                    alignItems: "center",
                    p: 1.5,
                    gap: 1,
                    marginTop: "auto",
                    textTransform: "none",
                    color: "var(--text-grey-disabled)",
                    fontSize: "14px",
                    fontWeight: 500,
                    borderRadius: "10px",
                    border: "1px solid var(--primary-color)",
                  }}
                  onClick={() => handleGoToNotifications()}
                >
                  {t("seeFullView")}
                  <ArrowRightAltIcon
                    sx={{ color: "var(--text-grey-disabled)" }}
                  />
                </Button>
              </Box>
            </Grid>
          </Grid>

          {/* #1  SMALL SCREEN VIEW */}
          <Box
            sx={{
              display: {
                xs: "grid",
                sm: "grid",
                md: "grid",
                lg: "grid",
                xl: "none",
              },
              gap: 2,
            }}
          >
            {/* PIC AND NOTIFICATION */}
            <Box>
              <Grid container spacing={2}>
                <Grid item sm={8} md={8} sx={{ display: "grid", gap: 1 }}>
                  <Box
                    sx={{
                      borderRadius: "15px",
                      backgroundImage: 'url("/bg-feedback.jpg")',
                      backgroundSize: "cover",
                      backgroundRepeat: "no-repeat",
                      backgroundPosition: "center center",
                      height: 290,
                    }}
                  >
                    <Box
                      sx={{
                        zIndex: 1,
                        backgroundColor: "rgba(161, 161, 161, 0.5)",
                        borderRadius: "15px",
                        display: "grid",
                        // justifyContent: "center",
                        alignItems: "center",
                        height: 290,
                        p: {
                          xs: 3,
                          sm: 3,
                          md: 5,
                          lg: 5,
                          xl: 6,
                        },
                      }}
                    >
                      <Typography
                        variant="h6"
                        sx={{
                          fontFamily: "Poppins",
                          fontSize: "25px",
                          fontWeight: 300,
                          color: "white",
                          textShadow: "2px 2px 4px rgba(0, 0, 0, 0.3)",
                        }}
                      >
                        {t("organizationManagementSystem")}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                {/* NOTIFICATION SECTION */}
                <Grid item sm={4} md={4}>
                  <Box
                    sx={{
                      height: "40.5vh",
                      bgcolor: "white",
                      borderRadius: "15px",
                      display: "grid",
                      gap: { lg: 1, xl: 2 },
                    }}
                  >
                    <NotificationListDashboard />

                    <Button
                      sx={{
                        m: { xs: 1, sm: 1, md: 1, lg: 1, xl: 1.5 },
                        display: "flex",
                        alignItems: "center",
                        p: { xs: 0.5, sm: 0.5, md: 0.5, lg: 0.5, xl: 1.5 },
                        gap: { lg: 0.2, xl: 1 },
                        textTransform: "none",
                        borderRadius: "10px",
                        border: "1px solid var(--text-grey-disabled)",
                      }}
                    >
                      <Typography className="step-header-login-disabled">
                        {t("seeFullView")}
                        <ArrowRightAltIcon className="step-header-login-disabled" />
                      </Typography>
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* BTN SECTION */}

            <Grid container spacing={1}>
              <Grid item md={5}>
                <Box
                  sx={{
                    bgcolor: "#fff",
                    borderRadius: "15px",
                    display: "grid",
                    gap: 1,
                    p: 2,
                    height: "100%",
                  }}
                >
                  <Typography
                    sx={{
                      mb: 1,
                    }}
                    className="sub-step-login-disabled"
                  >
                    {t("sertaPertubuhan")}
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={2} sm={4} md={4}>
                      <Box
                        sx={{
                          backgroundImage: `url("data:image/svg+xml;utf8,${encodeURIComponent(
                            TickAddBackgroundSVG
                          )}")`,
                          p: 1,
                          borderRadius: "10px",
                          backgroundColor: "var(--primary-color)",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "5px bottom",
                          backgroundSize: "40px",
                          cursor: "pointer",
                          textAlign: "right",
                          // pb: 2,
                          height: "100%",
                          boxShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                        }}
                        onClick={handleOpenSertaModal}
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "top",
                            justifyContent: "flex-end",
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: {
                                xs: "14px",
                                sm: "14px",
                                md: "14px",
                                lg: "14px",
                              },
                              textShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                              color: "white",
                              width: "50%",
                            }}
                          >
                            {t("memberRegister")}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={2} sm={4} md={4}>
                      <Box
                        sx={{
                          backgroundImage: `url("data:image/svg+xml;utf8,${encodeURIComponent(
                            AddBackgroundSVG
                          )}")`,
                          p: 1,
                          borderRadius: "10px",
                          backgroundColor: "var(--primary-color)",
                          textAlign: "right",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "5px bottom",
                          backgroundSize: "40px",
                          cursor: "pointer",
                          pb: 4,
                          height: "100%",
                          boxShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                        }}
                        onClick={
                          //@ts-ignore
                          HideOrDisplayInherit === "none"
                            ? gotoComingSoon
                            : handleDaftarPertubuhan
                        }
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "top",
                            justifyContent: "flex-end",
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: {
                                xs: "14px",
                                sm: "14px",
                                md: "14px",
                                lg: "14px",
                              },
                              width: {
                                xs: "100%",
                                sm: "100%",
                                md: "100%",
                                lg: "100%",
                              },
                              textShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                              color: "white",
                            }}
                          >
                            {t("registerOrg")}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                    <Grid item xs={2} sm={4} md={4}>
                      <Box
                        sx={{
                          backgroundImage: `url("data:image/svg+xml;utf8,${encodeURIComponent(
                            ArrowBackground2SVG
                          )}")`,
                          p: 1,
                          borderRadius: "10px",
                          backgroundColor: "var(--primary-color)",
                          backgroundRepeat: "no-repeat",
                          backgroundPosition: "5px bottom",
                          backgroundSize: "40px",
                          cursor: "pointer",
                          textAlign: "right",
                          pb: 4,
                          height: "100%",
                          boxShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                        }}
                        onClick={() =>
                          navigate("/pertubuhan/pembaharuan-setiausaha")
                        }
                      >
                        <Box
                          sx={{
                            display: "flex",
                            alignItems: "top",
                            justifyContent: "flex-end",
                          }}
                        >
                          <Typography
                            sx={{
                              fontSize: {
                                xs: "14px",
                                sm: "14px",
                                md: "14px",
                                lg: "14px",
                              },
                              width: {
                                xs: "100%",
                                sm: "100%",
                                md: "100%",
                                lg: "100%",
                              },
                              textShadow: "2px 2px 4px rgba(0, 0, 0, 0.2)",
                              color: "white",
                            }}
                          >
                            {t("tukarSetiausaha")}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>
                  </Grid>
                </Box>
              </Grid>
              {/* QA SECTION */}
              <Grid item md={7}>
                <Box
                  sx={{
                    display: "flex",
                    gap: 1,
                    height: "100%",
                  }}
                >
                  <Box
                    sx={{
                      p: 2,
                      display: "flex",
                      borderRadius: "15px",
                      bgcolor: "#fff",
                      height: "100%",
                      justifyContent: "center",
                      alignItems: "center",
                      gap: 3,
                    }}
                  >
                    <Box
                      sx={{
                        display: "grid",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <img
                        // width={50}
                        height={45}
                        src={"/exclamation_dialog.png"}
                      />
                      <Typography className="error-text-login">
                        {t("peringatanCapFirst")}
                      </Typography>
                    </Box>

                    <Typography className="label">
                      {t("payOnlineRenewIn24")}
                    </Typography>
                  </Box>
                  <Box
                    sx={{
                      p: 2,
                      display: "flex",
                      borderRadius: "15px",
                      bgcolor: "#fff",
                      justifyContent: "center",
                      alignItems: "center",
                      gap: 3,
                      height: "100%",
                    }}
                  >
                    <Box
                      sx={{
                        display: "grid",
                        justifyContent: "center",
                        alignItems: "center",
                      }}
                    >
                      <img
                        // width={50}
                        height={45}
                        src={"/exclamation_dialog.png"}
                      />
                      <Typography className="error-text-login">
                        {t("peringatanCapFirst")}
                      </Typography>
                    </Box>

                    <Typography className="label">
                      {t("amendmentsConstitutionAlert")}
                    </Typography>
                  </Box>
                </Box>
              </Grid>
            </Grid>
          </Box>

          {/* #2 TABLE BIG SCREEN VIEW */}
          <Card
            sx={{
              p: {
                xs: 1,
                sm: 1,
                md: 1,
                lg: 3,
                xl: 3,
              },
              borderRadius: "15px",
              boxShadow: "none",
              height: "100%",
              width: "100%",
              display: "grid",
              alignItems: "center",
              gap: 2,
            }}
          >
            <Typography
              sx={{
                m: {
                  xs: "1rem 1rem 0rem 1rem",
                  sm: "1rem 1rem 0rem 1rem",
                  md: "1rem 1rem 0rem 1rem",
                  lg: 0,
                  xl: 0,
                },
              }}
              className="sub-step-login"
            >
              {t("pertubuhanAnda")}
            </Typography>
            {/* <TextField
                  fullWidth
                  variant="outlined"
                  placeholder={t("namaPertubuhan")}
                  sx={{
                    display: "block",
                    boxSizing: "border-box",
                    maxWidth: 570,
                    marginInline: "auto",
                    height: "40px",
                    background: "var(--border-grey)",
                    opacity: 0.5,
                    border: "1px solid var(--text-grey)",
                    borderRadius: "10px",
                    "& .MuiOutlinedInput-root": {
                      height: "40px",
                      "& fieldset": {
                        border: "none",
                      },
                    },
                  }}
                  onKeyDown={handleDashboardKeyDown}
                  onChange={(e) => setSearchSertaPertubuhan(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon
                          sx={{
                            color: "var(--text-grey-disabled)",
                            marginLeft: "8px",
                          }}
                        />
                      </InputAdornment>
                    ),
                  }}
                /> */}

            <DataTable
              columns={columns as any}
              rows={rowData}
              page={page}
              rowsPerPage={pageSize}
              totalCount={totalList}
              onPageChange={handleChangePage}
              isLoading={isLoadingSocietyListData}
              pagination={false}
            />

            <Button
              sx={{
                m: "1rem",
                display: "flex",
                alignItems: "center",
                p: "8px 16px 8px 22px",
                gap: 1,
                marginInline: "auto",
                textTransform: "none",
                color: "var(--text-grey-disabled)",
                fontSize: "14px",
                fontWeight: 500,
                borderRadius: "10px",
                border: "1px solid var(--primary-color)",
                maxWidth: 230,
              }}
              onClick={() => navigate("/pengurus-pertubuhan/pertubuhan")}
            >
              <Typography className="step-header-login-disabled">
                {t("seeFullView")}
              </Typography>
              <ArrowRightAltIcon sx={{ color: "var(--text-grey-disabled)" }} />
            </Button>
          </Card>
          <Dialog
            open={openSertaModal}
            onClose={handleCloseSertaModal}
            PaperProps={{
              style: {
                borderRadius: "1rem !important",
                backgroundColor: "#fff",
                color: "#000",
                minWidth: "50dvw",
                minHeight: "50dvh",
              },
            }}
            slotProps={{
              backdrop: {
                style: {
                  backgroundColor: "rgba(0, 0, 0, 0.5)",
                  backdropFilter: "blur(4px)",
                },
              },
            }}
          >
            <DialogTitle sx={{ pb: 2.5 }}>
              <Box
                sx={{
                  px: 2.5,
                  py: 0.5,
                  borderRadius: 2.5,
                }}
                display="flex"
                justifyContent="space-between"
                alignItems="center"
              >
                <Typography
                  variant="h6"
                  component="h2"
                  sx={{
                    color: "var(--primary-color)",
                    borderRadius: "16px",
                    fontSize: "16px",
                  }}
                >
                  {t("memberRegister")}
                </Typography>
                <IconButton onClick={handleCloseSertaModal} size="small">
                  <CloseIcon sx={{ color: "black" }} />
                </IconButton>
              </Box>
            </DialogTitle>
            <DialogContent sx={{ py: 2, px: 5, overflowY: "auto" }}>
              <Box
                sx={{
                  display: "flex",
                  alignItems: "center",
                  mb: 8,
                }}
              >
                {/* <Box sx={{ display: "flex", alignItems: "center" }} /> */}

                <TextField
                  fullWidth
                  placeholder={t("searchOrganization")}
                  sx={{
                    display: "block",
                    boxSizing: "border-box",
                    height: "40px",
                    marginTop: "12px",
                    background: "rgba(132, 132, 132, 0.3)",
                    opacity: 0.5,
                    border: "1px solid rgba(102, 102, 102, 0.8)",
                    borderRadius: "10px",
                    "& .MuiOutlinedInput-root": {
                      height: "40px",
                      "& fieldset": {
                        border: "none",
                      },
                    },
                  }}
                  onKeyDown={handleSertaPertubuhanKeyDown}
                  onChange={(e) => setSearchSertaPertubuhan(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon
                          onClick={() => handleSearchChange()}
                          sx={{
                            color: "#9CA3AF",
                            marginLeft: "8px",
                            "&:hover": { cursor: "pointer" },
                          }}
                        />
                      </InputAdornment>
                    ),
                  }}
                />
              </Box>
              <TableContainer sx={{ maxHeight: 440 }}>
                <Table stickyHeader>
                  <TableHead>
                    <TableRow>
                      <TableCell align="center">
                        {t("organizationName")}
                      </TableCell>
                      <TableCell align="center">
                        {t("organizationNumber")}
                      </TableCell>
                      <TableCell align="center">{t("status")}</TableCell>
                      <TableCell align="center">{t("action")}</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {showNoResults ? (
                      <TableRow>
                        <TableCell colSpan={4} align="center">
                          {t("noOrganizationFound")}
                        </TableCell>
                      </TableRow>
                    ) : (
                      <>
                        {societyList?.map((e, i) => (
                          <TableRow key={i}>
                            <TableCell align="center">
                              {e.societyName ?? ""}
                            </TableCell>
                            <TableCell align="center">
                              {e.societyNo ?? ""}
                            </TableCell>
                            <TableCell align="center">
                              {e.committeeStatus == null ||
                              e.committeeStatus == "2"
                                ? t("notMember")
                                : t("member")}
                            </TableCell>
                            <TableCell align="center">
                              {renderButtons(e)}
                            </TableCell>
                          </TableRow>
                        ))}
                      </>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </DialogContent>
            <DialogActions
              sx={{
                py: 2,
                px: 3,
                justifyContent: "center",
                flexDirection: "column",
                gap: 1,
              }}
            >
              <ButtonText
                onClick={handleCloseSertaModal}
                sx={{
                  marginLeft: "0px !important",
                }}
              >
                {t("back")}
              </ButtonText>
            </DialogActions>
          </Dialog>
          <ConfirmationDialog
            open={openConfirmDialog}
            onClose={() => setOpenConfirmDialog(false)}
            onConfirm={handleConfirmDelete}
            onCancel={() => setOpenConfirmDialog(false)}
            title={t("confirmDelete")}
            message={t("deleteOrganizationConfirmation")}
            isMutation={isDeletingSociety}
          />
        </Box>
      )}
    </>
  );
};
