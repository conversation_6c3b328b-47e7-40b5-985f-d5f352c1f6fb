import { Box, IconButton, Typography } from "@mui/material";
import { GridColDef } from "@mui/x-data-grid";
import { useMemo, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { DataGridUI } from "@/components/datagrid/UI";
import FilterBar from "@/components/filter";
import { EyeIcon } from "@/components/icons";
import { formatDate, MeetingTypeOption, useDataGrid } from "@/helpers";
import { usePertubuhanContext } from "../PertubuhanProvider";

export interface MeetingBySocietyIdResponseBodyGet {
  id: number;
  meetingType: string;
  meetingPlace: string;
  meetingDate: string;
  status: string;
}

const MaklumatMesyuaratSection: React.FC = <
  Data extends MeetingBySocietyIdResponseBodyGet = MeetingBySocietyIdResponseBodyGet
>() => {
  const { t } = useTranslation();
  const { societyDetailData } = usePertubuhanContext();
  const navigate = useNavigate();

  const [filterValues, setFilterValues] = useState<
    Record<
      "societyId" | "meetingType" | "meetingYear" | "status" | "branchId",
      number | null
    >
  >(() => ({
    societyId: societyDetailData?.id ?? null,
    meetingType: null,
    meetingYear: null,
    status: null,
    branchId: null,
  }));
  const initialSelectedFilters = {
    meetingType: "meetingType",
    status: "status",
    meetingYear: "meetingYear",
  };
  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >(() => initialSelectedFilters);

  const { societyId, meetingType, meetingYear, status, branchId } =
    filterValues;
  const {
    // biome-ignore lint/correctness/noUnusedVariables: unused variable
    dataGridProps: { sx, ...dataGridProps },
  } = useDataGrid({
    resource: "society/meeting/search",
    meta: {
      params: {
        societyId,
        ...(meetingType && { meetingType }),
        ...(meetingYear && { meetingYear }),
        ...(status && { status }),
        ...(branchId && { branchId }),
      },
    },
  });

  const filterOptions = useMemo(
    () => ({
      meetingType: MeetingTypeOption ?? [],
      meetingYear: Array.from({ length: 50 }, (_, i) => ({
        label: `${new Date().getFullYear() - i}`,
        value: `${new Date().getFullYear() - i}`,
      })),
    }),
    []
  );

  const columns: GridColDef<Data>[] = [
    {
      field: "meetingType",
      headerName: t("meetingType"),
      headerAlign: "center",
      align: "center",
      flex: 6,
      renderCell: ({ row }) => {
        const selectedMeeting =
          MeetingTypeOption?.find(
            (item) => item.value === parseInt(row.meetingType)
          ) ?? null;
        return selectedMeeting
          ? t(selectedMeeting.translateKey ?? selectedMeeting.label)
          : "-";
      },
    },
    {
      field: "meetingPlace",
      headerName: t("place"),
      headerAlign: "center",
      align: "center",
      flex: 5,
      renderCell: ({ row }) =>
        row?.meetingPlace?.length > 0 ? row.meetingPlace : "-",
    },
    {
      field: "meetingDate",
      headerName: t("meetingDate"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => formatDate(row.meetingDate),
      flex: 3,
    },
    {
      field: "status",
      headerName: t("status"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => {
        const isActive = row.status === "11" || row.status === "001";
        const isInactive = row.status === "008";

        if (!isActive && !isInactive) return "";

        return (
          <Typography
            className="status-pertubuhan-text"
            sx={{
              backgroundColor: "#fff",
              border: `2px solid var(--${isActive ? "success" : "error"})`,
              textAlign: "center",
            }}
          >
            {t(isActive ? "completed" : "inactive")}
          </Typography>
        );
      },
    },
    {
      field: "id",
      headerName: t("action"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => {
        return (
          <div
            style={{
              display: "flex",
              columnGap: "0.5rem",
            }}
          >
            <IconButton
              sx={{ width: "3rem", height: "3rem" }}
              onClick={() =>
                navigate(`./mesyuarat/${row.id}`, {
                  state: { societyId },
                })
              }
            >
              <EyeIcon />
            </IconButton>
          </div>
        );
      },
    },
  ];

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };
  const getParamsName = (filter: string) => {
    switch (filter) {
      case "meetingYear":
        return "meetingYear";
      default:
        return filter;
    }
  };
  const onFilterChange = (filter: string, value: number | string) => {
    const paramsName = getParamsName(filter);
    setFilterValues((prev) => ({
      ...prev,
      [paramsName]: value,
    }));
  };

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "1rem",
          borderRadius: "1rem",
          marginBottom: 1,
        }}
      >
        <FilterBar
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          selectedFilters={selectedFilters}
          onSelectedFiltersChange={handleSelectedFiltersChange}
        />
        <Box
          sx={{
            backgroundColor: "white",
            padding: "1rem",
            border: "1px solid #dfdfdf",
            borderRadius: "1rem",
            marginBottom: 1,
          }}
        >
          <DataGridUI
            {...dataGridProps}
            autoHeight
            {...{ columns }}
            noResultMessage={t("noData")}
          />
        </Box>
      </Box>
    </>
  );
};

export default MaklumatMesyuaratSection;
