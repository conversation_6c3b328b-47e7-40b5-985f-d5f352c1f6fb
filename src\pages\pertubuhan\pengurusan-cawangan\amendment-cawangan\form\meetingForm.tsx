import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  formatDateToDDMMYYYY,
  getLocalStorage,
  MeetingTypeOption,
} from "@/helpers";
// import { useSecretaryBranchReformContext } from "../Provider";

import {
  Box,
  Typography,
  Grid,
  FormControl,
  Select,
  TextField,
  useMediaQuery,
  Theme,
  MenuItem,
  IconButton,
} from "@mui/material";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useEffect, useState } from "react";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>ile<PERSON><PERSON><PERSON>, useMap } from "react-leaflet";
import { useSelector } from "react-redux";
import { EyeIcon } from "@/components/icons";
import { IMeetingOptions } from "@/types";
import { DisabledTextField } from "@/components";
import Input from "@/components/input/Input";
import { Controller, FieldValues, useForm } from "react-hook-form";
import { Meeting } from "@/pages/pertubuhan/pernyata-tahunan/interface";
import dayjs from "dayjs";
interface MeetingMemberAttendance {
  id: number;
  name: string;
  position: string;
}

interface MeetingForm {
  meetingId: string;
  branchId: number | null;
  branchNo: string | null;
  city: string;
  closing: string;
  confirmBy: string;
  district: string;
  id: number;
  mattersDiscussed: string;
  meetingAddress: string;
  meetingContent: string;
  meetingDate: string;
  meetingMemberAttendances: MeetingMemberAttendance[];
  meetingMethod: string;
  meetingMinute: string;
  meetingPlace: string;
  meetingPurpose: string;
  meetingTime: string;
  meetingTimeDurationMinutes: string;
  meetingTimeTo: string;
  meetingType: string;
  openingRemarks: string;
  otherMatters: string;
  platformType: string;
  postcode: string;
  providedBy: string;
  societyId: number;
  societyNo: string | null;
  state: string;
  totalAttendees: number;
}

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

export const MeetingForm: React.FC = () => {
  const { id, amendId, branchId } = useParams();
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const branchAmendRedux = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.data
  );
  const isView = useSelector(
    (state: { branchAmendData: any }) => state.branchAmendData.isView
  );
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [availableDateList, setAvailableDateList] = useState<string[]>([]);
  const [savedMeetingDate, setSavedMeetingDate] = useState("");
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [fileName, setFileName] = useState<string>("");
  const [fileUrl, setFileUrl] = useState<string>("");
  const [disabledNext, setDisableNext] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [filteredMeetings, setFilteredMeeting] = useState<any[]>();

  const {
    setValue,
    getValues,
    watch,
    control,
    reset: resetMeetingForm,
  } = useForm<FieldValues>({
    defaultValues: {
      meetingId: "",
      branchId: null,
      branchNo: null,
      city: "",
      closing: "",
      confirmBy: "",
      district: "",
      id: 0,
      mattersDiscussed: "",
      meetingAddress: "",
      meetingContent: "",
      meetingDate: "",
      meetingMemberAttendances: [],
      meetingMethod: "",
      meetingMinute: "",
      meetingPlace: "",
      meetingPurpose: "",
      meetingTime: "",
      meetingTimeDurationMinutes: "",
      meetingTimeTo: "",
      meetingType: "",
      openingRemarks: "",
      otherMatters: "",
      platformType: "",
      postcode: "",
      providedBy: "",
      societyId: 0,
      societyNo: null,
      state: "",
      totalAttendees: 0,
    },
  });

  const meetingDateExist = getValues("meetingDate");

  // const [meetingForm, setMeetingForm] = useState<MeetingForm>({
  //   meetingId: "",
  //   branchId: null,
  //   branchNo: null,
  //   city: "",
  //   closing: "",
  //   confirmBy: "",
  //   district: "",
  //   id: 0,
  //   mattersDiscussed: "",
  //   meetingAddress: "",
  //   meetingContent: "",
  //   meetingDate: "",
  //   meetingMemberAttendances: [],
  //   meetingMethod: "",
  //   meetingMinute: "",
  //   meetingPlace: "",
  //   meetingPurpose: "",
  //   meetingTime: "",
  //   meetingTimeDurationMinutes: "",
  //   meetingTimeTo: "",
  //   meetingType: "",
  //   openingRemarks: "",
  //   otherMatters: "",
  //   platformType: "",
  //   postcode: "",
  //   providedBy: "",
  //   societyId: 0,
  //   societyNo: null,
  //   state: "",
  //   totalAttendees: 0,
  // });

  // function reset() {
  //   setMeetingForm({
  //     meetingId: "",
  //     branchId: null,
  //     branchNo: null,
  //     city: "",
  //     closing: "",
  //     confirmBy: "",
  //     district: "",
  //     id: 0,
  //     mattersDiscussed: "",
  //     meetingAddress: "",
  //     meetingContent: "",
  //     meetingDate: "",
  //     meetingMemberAttendances: [],
  //     meetingMethod: "",
  //     meetingMinute: "",
  //     meetingPlace: "",
  //     meetingPurpose: "",
  //     meetingTime: "",
  //     meetingTimeDurationMinutes: "",
  //     meetingTimeTo: "",
  //     meetingType: "",
  //     openingRemarks: "",
  //     otherMatters: "",
  //     platformType: "",
  //     postcode: "",
  //     providedBy: "",
  //     societyId: 0,
  //     societyNo: null,
  //     state: "",
  //     totalAttendees: 0,
  //   });
  // }

  const meetingOptions: IMeetingOptions[] = getLocalStorage("meeting_list", []);

  const getMeetingName = (meetingId: number): string => {
    const meeting = meetingOptions.find((m) => m.id === meetingId);
    if (meeting) {
      return meeting.nameEn;
    }

    return "-";
  };

  const getMeetingMethod = (meetingId: number): string => {
    const meeting = meetingOptions.find((m) => m.id === meetingId);
    if (meeting) {
      return i18n.language === "my" ? meeting.nameBm : meeting.nameEn;
    }
    return "-";
  };
  const { data: addressList } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressListData = addressList?.data?.data || [];

  const getStateName = (stateCode: any) => {
    const stateName = addressListData.filter((i: any) => i.id == stateCode);
    return stateName[0]?.name;
  };

  const getDistrict = (val: any) => {
    const address = addressListData
      .filter((items: any) => items.id === Number(val))
      .map((item: any) => ({ value: item.id, label: item.name }));

    return address?.[0]?.label || "-";
  };

  // useCustom({
  //   url: `${API_URL}/society/meeting/search`,
  //   method: "get",
  //   config: {
  //     headers: {
  //       portal: localStorage.getItem("portal"),
  //       authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
  //     },
  //     query: {
  //       pageSize: 100,
  //       societyId: id,
  //       meetingDate: watch("meetingDate"),
  //     },
  //   },
  //   queryOptions: {
  //     enabled: !!watch("meetingDate"),
  //     onSuccess: (data) => {
  //       const meetingSearchlist = data?.data?.data?.data || [];
  //       if (meetingSearchlist.length > 0) {
  //         setMeetinglist(meetingSearchlist);
  //       }
  //     },
  //   },
  // });

  const { data: allMeetingData, isLoading: isAllMeetingDataLoading } =
    useCustom({
      url: `${API_URL}/society/meeting/findByBranchId/${branchId}`,
      method: "get",
      config: {
        headers: {
          portal: localStorage.getItem("portal"),
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
        },
      },
      queryOptions: {
        enabled: !!branchId,
        onSuccess: (data) => {
          const meetings = data?.data?.data || [];
          setMeetings(meetings);
          const availableList = meetings.map((item: any) => {
            return item.meetingDate;
          });
          setAvailableDateList(availableList);
          ///handle edit
          if (branchAmendRedux?.meetingDate) {
            const filteredMeeting = meetings.filter(
              (meeting: any) =>
                meeting.meetingDate.toString() ===
                dayjs(branchAmendRedux?.meetingDate, "DD-MM-YYYY").format(
                  "YYYY-MM-DD"
                )
            );
            setMeetingList(filteredMeeting);
          }
        },
      },
    });

  function setMeetingList(meetingList: Meeting[]) {
    if (isAllMeetingDataLoading) {
      return;
    }
    const formatMeetingOption = meetingList.map((item) => ({
      label: `${getMeetingLabel(
        Number(item.meetingType)
      )} (${formatDateToDDMMYYYY(item.meetingDate)})`,
      value: Number(item.id),
    }));
    setFilteredMeeting(formatMeetingOption);
    if (meetingList && meetingList.length > 0) {
      setIsDialogOpen(false);
    } else {
      setIsDialogOpen(true);
    }
  }

  useCustom({
    url: `${API_URL}/society/meeting/${watch("meetingId")}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      // enabled: !!meetingForm.meetingId,
      enabled: !!watch("meetingId"),
      onSuccess: (data) => {
        const meetingListById = data?.data?.data;
        if (meetingListById) {
          // setValue("meetingId", Number(meetingListById?.meetingId));
          setValue("branchId", meetingListById?.id);
          setValue("branchNo", meetingListById?.branchNo);
          setValue("district", meetingListById?.districtCode);
          setValue("id", meetingListById?.id);
          setValue("meetingAddress", meetingListById?.meetingAddress);
          setValue("meetingDate", meetingListById?.meetingDate);
          setValue("meetingMethod", meetingListById?.meetingMethod);
          setValue("meetiplatformTypengPlace", meetingListById?.platformType);
          setValue("meetingPurpose", meetingListById?.meetingPurpose);
          setValue("meetingTime", meetingListById?.meetingTime);
          setValue("meetingTimeTo", meetingListById?.meetingTimeTo);
          setValue("meetingPlace", meetingListById?.meetingPlace);
          setValue("state", meetingListById?.state);

          setValue("district", meetingListById?.district);
          setValue("city", meetingListById?.city);
          setValue("postcode", meetingListById?.postcode);
          setValue("totalAttendees", meetingListById?.totalAttendees);
          // setMeetingForm((prevState) => ({
          //   ...prevState,
          //   ...meetingListById,
          // }));
        }
      },
    },
  });

  const { mutate: updateMeetingData, isLoading: isLoadingUpdateMeetingData } =
    useCustomMutation();

  const updateMeeting = () => {
    updateMeetingData(
      {
        url: `${API_URL}/society/external/branchAmendment/update`,
        method: "patch",
        values: {
          id: amendId,
          meetingId: watch("meetingId"),
          meetingType: watch("meetingType"),
          meetingDate: watch("meetingDate"),
          meetingPlace: watch("meetingPlace"),
          meetingTime: watch("meetingTime"),
          totalAttendees: watch("totalAttendees"),
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: t("messageKeputusanPermohonanError"),
            type: "error",
          };
        },
      },
      {
        onSuccess() {
          setDisableNext(false);
        },
      }
    );
  };

  useEffect(() => {
    if (isView) {
      setDisableNext(false);
    }
  }, [isView]);

  const RecenterAutomatically = ({
    lat,
    lng,
  }: {
    lat: number;
    lng: number;
  }) => {
    const map = useMap();
    useEffect(() => {
      map.setView([lat, lng]);
    }, [lat, lng]);
    return null;
  };

  const [meetingCoords, setMeetingCoords] = useState<[number, number]>([
    2.745564, 101.707021,
  ]);

  const goNext = () => {
    navigate(
      `/pertubuhan/society/${id}/senarai/cawangan/branch-Info/amend/${amendId}/${branchId}`
    );
  };

  useCustom({
    url: "society/document/documentByParam",
    method: "get",
    queryOptions: {
      enabled: !!watch("meetingId"),
      onSuccess(data) {
        const fileInfo = data?.data?.data?.[0];
        if (fileInfo) {
          setFileName(fileInfo?.name);
          setFileUrl(fileInfo?.url);
        }
      },
    },
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        { field: "meetingId", operator: "eq", value: watch("meetingId") },
      ],
    },
  });

  useEffect(() => {
    if (branchAmendRedux) { 
      setValue("branchId", branchAmendRedux?.branchId);
      setValue("branchNo", branchAmendRedux?.branchNo);
      setValue("district", branchAmendRedux?.districtCode);
      setValue("id", branchAmendRedux?.id);
      setValue("meetingAddress", branchAmendRedux?.address);
      setValue("meetingDate", branchAmendRedux?.meetingDate);
      setValue("meetingId", Number(branchAmendRedux?.meetingId));
      setValue("meetingMethod", branchAmendRedux?.meetingMethod);
      setValue("meetiplatformTypengPlace", branchAmendRedux?.platformType); 
      setValue("meetingPurpose ", branchAmendRedux?.meetingPurpose);
      // setValue("meetingTime", branchAmendRedux?.meetingTime);
      // setValue("meetingTimeTo", branchAmendRedux?.meetingTimeTo);
      setValue("meetingPlace", branchAmendRedux?.meetingPlace);
      setValue("state", branchAmendRedux?.state);
      setValue("district", branchAmendRedux?.district);
      setValue("city", branchAmendRedux?.city);
      setValue("postcode", branchAmendRedux?.postcode);
      setValue("totalAttendees", branchAmendRedux?.totalAttendees);

      // if (allMeetingData) {
      //   const filteredMeeting = meetings.filter(
      //     (meeting) =>
      //       meeting.meetingDate.toString() ===
      //       dayjs(branchAmendRedux?.meetingDate).format("YYYY-MM-DD")
      //   );
      //   setMeetingList(filteredMeeting);
      // }

      // setMeetingForm((prevState) => ({
      // setMeetingForm((prevState) => ({
      //   ...prevState,
      //   meetingId: branchAmendRedux?.meetingId,
      //   branchId: branchAmendRedux?.id,
      //   branchNo: branchAmendRedux?.branchNo,
      //   district: branchAmendRedux?.districtCode,
      //   id: branchAmendRedux?.id,
      //   meetingAddress: branchAmendRedux?.address,
      //   meetingDate: branchAmendRedux?.meetingDate,
      //   meetingPlace: branchAmendRedux?.meetingPlace,
      // }));
    }
  }, [branchAmendRedux]);

  const getMeetingLabel = (value: number): string => {
    const meeting = MeetingTypeOption.filter((item: any) => item.id !== 1).find(
      (option) => Number(option.value) === Number(value)
    );
    return meeting ? meeting.label : "Unknown Meeting Type";
  };

  const goToMeeting = () => {
    navigate(`/pertubuhan/society/${id}/senarai/mesyuarat`);
  };

  const viewFile = (filePath: string) => {
    window.open(filePath, "_blank");
  };

  function resetChanges() {
    setFilteredMeeting([]);
    resetMeetingForm();
    setValue("meetingId", "");
  }

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          p: 3,
          borderRadius: "15px",
        }}
      >
        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Box sx={{ display: "flex", flexDirection: "column", gap: 1 }}>
            <Typography
              sx={{
                color: "#FF0000",
                fontSize: "12px",
                fontWeight: "500 !important",
              }}
            >
              {t("peringatan")} :
            </Typography>
            <Box
              sx={{
                color: "#666666",
                fontSize: "12px",
                fontWeight: "400 !important",
                display: "inline-block",
              }}
            >
              {t("reminderPindaanTitle1")}
              <span
                style={{ cursor: "pointer", color: "var(--primary-color)" }}
                onClick={goToMeeting}
              >
                {" "}
                {t("clickhere")}{" "}
              </span>
              {t("reminderPindaanTitle2")}
            </Box>
          </Box>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("meetingPindaanTitle")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12}>
              <Controller
                name="meetingDate"
                control={control}
                defaultValue={getValues("meetingDate")}
                render={({ field }) => (
                  <Input
                    isStandardSize
                    required
                    label={t("meetingDate")}
                    type="date"
                    availableDate={availableDateList}
                    value={
                      meetingDateExist ? meetingDateExist : savedMeetingDate
                    }
                    onChange={(e) => {
                      setSavedMeetingDate(e.target.value);
                      setValue("meetingDate", e.target.value);
                      resetChanges();
                      const filteredMeeting = meetings.filter(
                        (meeting) =>
                          meeting.meetingDate.toString() ===
                          dayjs(e.target.value).format("YYYY-MM-DD")
                      );
                      setMeetingList(filteredMeeting);
                    }}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="meetingId"
                control={control}
                defaultValue={getValues("meetingId")}
                render={({ field }) => (
                  <Input
                    {...field}
                    required
                    isStandardSize
                    type="select"
                    options={filteredMeetings}
                    value={Number(getValues("meetingId"))}
                    // disabled={filteredMeetings?.length === 0}
                    label={t("meetingList")}
                  />
                )}
              />
            </Grid>
            {/* Kaedah Mesyuarat*/}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("meetingMethod")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
            
              <DisabledTextField
                value={
                  meetingForm.meetingMethod
                    ? getMeetingMethod(parseInt(meetingForm.meetingMethod))
                    : "-"
                }
              />
            </Grid> */}
            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={
                  getValues("meetingMethod")
                    ? getMeetingMethod(parseInt(getValues("meetingMethod")))
                    : "-"
                }
                name="meetingMethod"
                label={t("meetingMethod")}
              />
            </Grid>
            {/* Jenis Platform*/}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("platformType")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField
                value={getMeetingName(parseInt(meetingForm.platformType))}
              />
            </Grid> */}
            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={getMeetingName(getValues("platformType"))}
                name="platformType"
                label={t("platformType")}
              />
            </Grid>
            {/* Tujuan Mesyuarat*/}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("meetingPurpose")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField value={meetingForm.meetingPurpose} />
              <Input
                disabled
                value={getValues("meetingPurpose")}
                name="meetingPurpose"
                label={t("meetingPurpose")}
              />
            </Grid> */}

            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={getValues("meetingPurpose")}
                name="meetingPurpose"
                label={t("meetingPurpose")}
              />
            </Grid>
            {/* Masa */}
            {/* 
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("Masa")}</Typography>
            </Grid>

            <Grid item xs={12} sm={9} gap={1} sx={{ display: "flex" }}>
              <Grid item xs={6}>
                <Input
                  disabled
                  value={getValues("meetingTime")}
                  name="meetingTime"
                  label={t("meetingPurpose")}
                />
              </Grid>
              <Grid item xs={6}>
                <DisabledTextField value={meetingForm.meetingTimeTo} />
              </Grid>
            </Grid> */}
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("Masa")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9} gap={1} sx={{ display: "flex" }}>
              <Grid item xs={6}>
                <DisabledTextField value={watch("meetingTime")} />
                {/* <Input 
                  isLabelNoSpace
                  isLabel={false}
                  disabled
                  value={getValues("meetingTime")}
                  name="meetingTime"
                /> */}
              </Grid>

              <Grid item xs={6}>
                <DisabledTextField value={watch("meetingTimeTo")} />
                {/* <DisabledTextField value={meetingForm.meetingTimeTo} /> */}
                {/* <Input 
                  isLabelNoSpace
                  isLabel={false}
                  disabled
                  value={getValues("meetingTimeTo")}
                  name="meetingTimeTo"
                /> */}
              </Grid>
            </Grid>
            {/* Nama tempat mesyuarat */}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("namaTempatMesyuarat")}
              </Typography>
            </Grid> */}
            {/* <Grid item xs={12} sm={9}>
              <DisabledTextField
                value={
                  meetingForm.meetingPlace ? meetingForm.meetingPlace : "-"
                }
              />
            </Grid> */}
            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={getValues("meetingPlace")}
                name="meetingPlace"
                label={t("namaTempatMesyuarat")}
              />
            </Grid>

            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("meetingLocation")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <Box>
                <MapContainer
                  center={meetingCoords}
                  zoom={13}
                  style={{
                    height: "150px",
                    width: "100%",
                    borderRadius: "8px",
                  }}
                >
                  <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />
                  <Marker position={meetingCoords} />
                  <RecenterAutomatically
                    lat={meetingCoords[0]}
                    lng={meetingCoords[1]}
                  />
                </MapContainer>
              </Box>
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("alamatTempatMesyuarat")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            {/* Alamat tempat mesyuarat*/}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>
                {t("meetingPlaceAddress")}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={9}> 
              <DisabledTextField value={meetingForm.meetingAddress ?? "-"} />
            </Grid> */}

            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={getValues("meetingAddress")}
                name="meetingAddress"
                label={t("meetingPlaceAddress")}
              />
            </Grid>
            {/* Negeri*/}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("negeri")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}> 
              <DisabledTextField
                value={
                  meetingForm.state ? getStateName(meetingForm.state) : "-"
                }
              />
            </Grid> */}

            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={
                  getValues("state") ? getStateName(getValues("state")) : "-"
                }
                name="state"
                label={t("negeri")}
              />
            </Grid>

            {/* Daerah*/}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("daerah")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}> 
              <DisabledTextField
                value={
                  meetingForm.district ? getDistrict(meetingForm.district) : "-"
                }
              />
            </Grid> */}

            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={
                  getValues("district")
                    ? getDistrict(getValues("district"))
                    : "-"
                }
                name="district"
                label={t("daerah")}
              />
            </Grid>
            {/* Bandar*/}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("bandar")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField value={meetingForm.city ?? "-"} /> 
            </Grid> */}
            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={getValues("city") ? getValues("city") : "-"}
                name="city"
                label={t("bandar")}
              />
            </Grid>
            {/* Poskod */}
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("postcode")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}> 
              <DisabledTextField value={meetingForm.postcode ?? "-"} />  
            </Grid> */}

            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={getValues("postcode") ? getValues("postcode") : "-"}
                name="postcode"
                label={t("postcode")}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("maklumatMesyuarat")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            {/* <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("totalAttendMember")}</Typography>
            </Grid>
            <Grid item xs={12} sm={9}>
              <DisabledTextField value={meetingForm.totalAttendees} />
            </Grid> */}
            <Grid item xs={12}>
              <Input
                isStandardSize
                disabled
                value={
                  getValues("totalAttendees")
                    ? getValues("totalAttendees")
                    : "-"
                }
                name="totalAttendees"
                label={t("totalAttendMember")}
              />
            </Grid>
          </Grid>
        </Box>

        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "8px",
            p: { xs: 2, sm: 3 },
            backgroundColor: "transparent",
            marginBottom: 3,
          }}
        >
          <Typography
            sx={{
              color: "var(--primary-color)",
              fontSize: "14px",
              fontWeight: "500 !important",
              mb: 2,
            }}
          >
            {t("meetingInformation")}
          </Typography>
          <Grid container spacing={2} sx={{ mt: 0 }}>
            <Grid item xs={12} sm={3}>
              <Typography sx={labelStyle}>{t("minitMesyuarat")}</Typography>
            </Grid>

            <Grid item xs={12} sm={9}>
              <Box
                sx={{
                  border: "1px solid #DADADA",
                  borderRadius: "8px",
                  p: 3,
                  display: "flex",
                  flexDirection: "column",
                  alignItems: "center",
                  gap: 1,
                  backgroundColor: "#FFFFFF",
                  cursor: "normal",
                  "&:hover": {
                    backgroundColor: "#F9FAFB",
                  },
                }}
              >
                <Box
                  sx={{
                    width: "100%",
                    display: "flex",
                    flexDirection: "column",
                    alignItems: "center",
                    gap: 1,
                  }}
                >
                  <Box
                    sx={{
                      width: "100%",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "space-between",
                    }}
                  >
                    <Box>
                      <Typography
                        sx={{
                          color: "#222222",
                          fontSize: "14px",
                          textAlign: "center",
                        }}
                      >
                        {fileName || "-"}
                      </Typography>
                    </Box>

                    <IconButton
                      onClick={() => viewFile(fileUrl)}
                      sx={{
                        padding: 0,
                      }}
                    >
                      <EyeIcon color="#666666" />
                    </IconButton>
                  </Box>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </Box>
        <Grid container spacing={2} sx={{ display: isView ? "none" : "block" }}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                width: isMobile ? "100%" : "auto",
              }}
              onClick={() => resetMeetingForm()}
            >
              {t("semula")}
            </ButtonOutline>

            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              onClick={updateMeeting}
              disabled={!watch("meetingId") || isLoadingUpdateMeetingData}
            >
              {t("update")}
            </ButtonPrimary>
          </Grid>
        </Grid>

        <Grid container spacing={2}>
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonOutline
              sx={{
                width: isMobile ? "100%" : "auto",
                display: isView ? "block" : "none",
              }}
              onClick={() => navigate(-1)}
            >
              {t("back")}
            </ButtonOutline>

            <ButtonPrimary
              variant="contained"
              sx={{ width: isMobile ? "100%" : "auto" }}
              disabled={disabledNext}
              onClick={() => goNext()}
            >
              {t("seterusnya")}
            </ButtonPrimary>
          </Grid>
        </Grid>
      </Box>
    </>
  );
};

export default MeetingForm;
