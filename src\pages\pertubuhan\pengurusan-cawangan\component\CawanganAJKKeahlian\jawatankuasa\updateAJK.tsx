import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  IconButton,
  Stack,
  Theme,
  Typography,
  useMediaQuery,
} from "@mui/material";
import { useCustomMutation, useNotification } from "@refinedev/core";
import { t } from "i18next";
import { useEffect, useState } from "react";
import { API_URL } from "@/api";
import { ButtonOutline, ButtonPrimary } from "@/components/button";
import { useNavigate, useParams } from "react-router-dom";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import useQuery from "@/helpers/hooks/useQuery";
// import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

import {
  Ajk,
  AjkNonCiizen,
  Meeting,
} from "../../CawanganPenyataTahunan/interface";
import Input from "@/components/input/Input";
import {
  ApplicationStatusEnum,
  MeetingTypeOption,
  OrganisationPositions,
} from "@/helpers/enums";
import { usejawatankuasaContext } from "./jawatankuasaProvider";
import ConfirmationDialog from "@/components/dialog/confirm";
import { Trans } from "react-i18next";
import dayjs from "dayjs";
import { DataTable, DisabledTextField, Label } from "@/components";
import { Controller, useForm } from "react-hook-form";
import { useSelector } from "react-redux";
import MessageDialog from "@/components/dialog/message";
import {
  DocumentUploadType,
  filterEmptyValuesOnObject,
  getLocalStorage,
  useMutation,
} from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import { useDispatch } from "react-redux";
import { RootState } from "@/redux/store";
import { setJawatankuasaAJKFormValues } from "@/redux/slices/jawatankuasaAJKFormSlice";

const updateAJK = () => {
  const navigate = useNavigate();
  const [availableDateList, setAvailableDateList] = useState<string[]>([]);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [filteredMeetings, setFilteredMeeting] = useState<Meeting[]>();
  const [meeting, setMeeting] = useState<Meeting>();
  const [meetingDoc, setMeetingDoc] = useState([]);
  const { mutate: deleteAJK, isLoading: isUpdateAJK } = useCustomMutation();
  const [committeeId, setCommitteeId] = useState<number | null>(null);
  const [citizenFlag, setCitizenFlag] = useState<boolean>(false);
  const [openConfirm, setOpenConfirm] = useState<boolean>(false);
  const [openConfirmSend, setOpenConfirmSend] = useState<boolean>(false);
  const [isAcknowledged, setIsAcknowledged] = useState<boolean>(false);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [appointmentDate, setAppointmentDate] = useState<string>("");
  const [uploadedIds, setUploadedIds] = useState<string[]>([]);
  const [oldFiles, setOldFiles] = useState<any[]>([]);
  const [nonCitizenAjkList, setNonCitizenAjkList] = useState<any[]>();
  const [totalListNonCitizen, setTotalListNonCitizen] = useState<number>(0);
  const [rowDataNonCitizen, setRowDataNonCitizen] = useState<any[]>([]);
  const dispatch = useDispatch();
  const jawatankuasaForm = useSelector(
    (state: RootState) => state.jawatankuasaAJKForm
  );

  const [totalList, setTotalList] = useState<number>(0);
  const [rowData, setRowData] = useState<any[]>([]);

  const { open } = useNotification();

  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { id: societyId } = useParams();
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);

  useQuery({
    url: `society/meeting/findByBranchId/${branchDataRedux.id}`,
    onSuccess: (data) => {
      const meetings = data?.data?.data || [];
      setMeetings(meetings);
      const availableList = meetings.map((item: any) => {
        return item.meetingDate;
      });
      setAvailableDateList(availableList);
    },
  });

  const form = useForm<any>();

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    setValue,
    getValues,
    watch,
    reset,
  } = form;

  const onSubmit = (data: any) => {
    if (isAcknowledged) {
      setOpenConfirmSend(true);
    }
  };

  const {
    // addressList,
    setSavedMeetingDate,
    fetchAddressList,
    savedMeetingDate,
    setSavedMeetingDetail,
    savedMeetingDetail,
    meetingId,
    setMeetingId,
    documentIds,
    setDocumentIds,
    setAppointmentDateG,
  } = usejawatankuasaContext();

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  const handleUploadComplete = (id: string) => {
    setUploadedIds((prev) => (prev.includes(id) ? prev : [...prev, id]));
  };
  const handleOldUploadedFiles = (files: any) => {
    setOldFiles(files);
  };

  const [ajk, setAjk] = useState<Ajk[]>();
  const addressList = getLocalStorage("address_list", []);
  useEffect(() => {
    setValue("type", 0);
    // fetchAddressList();
  }, []);

  useEffect(() => {
    setDocumentIds(uploadedIds);
  }, [uploadedIds]);

  useEffect(() => {
    setMeetingId(meeting?.id);
  }, [meeting]);

  useEffect(() => {
    setAppointmentDateG(appointmentDate);
  }, [appointmentDate]);

  const handleDaftarAJKBukanWn = () => {
    navigate(`../create-ajk-bukan-wn?`);
  };

  const handleEditAJK = (ajk: Ajk) => {
    navigate(`../kemaskini-ajk`, {
      state: {
        ajk: ajk,
        meetingData: savedMeetingDetail,
      },
    });
  };

  const handleEditAJKBukanWn = (ajk: AjkNonCiizen, view: Boolean = false) => {
    navigate(`../create-ajk-bukan-wn`, {
      state: {
        ajkNonCitizen: ajk,
        view: view,
      },
    });
  };

  const page = watch("page") || 1;
  const pageSize = watch("pageSize") || 10;

  const { fetch: callGetAJKList, isLoading: isLoadingAJKs } = useMutation({
    url: `society/committee/draft/getOrCreateByMeetingId?pageNo=${page}&pageSize=${pageSize}`,
    method: "post",
    showSuccessNotification: false,
    onSuccess: (data) => {
      if (data?.data?.status === "SUCCESS") {
        setTotalList(data?.data?.data?.total ?? 0);
        setRowData(data?.data?.data?.data ?? []);
        setAjk(data?.data?.data?.data);
      } else {
        return open?.({
          type: "error",
          message: t("error"),
          description: t(data?.data?.msg),
        });
      }
    },
  });

  const handleCheckMeetingArchive = () => {
    const type = watch("type") ? watch("type") : jawatankuasaForm.type;
    const effectiveSavedMeetingDate = savedMeetingDate || appointmentDate;
    const effectiveAppointmentDate = appointmentDate || savedMeetingDate;
    const effectiveMeetingId =
      jawatankuasaForm.savedMeetingDetail?.id || savedMeetingDetail?.id;
    const effectiveUploadedIds =
      jawatankuasaForm.uploadedIds?.length > 0
        ? jawatankuasaForm.uploadedIds
        : uploadedIds;

    dispatch(
      setJawatankuasaAJKFormValues({
        type,
        savedMeetingDate: effectiveSavedMeetingDate,
        savedMeetingDetail,
        appointmentDate: effectiveAppointmentDate,
        uploadedIds,
      })
    );

    const appointedDate =
      jawatankuasaForm?.savedMeetingDate ||
      savedMeetingDate ||
      jawatankuasaForm?.appointmentDate ||
      appointmentDate;

    const payload =
      jawatankuasaForm.type === "0"
        ? {
            societyId,
            meetingId: effectiveMeetingId,
            appointedDate,
            branchId: branchDataRedux.id,
          }
        : {
            societyId,
            documentId: effectiveUploadedIds,
            appointedDate,
            branchId: branchDataRedux.id,
          };

    callGetAJKList(payload);
  };

  useEffect(() => {
    if (
      jawatankuasaForm.type !== null &&
      societyId &&
      branchDataRedux.id &&
      (jawatankuasaForm.uploadedIds?.length > 0 ||
        jawatankuasaForm.savedMeetingDetail?.id)
    ) {
      handleCheckMeetingArchive();
    }
  }, [watch("page"), watch("pageSize")]);

  const handleConfirmDeleteAJK = (id: number, citizen: boolean) => {
    setCitizenFlag(citizen);
    setCommitteeId(id);
    setOpenConfirm(true);
  };

  useEffect(() => {
    if (meetings && meetings.length > 0 && savedMeetingDate) {
      const filteredMeeting = meetings.filter(
        (meeting) =>
          meeting.meetingDate.toString() ===
          dayjs(savedMeetingDate).format("YYYY-MM-DD")
      );
      setMeetingList(filteredMeeting);
    }
  }, [savedMeetingDate, meetings]);

  const handleDeleteAJK = () => {
    deleteAJK(
      {
        url: citizenFlag
          ? `${API_URL}/society/committee/${committeeId}/delete`
          : `${API_URL}/society/nonCitizenCommittee/${committeeId}`,
        method: citizenFlag ? "put" : "delete",
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
      },
      {
        onSuccess: () => {
          setShouldFetch(true);
          fetchNonCitizenAjkList({
            filters: [
              {
                field: "societyId",
                operator: "eq",
                value: societyId,
              },
              {
                field: "appointedDate",
                operator: "eq",
                value: savedMeetingDate ? savedMeetingDate : appointmentDate,
              },
              {
                field: "branchId",
                value: branchDataRedux?.id,
                operator: "eq",
              },
              {
                field: "documentId",
                operator: "eq",
                value: documentIds,
              },
              {
                field: "meetingId",
                operator: "eq",
                value: meetingId,
              },
            ],
          });
          setOpenConfirm(false);
        },
      }
    );
  };

  const handleNavigate = (url: string) => {
    navigate(url);
  };

  const { refetch: fetchMeetingDocs } = useQuery({
    url: `society/document/documentByParam`,
    filters: [{ field: "meetingId", operator: "eq", value: meeting?.id }],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      const docs = data?.data?.data || [];
      setMeetingDoc(docs);
    },
  });

  function setMeetingList(meetingList: Meeting[]) {
    setFilteredMeeting(meetingList);
    if (meetingList && meetingList.length > 0) {
      setIsDialogOpen(false);
    } else {
      setIsDialogOpen(true);
    }
  }

  function resetChanges() {
    setSavedMeetingDetail(undefined);
    setMeeting(undefined);
    // setIsSavedStatus(false);
  }

  useEffect(() => {
    if (oldFiles) {
      const ids = oldFiles.map((file) => file.id);
      setUploadedIds(ids);
    }
  }, [oldFiles]);

  useEffect(() => {
    if (savedMeetingDetail) {
      if (jawatankuasaForm.type === "1") {
        fetchMeetingDocs(); // Refetch the data when meetingId changes
      }
    }
  }, [savedMeetingDetail]); // Trigger refetch when meetingId changes

  const { fetch: sendUpdate, isLoading } = useMutation({
    url: `society/committee/draft/submitAjk`,
    method: "put",
    onSuccess: () => {
      setOpenConfirmSend(false);
      navigate(-1);
    },
  });

  const sendAJK = () => {
    let payload: {
      societyId?: string | number;
      committeeDraftId?: any[];
      meetingId?: string | number;
      documentId?: any[];
      branchId?: string | number;
    } = {};

    if (ajk && ajk?.length > 0) {
      // const NonCitizenIds = nonCitizenAjkList?.map((item) => item.id);
      const ids = ajk?.map((item) => item.id);

      payload.societyId = societyId;
      payload.committeeDraftId = ids;
      payload.branchId = branchDataRedux?.id;

      if (jawatankuasaForm.type === "0") {
        payload.meetingId = meeting?.id;
      } else {
        payload.documentId = uploadedIds;
      }
      const filterPayload = filterEmptyValuesOnObject(payload);
      sendUpdate(filterPayload);
    }
  };

  const shouldDisable = (() => {
    if (
      nonCitizenAjkList?.some(
        (item: any) => Number(item.applicationStatusCode) === 1
      )
    ) {
      return true; // if any is 1 (not hantar) dont allow
    }

    if (!ajk || ajk.length <= 0) return true;

    if (jawatankuasaForm.type === "0") {
      return savedMeetingDetail ? false : true;
    }

    if (jawatankuasaForm.type === "1") {
      return uploadedIds?.length > 0 ? false : true;
    }

    return true;
  })();

  function goMeetingPage() {
    navigate(`../../../mesyuarat`);
  }

  const { mutate: editNonCitizenIds, isLoading: isLoadingEdit } =
    useCustomMutation();

  const EditNonCitizenIds: (ids: any) => void = (ids) => {
    editNonCitizenIds(
      {
        url: `${API_URL}/society/nonCitizenCommittee/update-by-list`,
        method: "put",
        values: { ids },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          fetchNonCitizenAjkList({
            filters: [
              {
                field: "societyId",
                operator: "eq",
                value: societyId,
              },
              {
                field: "branchId",
                operator: "eq",
                value: branchDataRedux?.id,
              },
              {
                field: "appointedDate",
                operator: "eq",
                value: savedMeetingDate ? savedMeetingDate : appointmentDate,
              },
              {
                field: "documentId",
                operator: "eq",
                value: documentIds,
              },
              {
                field: "meetingId",
                operator: "eq",
                value: meetingId,
              },
            ],
          });
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const hantarNonCitizen = () => {
    const ids = nonCitizenAjkList
      ?.filter((item) => Number(item.applicationStatusCode) === 1)
      .map((item) => item.id);
    EditNonCitizenIds(ids);
  };

  const {
    data: nonCitizenList,
    refetch: fetchNonCitizenAjkList,
    isLoading: isLoadingNonCitizenAJKList,
  } = useQuery({
    url: `society/nonCitizenCommittee/getAll`,
    filters: [
      { field: "societyId", operator: "eq", value: societyId },
      { field: "branchId", operator: "eq", value: branchDataRedux?.id },
    ],
    autoFetch: false, // Disable auto-fetch on mount
    onSuccess: (data) => {
      fetchAddressList();
      setNonCitizenAjkList(data?.data?.data?.data || []);
      setTotalListNonCitizen(data?.data?.data?.total ?? 0);
      setRowDataNonCitizen(data?.data?.data?.data ?? []);
    },
  });

  const pageNonCitizen = watch("pageNonCitizen") || 1;
  const pageSizeNonCitizen = watch("pageSizeNonCitizen") || 10;

  useEffect(() => {
    if (savedMeetingDate || appointmentDate) {
      fetchNonCitizenAjkList({
        filters: [
          {
            field: "branchId",
            value: branchDataRedux?.id,
            operator: "eq",
          },
          {
            field: "societyId",
            operator: "eq",
            value: societyId,
          },
          {
            field: "appointedDate",
            operator: "eq",
            value: savedMeetingDate ? savedMeetingDate : appointmentDate,
          },
          {
            field: "documentId",
            operator: "eq",
            value: documentIds,
          },
          {
            field: "meetingId",
            operator: "eq",
            value: meetingId,
          },
        ],
      });
    } else {
      setTotalListNonCitizen(0);
      setRowDataNonCitizen([]);
    }
  }, [savedMeetingDate, appointmentDate, meetingId, documentIds]);

  useEffect(() => {
    if (shouldFetch === true && (savedMeetingDate || appointmentDate)) {
      fetchNonCitizenAjkList({
        filters: [
          {
            field: "societyId",
            operator: "eq",
            value: societyId,
          },
          {
            field: "appointedDate",
            operator: "eq",
            value: savedMeetingDate ? savedMeetingDate : appointmentDate,
          },
          {
            field: "documentId",
            operator: "eq",
            value: documentIds,
          },
          {
            field: "meetingId",
            operator: "eq",
            value: meetingId,
          },
        ],
      });
      setShouldFetch(false);
    } else {
      setTotalListNonCitizen(0);
      setRowDataNonCitizen([]);
    }
  }, [shouldFetch]);

  const column = [
    {
      field: "designationCode",
      headerName: t("position"),
      flex: 1,
      align: "left",
      headerAlign: "left",
      renderCell: (params: any) => {
        const row = params?.row;
        const matched = OrganisationPositions.find(
          (pos) => pos.value === Number(row?.designationCode)
        );
        return matched ? t(matched.label) : "-";
      },
    },
    {
      field: "name",
      headerName: t("name"),
      width: 300,
      align: "center",
      headerAlign: "center",
      cellClassName: "custom-cell",
    },
    {
      field: "email",
      headerName: t("email"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      cellClassName: "custom-cell",
    },
    {
      field: "residentialStateCode",
      headerName: t("negeri"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return addressList.find(
          (item: any) => item.id.toString() === row?.residentialStateCode
        )?.name;
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      width: 100,
      sortable: false,
      disableColumnMenu: true,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;

        return (
          <Box>
            <IconButton onClick={() => handleEditAJK(row)}>
              <EditIcon
                sx={{
                  color: "var(--primary-color)",
                  width: "1rem",
                  height: "1rem",
                }}
              />
            </IconButton>
          </Box>
        );
      },
    },
  ];

  const columnNonCitizenAjk = [
    {
      field: "designationCode",
      headerName: t("position"),
      flex: 1,
      align: "left",
      headerAlign: "left",
      renderCell: (params: any) => {
        const row = params?.row;
        const matched = OrganisationPositions.find(
          (pos) => pos.value === Number(row?.designationCode)
        );
        return matched ? t(matched.label) : "-";
      },
    },
    {
      field: "name",
      headerName: t("name"),
      width: 300,
      align: "center",
      headerAlign: "center",
      cellClassName: "custom-cell",
    },
    {
      field: "applicationStatusCode",
      headerName: t("statusPermohonan"),
      flex: 1,
      align: "center",
      headerAlign: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return t(ApplicationStatusEnum[row?.applicationStatusCode]);
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      width: 100,
      sortable: false,
      disableColumnMenu: true,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;

        return (
          <Box>
            {row?.applicationStatusCode == "3" ||
            row?.applicationStatusCode == "4" ? (
              <IconButton onClick={() => handleEditAJKBukanWn(row, true)}>
                <EyeIcon />
              </IconButton>
            ) : (
              <>
                <IconButton
                  disabled={!meetingId && (documentIds?.length ?? 0) < 1}
                  onClick={() => handleEditAJKBukanWn(row)}
                >
                  <EditIcon
                    sx={{
                      color:
                        !meetingId && (documentIds?.length ?? 0) < 1
                          ? "var(--gray)"
                          : "var(--primary-color)",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>
                <IconButton
                  onClick={() => handleConfirmDeleteAJK(row?.id, false)}
                >
                  <TrashIcon
                    sx={{
                      color: "var(--error)",
                      width: "1rem",
                      height: "1rem",
                    }}
                  />
                </IconButton>
              </>
            )}
          </Box>
        );
      },
    },
  ];

  const handleTypeChange = (value: any) => {
    setValue("type", value);
    setSavedMeetingDate("");
    setMeeting(undefined);
    setRowData([]);
    setAppointmentDate("");
    setUploadedIds([]);
    setSavedMeetingDetail(undefined);
    setTotalList(0);
    dispatch(
      setJawatankuasaAJKFormValues({
        type: value,
        savedMeetingDate: null,
        savedMeetingDetail: null,
        appointmentDate: null,
        uploadedIds: [],
        citizenAJKs: [],
        branchId: branchDataRedux?.id,
      })
    );
  };

  useEffect(() => {
    if (jawatankuasaForm.type !== null) {
      setAppointmentDate(jawatankuasaForm.appointmentDate);
      setUploadedIds(jawatankuasaForm.uploadedIds);
      setSavedMeetingDate(jawatankuasaForm.savedMeetingDate);
      setSavedMeetingDetail(jawatankuasaForm.savedMeetingDetail);
      setMeeting(jawatankuasaForm.savedMeetingDetail);

      handleCheckMeetingArchive();
      fetchNonCitizenAjkList({
        filters: [
          {
            field: "branchId",
            value: branchDataRedux?.id,
            operator: "eq",
          },
          {
            field: "societyId",
            operator: "eq",
            value: societyId,
          },
          {
            field: "appointedDate",
            operator: "eq",
            value: jawatankuasaForm?.savedMeetingDate
              ? jawatankuasaForm?.savedMeetingDate
              : jawatankuasaForm?.appointmentDate,
          },
          {
            field: "documentId",
            operator: "eq",
            value: jawatankuasaForm?.uploadedIds,
          },
          {
            field: "meetingId",
            operator: "eq",
            value: jawatankuasaForm?.savedMeetingDetail?.id,
          },
        ],
      });
    } else {
      handleTypeChange("0");
    }
  }, []);

  return (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <span style={{ color: "red", fontSize: "14px" }}>
            {t("peringatan")} :
          </span>
          <ul
            style={{
              color: "#666666",
              fontSize: "14px",
              paddingInlineStart: "15px",
            }}
          >
            <li>{t("ajk_jawatankuasa_peringatan_1_point1")}</li>
            <li>
              <Trans
                i18nKey="ajk_jawatankuasa_peringatan_1_point2"
                components={{
                  1: (
                    <span
                      style={{
                        color: "var(--primary-color)",
                        fontSize: "14px",
                        cursor: "pointer",
                      }}
                      onClick={() =>
                        handleNavigate("/pertubuhan/pembaharuan-setiausaha")
                      }
                    ></span>
                  ),
                  2: (
                    <span
                      style={{
                        color: "var(--primary-color)",
                        fontSize: "14px",
                        cursor: "pointer",
                      }}
                      onClick={() =>
                        handleNavigate("/pertubuhan/pembaharuan-setiausaha")
                      }
                    ></span>
                  ),
                }}
              />
            </li>
            <li>
              <Trans
                i18nKey="ajk_jawatankuasa_peringatan_1_point3"
                components={{
                  1: (
                    <span
                      style={{
                        color: "var(--primary-color)",
                        fontSize: "14px",
                        cursor: "pointer",
                      }}
                      onClick={() =>
                        handleNavigate(
                          `/pertubuhan/society/${societyId}/senarai/mesyuarat`
                        )
                      }
                    ></span>
                  ),
                }}
              />
            </li>
            <li
              style={{
                color: "red",
              }}
            >
              {t("ajk_jawatankuasa_peringatan_1_point4")}
            </li>
          </ul>
        </Box>

        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("maklumatMesyuaratPelantikanAjk")}
          </Typography>
          <Input
            type="radio"
            name="type"
            label={t("appointmentInfo")}
            value={jawatankuasaForm.type}
            radioSquare={true}
            options={[
              { value: "0", label: t("meeting") },
              { value: "1", label: t("appointment") },
            ]}
            onChange={(e) => {
              const value = e.target.value;
              handleTypeChange(value);
            }}
          />
          {jawatankuasaForm.type === "0" ? (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={12}>
                <Input
                  required
                  label={t("meetingDate")}
                  type="date"
                  availableDate={availableDateList}
                  value={savedMeetingDate}
                  onChange={(e) => {
                    setSavedMeetingDate(e.target.value);
                    resetChanges();
                    const filteredMeeting = meetings.filter(
                      (meeting) =>
                        meeting.meetingDate.toString() ===
                        dayjs(e.target.value).format("YYYY-MM-DD")
                    );
                    setMeetingList(filteredMeeting);
                  }}
                />
              </Grid>

              {filteredMeetings && filteredMeetings.length > 0 ? (
                <>
                  <Grid item xs={12} sm={12}>
                    <Input
                      label={t("meetingList")}
                      type="select"
                      alignItems="start"
                      value={savedMeetingDetail?.id}
                      onChange={(e) => {
                        // Find the selected meeting based on the value from the select input
                        const selectedMeeting = meetings.find(
                          (meeting) => meeting.id === e.target.value
                        );
                        if (selectedMeeting) {
                          setSavedMeetingDetail(selectedMeeting);
                          setMeeting(selectedMeeting); // Pass selected meeting to parent
                          dispatch(
                            setJawatankuasaAJKFormValues({
                              type: "0",
                              savedMeetingDate: savedMeetingDate ?? "",
                              savedMeetingDetail: selectedMeeting ?? null,
                              appointmentDate: null,
                              uploadedIds: uploadedIds ?? [],
                              citizenAJKs: [],
                            })
                          );
                        }
                      }}
                      options={filteredMeetings.map((meeting) => ({
                        value: meeting.id,
                        label: `${
                          MeetingTypeOption.find(
                            (option) =>
                              Number(option.value) ===
                              Number(meeting.meetingType)
                          )?.label ?? "-"
                        } (${dayjs(meeting.meetingDate).format("DD-MM-YYYY")})`,
                      }))}
                    />
                  </Grid>
                  {savedMeetingDetail ? (
                    <>
                      <Grid item xs={12} sm={12}>
                        <Input
                          alignItems="start"
                          label={t("bilanganAhliYangHadir")}
                          disabled
                          value={savedMeetingDetail?.totalAttendees}
                        />
                      </Grid>
                      <Grid item xs={12} sm={4}>
                        <Typography
                          variant="body1"
                          sx={{
                            color: "#666666",
                            fontWeight: "400 !important",
                            fontSize: "14px",
                          }}
                        >
                          {t("minitMesyuarat")}
                        </Typography>
                      </Grid>
                      <Grid item xs={12} sm={8}>
                        {meetingDoc.length > 0 ? (
                          meetingDoc.map((doc: any, index: any) => (
                            <Box
                              sx={{
                                border: "1px solid #DADADA",
                                borderRadius: "8px",
                                p: 3,
                                display: "flex",
                                flexDirection: "column",
                                alignItems: "center",
                                gap: 1,
                                backgroundColor: "#FFFFFF",
                                cursor: "normal",
                                "&:hover": {
                                  backgroundColor: "#F9FAFB",
                                },
                              }}
                            >
                              <Box
                                sx={{
                                  width: "100%",
                                  display: "flex",
                                  flexDirection: "column",
                                  alignItems: "center",
                                  gap: 1,
                                }}
                              >
                                <Box
                                  sx={{
                                    width: "100%",
                                    display: "flex",
                                    alignItems: "center",
                                    justifyContent: "space-between",
                                  }}
                                >
                                  <Box>
                                    <Typography
                                      sx={{
                                        color: "#222222",
                                        fontSize: "14px",
                                        textAlign: "center",
                                      }}
                                    >
                                      {doc?.name}
                                    </Typography>
                                  </Box>

                                  <IconButton
                                    onClick={() =>
                                      window.open(doc.url, "_blank")
                                    }
                                    sx={{
                                      padding: 0,
                                    }}
                                  >
                                    <EyeIcon color="#666666" />
                                  </IconButton>
                                </Box>
                              </Box>
                            </Box>
                          ))
                        ) : (
                          <DisabledTextField value={t("noData")} />
                        )}
                      </Grid>
                    </>
                  ) : null}
                </>
              ) : null}
            </Grid>
          ) : (
            <Grid container spacing={2}>
              <Grid item xs={12} sm={12}>
                <Input
                  required
                  label={t("dateOfAppointmentLetter")}
                  type="date"
                  minDate={dayjs().format("YYYY-MM-DD")}
                  availableDate={availableDateList}
                  value={savedMeetingDate}
                  onChange={(e) => {
                    setAppointmentDate(e.target.value);
                    resetChanges();
                  }}
                />
              </Grid>
              {societyId && branchDataRedux?.id ? (
                <Grid item xs={12} sm={12}>
                  <Box sx={{ display: "flex", minWidth: "100%", mb: 1 }}>
                    <Box sx={{ flex: 3.2 }}>
                      <Label text={t("appointmentLetter")} />
                    </Box>
                    {appointmentDate ? (
                      <Box sx={{ flex: 6.3 }}>
                        <FileUploader
                          key={appointmentDate}
                          type={DocumentUploadType.CITIZEN_COMMITTEE}
                          branchId={branchDataRedux.id}
                          societyId={Number(societyId)}
                          disabled={!appointmentDate}
                          ajkAppointmentLetterDate={appointmentDate}
                          onUploadComplete={handleUploadComplete}
                          hasOldFiles={handleOldUploadedFiles}
                          validTypes={[
                            "text/plain",
                            "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                            "application/msword",
                            "application/pdf",
                          ]}
                        />
                      </Box>
                    ) : null}
                  </Box>
                </Grid>
              ) : null}
            </Grid>
          )}
          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary
              disabled={
                (jawatankuasaForm.type === "0" && !savedMeetingDetail) ||
                (jawatankuasaForm.type === "1" && uploadedIds?.length === 0)
              }
              onClick={handleCheckMeetingArchive}
            >
              {t("save")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            textAlign: "center",
            p: 3,
            mb: 2,
          }}
        >
          <Typography
            variant="subtitle1"
            sx={{
              color: "var(--primary-color)",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {t("bilanganAhliJawatankuasaTerkini")}
          </Typography>
          <Typography
            sx={{
              color: "#666666",
              fontSize: 18,
              fontWeight: "500 !important",
            }}
          >
            {ajk?.length} Orang
          </Typography>
        </Box>

        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {t("ajkList")}
          </Typography>

          <DataTable
            columns={column as any}
            rows={rowData}
            page={page}
            rowsPerPage={pageSize}
            totalCount={totalList}
            onPageChange={(newPage) => setValue("page", newPage)}
            onPageSizeChange={(newPageSize) => {
              setValue("page", 1);
              setValue("pageSize", newPageSize);
            }}
            isLoading={isLoadingAJKs}
          />
        </Box>
      </Box>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <span style={{ color: "red", fontSize: "14px" }}>
            {t("peringatan")} :
          </span>
          <ul
            style={{
              color: "#666666",
              fontSize: "14px",
              paddingInlineStart: "15px",
            }}
          >
            <li>{t("ajk_jawatankuasa_peringatan_2_point1")}</li>
            <li>{t("ajk_jawatankuasa_peringatan_2_point2")}</li>
            <li>{t("ajk_jawatankuasa_peringatan_2_point3")}</li>
          </ul>
        </Box>
        <Box>
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              textAlign: "center",
              p: 3,
              mb: 2,
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                color: "var(--primary-color)",
                fontSize: 18,
                fontWeight: "500 !important",
              }}
            >
              {t("bilanganAhliJawatankuasaBukanWnTerkini")}
            </Typography>
            <Typography
              sx={{
                color: "#666666",
                fontSize: 18,
                fontWeight: "500 !important",
              }}
            >
              {
                nonCitizenAjkList?.filter(
                  (ajk) => ajk.applicationStatusCode == "3"
                ).length
              }{" "}
              Orang
            </Typography>
          </Box>

          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("senaraiAjkBukanWn")}
            </Typography>
            <Stack
              direction="row"
              spacing={2}
              mt={2}
              sx={{ pl: 1, width: "100%" }}
              justifyContent="flex-end"
            >
              <ButtonPrimary
                sx={{
                  bgcolor: "transparent",
                  color: "#666666",
                  boxShadow: "none",
                  border: "1px solid #67D1D1",
                  fontSize: "12px",
                  fontWeight: "500 !important",
                }}
                disabled={!meetingId && (documentIds?.length ?? 0) < 1}
                onClick={handleDaftarAJKBukanWn}
              >
                {t("registerNonCitizenAJK")}
              </ButtonPrimary>
            </Stack>

            <DataTable
              columns={columnNonCitizenAjk as any}
              rows={rowDataNonCitizen}
              page={pageNonCitizen}
              rowsPerPage={pageSizeNonCitizen}
              totalCount={totalListNonCitizen}
              onPageChange={(newPage) => setValue("pageNonCitizen", newPage)}
              onPageSizeChange={(newPageSize) => {
                setValue("pageNonCitizen", 1);
                setValue("pageSizeNonCitizen", newPageSize);
              }}
              isLoading={isLoadingNonCitizenAJKList}
              clientPaginationMode={true}
            />
          </Box>
          <form onSubmit={handleSubmit(onSubmit)}>
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                flexDirection: isMobile ? "column" : "row",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline
                sx={{
                  bgcolor: "white",
                  "&:hover": { bgcolor: "white" },
                  width: isMobile ? "100%" : "auto",
                }}
                // onClick={handleReset}
              >
                {t("semula")}
              </ButtonOutline>
              <ButtonPrimary
                disabled={
                  !nonCitizenAjkList ||
                  nonCitizenAjkList.length === 0 ||
                  isLoadingEdit ||
                  nonCitizenAjkList?.every(
                    (item) => Number(item.applicationStatusCode) !== 1
                  )
                }
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                }}
                onClick={hantarNonCitizen}
              >
                {t("hantar")}
              </ButtonPrimary>
            </Grid>
          </form>
        </Box>
      </Box>

      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Controller
            name="acknowledgeAjk"
            control={control}
            rules={{
              required: t("pleaseSelect"),
            }}
            render={({ field, fieldState: { error } }) => (
              <>
                <FormControlLabel
                  control={
                    <Checkbox
                      {...field}
                      checked={field.value}
                      onChange={(e) => {
                        const checked = e.target.checked;
                        field.onChange(checked);
                        setIsAcknowledged(checked);
                      }}
                      sx={{ fontWeight: 100 }}
                    />
                  }
                  label={
                    <Typography
                      style={{
                        fontWeight: "normal",
                        fontSize: "14px",
                        color: "#333",
                        lineHeight: "1.5",
                      }}
                    >
                      {t("jawatankuasaConfirmation")}{" "}
                      <span style={{ color: "red" }}>*</span>
                    </Typography>
                  }
                />
                {error && (
                  <Typography variant="caption" color="error">
                    {t("pleaseSelect")}
                  </Typography>
                )}
              </>
            )}
          />
        </Box>
        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonOutline
            sx={{
              bgcolor: "white",
              "&:hover": { bgcolor: "white" },
              width: isMobile ? "100%" : "auto",
            }}
            onClick={() => navigate(-1)}
          >
            {t("kembali")}
          </ButtonOutline>
          <ButtonPrimary
            disabled={!isAcknowledged ? true : shouldDisable}
            variant="contained"
            onClick={handleSubmit(onSubmit)}
            sx={{
              width: isMobile ? "100%" : "auto",
            }}
          >
            {t("update")}
          </ButtonPrimary>
        </Grid>
      </Box>

      <ConfirmationDialog
        status={1}
        open={openConfirm}
        onClose={() => setOpenConfirm(false)}
        title={t("confirmDelete")}
        message={`${t("deleteConfirmationMessage")}`}
        onConfirm={handleDeleteAJK}
        isMutation={isUpdateAJK}
        onCancel={() => setOpenConfirm(false)}
      />

      <ConfirmationDialog
        status={1}
        open={openConfirmSend}
        onClose={() => setOpenConfirmSend(false)}
        title={t("confirmSend")}
        message={`${t("confirmSend")}`}
        onConfirm={sendAJK}
        onCancel={() => setOpenConfirmSend(false)}
      />

      <MessageDialog
        open={isDialogOpen}
        onClickFunction={() => goMeetingPage()}
        buttonText={t("Viewlist")}
        onClose={() => setIsDialogOpen(false)}
        message={t("createMeetingReminder")}
      />
    </>
  );
};

export default updateAJK;
