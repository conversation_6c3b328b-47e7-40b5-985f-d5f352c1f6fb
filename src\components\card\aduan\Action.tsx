import { Box, Grid, MenuItem, Typography, useTheme } from "@mui/material";
import { useTranslation } from "react-i18next";
import { TextFieldControllerFormik } from "@/components/input";
import { ComplaintTypeOptions } from "@/helpers";

export const CardAduanAction = () => {
  const { t } = useTranslation();
  const theme = useTheme();

  const error = theme.palette.error.main;
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  return (
    <Box
      sx={{
        borderRadius: "1rem",
        backgroundColor: "white",
        boxShadow: "0 0 0.75rem rgba(234, 232, 232, 0.4)",
        padding: "1rem",
        display: "flex",
        flexDirection: "column",
        rowGap: "1rem",
        marginBottom: "0.5rem"
      }}
    >
      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "0.5rem",
          padding: "1.5rem"
        }}
      >
        <Typography className="title" mb="1.25rem">
          {t("complaintAction")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item md={4} xs={12}>
            <Typography sx={labelStyle}>
              {t("typeOfAction")}
            </Typography>
          </Grid>
          <Grid item md={8} xs={12}>
            <TextFieldControllerFormik
              name="branchName"
              helperTextComponentPlacement="INSIDE"
              helperTextFallbackValue=""
              select
            >
              {ComplaintTypeOptions.map((item, index) =>
                <MenuItem key={`gender-${index}`} value={item.value}>{t(item.name)}</MenuItem>
              )}
            </TextFieldControllerFormik>
          </Grid>
          <Grid item md={4} xs={12}>
            <Typography sx={labelStyle}>
              {t("remarks")}{" "}
              <span style={{ color: error }}>*</span>
            </Typography>
          </Grid>
          <Grid item md={8} xs={12}>
            <TextFieldControllerFormik
              name="branchName"
              helperTextComponentPlacement="INSIDE"
              helperTextFallbackValue=""
              multiline
              rows={4}
            />
          </Grid>
        </Grid>
      </Box>
    </Box>
  )
}
