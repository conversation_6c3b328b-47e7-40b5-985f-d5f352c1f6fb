import { Info } from "@mui/icons-material"
import { Box, Button, CircularProgress, Grid, IconButton, MenuItem, Popover, Typography, useTheme } from "@mui/material"
import { Form, useFormikContext } from "formik"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { TextFieldControllerFormik } from "@/components/input"
import { GenderType, IdTypes } from "@/helpers"
import { FeedbackAduanPublicCheckIdentityRequestBody } from "./PublicCheckIdentity"
import { useControllerFormManagementCoreConfirmationBeforeSubmitFormik } from "@/controllers"
import { DatePickerFormik } from "@/components/input/DatePickerFormik"
import { TimePickerFormik } from "@/components/input/TimePickerFormik"

export type FeedbackAduanExternalCreateRequestBody<
  Base extends FeedbackAduanPublicCheckIdentityRequestBody = FeedbackAduanPublicCheckIdentityRequestBody
> = Base & {
  /**
   * @todo replace attribute for "Jantina"
   */
  gender: string

  /**
   * @todo replace attribute for "bangsa"
   */
  bangsa: string

  /**
   * @todo replace attribute for "pekerjaan"
   */
  pekerjaan: string

  /**
   * @todo replace attribute for "umur"
   */
  umur: string

  /**
   * @todo replace attribute for "kewarganegaraan"
   */
  kewarganegaraan: string

  /**
   * @todo replace attribute for "alamat"
   */
  address: string

  /**
   * @todo replace attribute for "poskod"
   */
  postcode: string

  /**
   * @todo replace attribute for "daerah"
   */
  daerah: string

  /**
   * @todo replace attribute for "bandar"
   */
  bandar: string

  /**
   * @todo replace attribute for "negeri"
   */
  negeri: string

  /**
   * @todo replace attribute for "email"
   */
  email: string

  /**
   * @todo replace attribute for "no tel"
   */
  phoneNumber: string

  /**
   * @todo replace attribute for "no tel rumah"
   */
  residencePhoneNumber: string
}

export interface FormFeedbackAduanExternalCreateProps<
  ReqBody extends FeedbackAduanExternalCreateRequestBody = FeedbackAduanExternalCreateRequestBody
> {
  initialValue: ReqBody

  /**
   * @default false
   */
  viewOnly?: boolean
}

export const FormFeedbackAduanExternalCreate = <
  ReqBody extends FeedbackAduanExternalCreateRequestBody = FeedbackAduanExternalCreateRequestBody,
  PropType extends FormFeedbackAduanExternalCreateProps<ReqBody> = FormFeedbackAduanExternalCreateProps<ReqBody>
>({ initialValue, viewOnly = false }: PropType) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const { isValid, isSubmitting, resetForm } = useFormikContext<ReqBody>();
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [selectedModalType, setSelectedModalType] = useState<null | "RESET" | "SUBMIT">(null);

  const {
    toggleDialogOpen,
    ConfirmationModal
  } = useControllerFormManagementCoreConfirmationBeforeSubmitFormik({
    onAction: () => {
      if (selectedModalType === "RESET") {
        resetForm({ values: initialValue });
      } else {
        return null;
      }
    },
    redirectURL: "../success",
    redirectOptions: { relative: "path" },
    onConfirmationText: t(`areYouSureYouWantTo${selectedModalType === "SUBMIT" ? "SubmitThisComplaint" : "ResetTheInformationOnThisPage"}`),
    onSuccessText: t(`thisComplaintHasBeenSuccessfully${selectedModalType === "SUBMIT" ? "SubmittedAndRecorded" : "Reset"}`),
    onClose: () => setSelectedModalType(null)
  })

  const primary = theme.palette.primary.main;
  const error = theme.palette.error.main;
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const popoverBranchHelperOpened = Boolean(anchorEl);
  const popoverBranchHelperId = popoverBranchHelperOpened ? "popover-branch-helper" : undefined;
  const idTypeOptions = IdTypes.filter((item) =>
    [1, 4, 5].includes(parseInt(item.value))
  )

  const handleButtonClicked = (action: "SUBMIT" | "RESET" = "SUBMIT") => () => {
    setSelectedModalType(action);
    toggleDialogOpen();
  }
  const handleButtonDownloadClicked = () => {
    window.open("/test", "_blank")
  }

  return (
    <>
      <Form>
        <Box
          sx={{
            borderRadius: "1rem",
            backgroundColor: "white",
            boxShadow: "0 0 0.75rem rgba(0, 0, 0, 0.3)",
            padding: "1rem",
            display: "flex",
            flexDirection: "column",
            rowGap: "1rem",
            marginBottom: "0.5rem"
          }}
        >
          <Box
            sx={{
              border: "1px solid #DADADA",
              borderRadius: "0.5rem",
              padding: "1.5rem"
            }}
          >
            <Typography
              color={primary}
              sx={{
                fontSize: "14px",
                fontWeight: "medium",
                marginBottom: "1.25rem"
              }}
            >
              {t("complainantDetails")}
            </Typography>
            <Grid container spacing={2}>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("name")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="name"
                  placeholder="Nama Pemegang Jawatan"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("idType")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="identificationType"
                  placeholder="MyKad/Passport/MyPR"
                  helperTextComponentPlacement="INSIDE"
                  select
                  helperTextFallbackValue=""
                  disabled
                >
                  {idTypeOptions.map((item, index) =>
                    <MenuItem key={`id-type-option-${index}`} value={item.value}>{t(item.label)}</MenuItem>
                  )}
                </TextFieldControllerFormik>
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("idNumber")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="identificationNo"
                  placeholder="XXXXXXXXXXXX"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("gender")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="gender"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  select
                  disabled={viewOnly}
                >
                  {GenderType.map((item, index) =>
                    <MenuItem key={`gender-${index}`} value={item.code}>{t(item.translateKey)}</MenuItem>
                  )}
                </TextFieldControllerFormik>
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("race")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="bangsa"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("occupation")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="pekerjaan"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("age")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="umur"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("citizenship")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="kewarganegaraan"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("address")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="alamat"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("postcode")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="poskod"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("district")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="daerah"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("city")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="bandar"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("state")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="negeri"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("emailWithoutDash")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="emel"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("noTelephone")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="noTelp"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("homeNumber")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="noTelpRumah"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
            </Grid>
          </Box>
        </Box>
        <Box
          sx={{
            borderRadius: "1rem",
            backgroundColor: "white",
            boxShadow: "0 0 0.75rem rgba(0, 0, 0, 0.3)",
            padding: "1rem",
            display: "flex",
            flexDirection: "column",
            rowGap: "1rem",
          }}
        >
          <Box
            sx={{
              border: "1px solid #DADADA",
              borderRadius: "0.5rem",
              padding: "1.5rem"
            }}
          >
            <Typography
              color={primary}
              sx={{
                fontSize: "14px",
                fontWeight: "medium",
                marginBottom: "1.25rem"
              }}
            >
              {t("complaintDetails")}
            </Typography>
            <Grid container spacing={2}>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("titleFeedback")}{" "}
                  <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="tajuk"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("complaintTopic")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="complaintTopic"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  select
                  disabled={viewOnly}
                >
                  {GenderType.map((item, index) =>
                    <MenuItem key={`gender-${index}`} value={item.code}>{t(item.translateKey)}</MenuItem>
                  )}
                </TextFieldControllerFormik>
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("cawangan")}{" "}
                  <span style={{ color: error }}>*</span>
                  <IconButton
                    aria-describedby={popoverBranchHelperId}
                    color="info"
                    sx={{ marginLeft: "0.5rem" }}
                    onClick={(e) => setAnchorEl(e.currentTarget)}
                  >
                    <Info />
                  </IconButton>
                  <Popover
                    id={popoverBranchHelperId}
                    open={popoverBranchHelperOpened}
                    anchorEl={anchorEl}
                    onClose={() => setAnchorEl(null)}
                    sx={{ maxWidth: "35%" }}
                  >
                    <Typography sx={{ p: 2, fontSize: 12 }} color={error}>Sila pilih negeri atau cawangan yang bertanggung jawab untuk menguruskan aduan ini</Typography>
                  </Popover>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="branchName"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  select
                  disabled={viewOnly}
                >
                  {GenderType.map((item, index) =>
                    <MenuItem key={`gender-${index}`} value={item.code}>{t(item.translateKey)}</MenuItem>
                  )}
                </TextFieldControllerFormik>
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("nameOfThePerpetrator")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="namaPelaku"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>

              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("dateOfOccurrence")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <DatePickerFormik
                  name="tarikhKejadian"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallback=""
                  disableFuture
                  disabled={viewOnly}
                />
              </Grid>

              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("timeOfIncident")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <Box display="flex" alignItems="flex-start">
                  <TimePickerFormik
                    name="masaKejadianAwal"
                    disabled={viewOnly}
                  />
                  <Box sx={{ flex: 0.5, m: 2, mt: 1, textAlign: "center" }}>—</Box>
                  <TimePickerFormik
                    name="masaKejadianAkhir"
                    disabled={viewOnly}
                  />
                </Box>
              </Grid>

              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("sceneOfTheIncident")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="tempatKejadian"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("ppmNumber")} <span style={{ color: error }}>*</span>
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="ppmNumber"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("organizationName")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="namaPertubuhan"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("alamatPertubuhan")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="alamatPertubuhan"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("incidentDetails")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="butiranKejadian"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  multiline
                  rows={4}
                  disabled={viewOnly}
                />
              </Grid>
              <Grid item md={4} xs={12}>
                <Typography sx={labelStyle}>
                  {t("lampiran")}
                </Typography>
              </Grid>
              <Grid item md={8} xs={12}>
                <TextFieldControllerFormik
                  name="lampiran"
                  helperTextComponentPlacement="INSIDE"
                  helperTextFallbackValue=""
                  disabled={viewOnly}
                />
              </Grid>
            </Grid>
          </Box>
          <div style={{
            display: "flex",
            alignItems: "flex-end",
            justifyContent: "flex-end",
            marginTop: "1rem",
            columnGap: "0.5rem",
          }}>
            {viewOnly && (
              <Button
                sx={{
                  textTransform: "capitalize",
                  minWidth: "7.5rem",
                  color: "white"
                }}
                variant="contained"
                onClick={handleButtonDownloadClicked}
              >
                {t("download")}
              </Button>
            )}
            {!viewOnly && (
              <>
                <Button
                  sx={{
                    textTransform: "capitalize",
                    minWidth: "7.5rem",
                  }}
                  variant="outlined"
                  onClick={handleButtonClicked("RESET")}
                >
                  {t("reset")}
                </Button>
                <Button
                  sx={{
                    textTransform: "capitalize",
                    minWidth: "7.5rem",
                    color: "white"
                  }}
                  variant="contained"
                  disabled={!isValid || isSubmitting}
                  onClick={handleButtonClicked()}
                >
                  {isSubmitting ? <CircularProgress size="1.5rem" /> : t("hantar")}
                </Button>
              </>
            )}
          </div>
        </Box>
      </Form>
      <ConfirmationModal />
    </>
  )
}
