import React, { useMemo, useCallback, useLayoutEffect, useRef, useState, useEffect } from "react";
import { Box, Typography, Grid, IconButton } from "@mui/material";
import { useNavigate, useParams } from "react-router-dom";
import { useBack } from "@refinedev/core";
import { styled } from "@mui/material";
import { useTranslation } from "react-i18next";
import DashboardStatistikContent from "./dashboard-statistik";
import StatistikContent from "./statistik";
import LaporanContent from "./laporan";
import AnalisisContent from "./analisis";

// Import the SVG icons
import IconSekatanLiabiliti from "@/assets/svg/icon-sekatan-liabiliti.svg?react";
import IconPengurusanNotis from "@/assets/svg/icon-pengurusan-notis.svg?react";

// Import category content components
const categoryContent = {
  "dashboard-statistik": <DashboardStatistikContent />,
  "statistik": <StatistikContent />,
  "laporan": <LaporanContent />,
  "analisis": <AnalisisContent />
};

interface NavIconProps {
  icon: React.FC;
  active?: boolean;
}

const generateNavIcon = <Props extends NavIconProps = NavIconProps>({ icon }: Props) => (isActive: boolean) => {
  const StyledIcon = styled(icon)(({ theme }) => ({
    height: "38px",
    ...(isActive && {
      "path": {
        stroke: "white",
        fill: theme.palette.primary.main
      }
    })
  }));
  return <StyledIcon />;
};

const InternalLaporanIndex: React.FC = () => {
  const { t } = useTranslation();
  const back = useBack();
  const navigate = useNavigate();
  const { category } = useParams<{ category?: string }>();
  const gridRef = useRef<(HTMLDivElement | null)[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);

  const breadcrumbs = [
    {
      label: t("laporan"),
      path: "/laporan",
    },
  ];

  const primary = "#0CA6A6"; // var(--primary-color)



  const navigations = useMemo(() => [
    {
      label: t("dashboardStatistik"),
      categoryKey: "dashboard-statistik",
      icon: generateNavIcon({ icon: IconPengurusanNotis })
    },
    {
      label: t("statistik"),
      categoryKey: "statistik",
      icon: generateNavIcon({ icon: IconSekatanLiabiliti })
    },
    {
      label: t("laporan"),
      categoryKey: "laporan",
      icon: generateNavIcon({ icon: IconPengurusanNotis })
    },
    {
      label: t("analisis"),
      categoryKey: "analisis",
      icon: generateNavIcon({ icon: IconPengurusanNotis })
    }
  ], [t]);

  // Set selected category based on URL parameter
  useEffect(() => {
    if (category) {
      setSelectedCategory(category);
    }
  }, [category]);

  const handleCategoryClick = useCallback((categoryKey: string) => {
    setSelectedCategory(categoryKey);
    // Navigate to the category URL
    navigate(`/laporan/${categoryKey}`);
  }, [navigate]);

  const setGridRef = useCallback((el: HTMLDivElement, index: number) => {
    gridRef.current[index] = el;
  }, []);

  useLayoutEffect(() => {
    const itemsHeight = gridRef.current.map(el => el?.offsetHeight!);
    const maxHeight = Math.max(...itemsHeight);
    for (const el of gridRef.current) {
      if (el) {
        el.style.height = `${maxHeight}px`;
      }
    }
  }, []);

  return (
    <Box
      sx={{
        mx: 3,
      }}
    >
      {/* Header with back button and breadcrumb - exactly like penguatkuasaan */}
      <Box
        sx={{
          mb: 2,
          display: "flex",
          alignItems: "center",
          gap: 1,
          mt: 3,
        }}
      >
        <IconButton onClick={() => back()}>
          <svg
            width="12"
            height="12"
            viewBox="0 0 12 12"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M8.03065 10.7726C8.17126 10.632 8.25024 10.4412 8.25024 10.2423C8.25024 10.0435 8.17126 9.85274 8.03065 9.7121L4.31815 5.9996L8.03065 2.2871C8.16727 2.14565 8.24287 1.95619 8.24116 1.75955C8.23945 1.5629 8.16057 1.37479 8.02152 1.23573C7.88246 1.09668 7.69435 1.0178 7.4977 1.01609C7.30106 1.01438 7.1116 1.08998 6.97015 1.2266L2.7274 5.46935C2.5868 5.60999 2.50781 5.80072 2.50781 5.9996C2.50781 6.19847 2.5868 6.3892 2.7274 6.52985L6.97015 10.7726C7.1108 10.9132 7.30153 10.9922 7.5004 10.9922C7.69928 10.9922 7.89001 10.9132 8.03065 10.7726Z"
              fill="#666666"
            />
          </svg>
        </IconButton>
        <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
          {breadcrumbs.map((item, index, array) => (
            <React.Fragment key={item.path}>
              <Typography
                fontWeight={500}
                sx={{
                  color: "#666666",
                  cursor: "pointer",
                  "&:hover": {
                    textDecoration: "underline",
                  },
                }}
                onClick={() => navigate(item.path)}
              >
                {item.label}
              </Typography>
              {index < array.length - 1 && (
                <svg
                  key={`icon-${item.path}`}
                  width="12"
                  height="12"
                  viewBox="0 0 12 12"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M8.03065 10.7726C8.17126 10.632 8.25024 10.4412 8.25024 10.2423C8.25024 10.0435 8.17126 9.85274 8.03065 9.7121L4.31815 5.9996L8.03065 2.2871C8.16727 2.14565 8.24287 1.95619 8.24116 1.75955C8.23945 1.5629 8.16057 1.37479 8.02152 1.23573C7.88246 1.09668 7.69435 1.0178 7.4977 1.01609C7.30106 1.01438 7.1116 1.08998 6.97015 1.2266L2.7274 5.46935C2.5868 5.60999 2.50781 5.80072 2.50781 5.9996C2.50781 6.19847 2.5868 6.3892 2.7274 6.52985L6.97015 10.7726C7.1108 10.9132 7.30153 10.9922 7.5004 10.9922C7.69928 10.9922 7.89001 10.9132 8.03065 10.7726Z"
                    fill="#666666"
                  />
                </svg>
              )}
            </React.Fragment>
          ))}
        </Box>
      </Box>

      {/* Navigation section - exactly like penguatkuasaan */}
      <Grid container spacing={2}>
        <Grid item md={12}>
          <Grid container>
            <Grid item md={12}>
              <Box
                sx={{
                  backgroundColor: "white",
                  borderRadius: "1rem",
                  padding: 2,
                  boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)"
                }}
              >
                <Typography
                  color={primary}
                  sx={{
                    fontSize: 14,
                    fontWeight: "medium",
                    marginBottom: "1.5rem"
                  }}
                >
                  {t("pengurusanPelaporan")}
                </Typography>
                <Grid container spacing={1}>
                  {navigations.map((navItem, index) => {
                    const isActive = selectedCategory === navItem.categoryKey;
                    return (
                      <Grid
                        key={`laporan-nav-item-${index}`}
                        item
                        sm={2}>
                        <Box
                          ref={el => setGridRef(el as HTMLDivElement, index)}
                          onClick={() => handleCategoryClick(navItem.categoryKey)}
                          sx={{
                            padding: "0.75rem",
                            paddingTop: "0.5rem !important",
                            borderRadius: "0.5rem",
                            border: `1px solid ${primary}`,
                            backgroundColor: isActive ? primary : "white",
                            position: "relative",
                            display: "flex",
                            flexDirection: "column",
                            justifyContent: "space-between",
                            alignItems: "flex-start",
                            height: "100%",
                            minHeight: "67px",
                            paddingBottom: 0,
                            cursor: "pointer",
                            transition: "all 0.2s ease-in-out",
                            "&:hover": {
                              backgroundColor: isActive ? primary : "#f5f5f5",
                              transform: "translateY(-2px)",
                            },
                            ...(isActive && { boxShadow: "4px 6px 12px 0 rgba(102, 102, 102, 0.3)" })
                          }}
                        >
                          <Typography
                            fontSize={12}
                            color={isActive ? "white" : primary}
                            maxWidth={85}
                          >
                            {navItem.label}
                          </Typography>
                          {navItem.icon && (
                            <div
                              style={{
                                display: "flex",
                                width: "100%",
                                alignItems: "flex-end",
                                justifyContent: "flex-end",
                                position: "absolute",
                                height: "38px",
                                bottom: 0,
                                right: "10px",
                              }}
                            >
                              {navItem.icon(isActive)}
                            </div>
                          )}
                        </Box>
                      </Grid>
                    );
                  })}
                </Grid>
              </Box>
            </Grid>
          </Grid>
        </Grid>

        {/* Category Content Display */}
        {selectedCategory && (
          <Grid container spacing={2} marginTop={'7px'} marginLeft={0}>
            <Grid item md={12} paddingTop={'0 !important'}>
              <Box
                paddingTop={0}
                sx={{
                  boxShadow: "0 12px 12px 0 rgba(234, 232, 232, 0.4)"
                }}
              >
                {categoryContent[selectedCategory as keyof typeof categoryContent]}
              </Box>
            </Grid>
          </Grid>
        )}
      </Grid>
    </Box>
  );
};

export default InternalLaporanIndex;
