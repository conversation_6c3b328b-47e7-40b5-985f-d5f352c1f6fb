import { Box, Button, Grid, MenuItem, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { TextFieldControllerFormik } from "@/components/input";
import { GenderType } from "@/helpers";

export const CardAduanDecision = () => {
  const { t } = useTranslation();

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  return (
    <Box
      sx={{
        borderRadius: "1rem",
        backgroundColor: "white",
        boxShadow: "0 0 0.75rem rgba(234, 232, 232, 0.4)",
        padding: "1rem",
        display: "flex",
        flexDirection: "column",
        rowGap: "1rem",
        marginBottom: "0.5rem"
      }}
    >
      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "0.5rem",
          padding: "1.5rem"
        }}
      >
        <Typography className="title" mb="1.25rem">
          {t("complaintDecision")}
        </Typography>
        <Grid container spacing={2}>
          <Grid item md={4} xs={12}>
            <Typography sx={labelStyle}>
              {t("status")}
            </Typography>
          </Grid>
          <Grid item md={8} xs={12}>
            <TextFieldControllerFormik
              name="branchName"
              helperTextComponentPlacement="INSIDE"
              helperTextFallbackValue=""
              select
            >
              {GenderType.map((item, index) =>
                <MenuItem key={`gender-${index}`} value={item.code}>{t(item.translateKey)}</MenuItem>
              )}
            </TextFieldControllerFormik>
          </Grid>
          <Grid item md={4} xs={12}>
            <Typography sx={labelStyle}>
              {t("remarks")}
            </Typography>
          </Grid>
          <Grid item md={8} xs={12}>
            <TextFieldControllerFormik
              name="branchName"
              helperTextComponentPlacement="INSIDE"
              helperTextFallbackValue=""
              multiline
              rows={4}
            />
          </Grid>
        </Grid>
      </Box>
      <div
        style={{
          display: "flex",
          justifyContent: "flex-end",
          columnGap: "0.5rem"
        }}
      >
        <Button
          variant="outlined"
          sx={{
            textTransform: "capitalize",
          }}
        >
          {t("update")}
        </Button>
        <Button
          variant="contained"
          sx={{
            textTransform: "capitalize",
            color: "white"
          }}
        >
          {t("hantar")}
        </Button>
      </div>
    </Box>
  )
}
