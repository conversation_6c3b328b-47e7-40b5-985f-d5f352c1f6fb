import { Form, Formik, type FormikHelpers } from "formik";
import { useLocation } from "react-router-dom";

import { CardAduanTitleSection } from "@/components/card/aduan/TitleSection";
import { FormFeedbackAduanInternalCreate } from "@/components/form/feedback/aduan/InternalCreate";
import { type FeedbackAduanPublicCreateRequestBody } from "@/components/form/feedback/aduan/PublicCreate";
import AccordionComp from "@/pages/pengurusan-pertubuhan-internal/View/Accordion";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import { CardAduanHistory } from "@/components/card/aduan/History";
import { CardAduanSendingPEMNotification } from "@/components/card/aduan/SendingPEMNotification";
import { CardAduanDecision } from "@/components/card/aduan/Decision";
import { CardAduanAction } from "@/components/card/aduan/Action";

export const FeedbackPublicAduanViewWithAction = <
  ReqBody extends FeedbackAduanPublicCreateRequestBody = FeedbackAduanPublicCreateRequestBody
>() => {
  const location = useLocation();
  const { t } = useTranslation();
  const [currentExpandSection, setCurrentExpandSection] = useState<
    number | false
  >(false);
  const [readStatus, setReadStatus] = useState<{ [key: number]: boolean }>({});

  const initialValue = {
    ...location.state?.prevData
  } as ReqBody;
  const sectionItems = [
    {
      subTitle: t("complaintInformation"),
      component: null
    }
  ]

  const handleChangeCurrentExpandSection =
    (item: number) => (event: React.SyntheticEvent, isExpanded: boolean) => {
      setCurrentExpandSection(isExpanded ? item : false);

      if (isExpanded) {
        setReadStatus((prevState) => {
          const updatedStatus = sectionItems.reduce((acc, _, i) => {
            if (i + 1 <= item) {
              acc[i + 1] = true;
            } else {
              acc[i + 1] = !!prevState[i + 1] || false;
            }
            return acc;
          }, {} as Record<number, boolean>);
          return updatedStatus;
        });
      }
    };
  const handleSubmit = async (payload: ReqBody, { setSubmitting }: FormikHelpers<ReqBody>) => {
    const { promise, resolve } = Promise.withResolvers();
    setSubmitting(true);
    setTimeout(() => {
      resolve(payload);
      setSubmitting(false);
    }, 3000);
    await promise;
    // try {
    // } finally {
    //   setSubmitting(false);
    // }
  }

  return (
    <div style={{
      display: "flex",
      flexDirection: "column",
      rowGap: "0.5rem"
    }}>
      <CardAduanTitleSection />
      <AccordionComp
        subTitle={t("complaintInformation")}
        currentIndex={0}
        currentExpand={currentExpandSection}
        readStatus={readStatus}
        onChangeFunc={handleChangeCurrentExpandSection}
        withRightBox={false}
        withMarginTop={false}
      >
        <Formik<ReqBody>
          initialValues={{ ...location.state?.prevData } as ReqBody}
          onSubmit={handleSubmit}
        >
          <FormFeedbackAduanInternalCreate initialValue={initialValue} viewOnly />
        </Formik>
      </AccordionComp>
      <CardAduanHistory />
      <Formik initialValues={{}} onSubmit={console.log}>
        <Form>
          <CardAduanSendingPEMNotification />
          <CardAduanAction />
          <CardAduanDecision />
        </Form>
      </Formik>
    </div>
  )
}
