import React, { useEffect, useState } from "react";
import { useSearchParams, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  Card,
  CardContent,
  CircularProgress,
  Fade,
  LinearProgress
} from "@mui/material";
import { useDispatch } from "react-redux";
import { PaymentRedirectUrl } from "@/helpers/enums";
import { PENDAFTARAN_PEGAWAI_AWAM_ONLINE, PENDAFTARAN_PEGAWAI_HARTA_ONLINE } from "@/helpers";
import usePaymentService from "@/helpers/hooks/usePaymentService";
import { IPaymentRecordResponse } from "@/services/paymentService";
import { setBranchDataRedux } from "@/redux/branchDataReducer";

const PaymentKeputusan: React.FC = () => {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { getPaymentRecord, loading: serviceLoading, error: apiError } = usePaymentService();

  const [redirectCountdown, setRedirectCountdown] = useState(5);
  const [paymentData, setPaymentData] = useState<IPaymentRecordResponse | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [minWaitCompleted, setMinWaitCompleted] = useState(false);

  // Get parameters from URL
  const transactionId = searchParams.get("transactionId");

  // Redirect to pertubuhan dashboard
  const fallbackRedirectUrl = "/pertubuhan";

  // Fetch payment record when transactionId is available
  useEffect(() => {
    if (transactionId) {
      setIsLoading(true);
      setMinWaitCompleted(false);

      // Start minimum wait timer
      const minWaitTimer = setTimeout(() => {
        setMinWaitCompleted(true);
      }, 5000); // 5 seconds minimum wait

      getPaymentRecord(transactionId)
        .then((result) => {
          if (result.data) {
            setPaymentData(result.data);
          }
        })
        .catch((error) => {
          console.error("Failed to fetch payment record:", error);
        });

      return () => clearTimeout(minWaitTimer);
    }
  }, [transactionId, getPaymentRecord]);

  // Update loading state based on service loading and minimum wait
  useEffect(() => {
    if (!serviceLoading && minWaitCompleted) {
      setIsLoading(false);
    }
  }, [serviceLoading, minWaitCompleted]);

  // Handle API error and provide mock data for demo
  useEffect(() => {
    if (apiError) {
      console.log("API error occurred:", apiError);

      // redirect to dashboard
      navigate(fallbackRedirectUrl);
    }
  }, [apiError, transactionId, navigate, fallbackRedirectUrl]);

  const redirectToModule = () => {
    if (paymentData) {
      // Set branchDataRedux id from paymentData.branchId if branch id is not null or 0
      if (paymentData.branchId != null && paymentData.branchId != 0) {
        dispatch(setBranchDataRedux({ id: paymentData.branchId }));
      }

      let paymentType = paymentData.paymentType;
      if (paymentType === PENDAFTARAN_PEGAWAI_AWAM_ONLINE || paymentType === PENDAFTARAN_PEGAWAI_HARTA_ONLINE) {
        if (paymentData.branchId == null || paymentData.branchId == 0) {
          paymentType = paymentType + " PERTUBUHAN";
        } else {
          paymentType = paymentType + " CAWANGAN";
        }
      }

      let redirectUrl = PaymentRedirectUrl[paymentType as keyof typeof PaymentRedirectUrl];
      if (redirectUrl) {
        if (paymentData.societyId && redirectUrl.includes("{societyId}")) {
          redirectUrl = redirectUrl.replace("{societyId}", paymentData.societyId?.toString());
        }

        navigate(redirectUrl);
      } else {
        // redirect to dashboard
        navigate(fallbackRedirectUrl);
      }
    } else {
      // redirect to dashboard
      navigate(fallbackRedirectUrl);
    }
  };

  // Redirect countdown timer
  useEffect(() => {
    if (paymentData && redirectCountdown > 0) {
      const timer = setTimeout(() => {
        setRedirectCountdown(redirectCountdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    } else if (paymentData && redirectCountdown === 0) {
      // Redirect to appropriate page based on payment data
      redirectToModule();
    }
  }, [paymentData, redirectCountdown, redirectToModule]);

  return (
    <Box
      sx={{
        display: "flex",
        justifyContent: "center",
        alignItems: "center",
        minHeight: "60vh",
        p: 3,
      }}
    >
      <Fade in={true} timeout={800}>
        <Card sx={{ maxWidth: 600, width: "100%", boxShadow: 3 }}>
          <CardContent sx={{ textAlign: "center", p: 4 }}>
            <CircularProgress
                size={60}
                sx={{ color: "#7fbfbf", mb: 3 }}
              />
              <Typography variant="h5" gutterBottom>
                {t("paymentResultProcessing")}
              </Typography>
              <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                {t("paymentResultPleaseWait")}
              </Typography>
              <LinearProgress
                sx={{
                  width: "100%",
                  height: 6,
                  borderRadius: 3,
                  backgroundColor: "#f0f0f0",
                  "& .MuiLinearProgress-bar": {
                    backgroundColor: "#7fbfbf"
                  }
                }}
              />
          </CardContent>
        </Card>
      </Fade>
    </Box>
  );
};

export default PaymentKeputusan;
