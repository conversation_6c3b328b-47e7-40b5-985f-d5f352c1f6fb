import { useFormikContext } from "formik";
import { useState } from "react";
import { NavigateOptions, To, useNavigate } from "react-router-dom";
import { DialogConfirmation, DialogConfirmationProps } from "@/components";

export interface DefaultControllerFormManagementCoreConfirmationBeforeSubmitFormikOptions
  extends Omit<DialogConfirmationProps, "open" | "onClose" | "onAction" | "isMutating"> {
  /**
   * @default 1000ms
   */
  redirectDelay?: number

  /**
   * @default false
   */
  initialDialogOpen?: boolean

  redirectURL: To | null

  redirectOptions?: NavigateOptions

  isMutating?: boolean | ((isSubmitting: boolean) => boolean)

  /**
   * @default null
   */
  onClose?: (() => void) | null

  onAction?: null | (() => void | Promise<void> | null)
}

export function useControllerFormManagementCoreConfirmationBeforeSubmitFormik<
  Options extends DefaultControllerFormManagementCoreConfirmationBeforeSubmitFormikOptions = DefaultControllerFormManagementCoreConfirmationBeforeSubmitFormikOptions
>(options: Options) {
  const {
    redirectDelay = 1000,
    initialDialogOpen = false,
    redirectURL,
    redirectOptions = undefined,
    isMutating: initialIsMutating,
    ...dialogConfirmationProps
  } = options

  const [open, setDialogOpen] = useState(() => initialDialogOpen);
  const [isSuccess, setIsSuccess] = useState(false);
  const { submitForm, isSubmitting: isSubmittingFallback } = useFormikContext();
  const navigate = useNavigate();

  const isMutating = typeof initialIsMutating === "function"
    ? initialIsMutating(isSubmittingFallback)
    : (initialIsMutating ?? isSubmittingFallback)

  const toggleDialogOpen = (val: boolean | null = null) =>
    setDialogOpen(prev => val ?? !prev);
  const onAction = async () => {
    try {
      const onActionResult = await dialogConfirmationProps?.onAction?.();
      if (onActionResult === null && redirectURL !== null) {
        await submitForm();
        setIsSuccess(true);
        setTimeout(() => {
          navigate(redirectURL, redirectOptions);
        }, redirectDelay);
      } else {
        setIsSuccess(true);
      }
    } catch {
      setIsSuccess(false);
    }
  }
  const onClose = () => {
    setDialogOpen(false);
    setIsSuccess(false);
    dialogConfirmationProps?.onClose?.();
  }

  const ConfirmationModal = () => (
    <DialogConfirmation
    {...dialogConfirmationProps}
    {...{ isMutating, isSuccess, open, onAction, onClose }}
    />
  )

  return {
    toggleDialogOpen,
    ConfirmationModal
  }
}
