import { useState } from "react";
import { NavigateOptions, To, useNavigate } from "react-router-dom";
import { DialogConfirmation, DialogConfirmationProps } from "@/components";

export interface DefaultControllerFormManagementCoreConfirmationBeforeSubmitOptions
  extends Omit<DialogConfirmationProps, "open" | "onClose" | "onAction" | "isMutating"> {
  /**
   * @default 1000ms
   */
  redirectDelay?: number

  /**
   * @default false
   */
  initialDialogOpen?: boolean

  redirectURL: To | null

  redirectOptions?: NavigateOptions

  isMutating?: boolean

  /**
   * @default null
   */
  onClose?: (() => void) | null

  onAction?: null | (() => void | Promise<void> | null)

  autoCloseAfterSuccess?: boolean
}

export function useControllerFormManagementCoreConfirmationBeforeSubmit<
  Options extends DefaultControllerFormManagementCoreConfirmationBeforeSubmitOptions = DefaultControllerFormManagementCoreConfirmationBeforeSubmitOptions
>(options: Options) {
  const {
    redirectDelay = 1000,
    initialDialogOpen = false,
    redirectURL,
    redirectOptions = undefined,
    isMutating: initialIsMutating = false,
    autoCloseAfterSuccess = true,
    ...dialogConfirmationProps
  } = options

  const [open, setDialogOpen] = useState(() => initialDialogOpen);
  const [isSuccess, setIsSuccess] = useState(false);
  const [isMutating, setIsMutating] = useState(() => initialIsMutating);
  const navigate = useNavigate();

  const toggleDialogOpen = (val: boolean | null = null) =>
    setDialogOpen(prev => val ?? !prev);
  const onAction = async () => {
    setIsMutating(true);
    try {
      await dialogConfirmationProps?.onAction?.();
      if (redirectURL !== null) {
        setIsSuccess(true);
        setTimeout(() => {
          navigate(redirectURL, redirectOptions);
        }, redirectDelay);
      } else {
        setIsSuccess(true);
      }
    } catch {
      setIsSuccess(false);
    } finally {
      setIsMutating(false);
      setTimeout(() => {
        if (autoCloseAfterSuccess) {
          onClose();
        }
      }, redirectDelay);
    }
  }
  const onClose = () => {
    setDialogOpen(false);
    setIsSuccess(false);
    setIsMutating(false);
    dialogConfirmationProps?.onClose?.();
  }

  const ConfirmationModal = () => (
    <DialogConfirmation
    {...dialogConfirmationProps}
    {...{ isMutating, isSuccess, open, onAction, onClose }}
    />
  )

  return {
    toggleDialogOpen,
    ConfirmationModal
  }
}
