import { Box, Typography, <PERSON><PERSON>, <PERSON>, Stack, Paper } from "@mui/material";

interface DocumentCardProps {
  title: string;
  subtitle: string;
  formats: string[];
  size: string;
}

export default function DocumentCard({
  title,
  subtitle,
  formats,
  size,
}: DocumentCardProps) {
  return (
    <Paper
      variant="outlined"
      sx={{
        borderRadius: 2,
        p: 2,
        minWidth: 300,
        flex: 1,
        bgcolor: "#FFF",
        border: "1px solid #0CA6A6",
      }}
    >
      <Box
        sx={{
          p: 2,
          mb: 3,
          background: "#EEF9F9",
          borderRadius: "10px",
        }}
      >
        <Typography className="title" fontWeight="bold" mb={1}>
          {title}
        </Typography>
        <Typography fontSize="12px" mb={10}>
          {subtitle}
        </Typography>

        <Stack direction="row" spacing={1} flexWrap="wrap">
          {formats.map((format, index) => (
            <Chip
              sx={{
                color: "#666666",
                background: "#0CA6A612",
                border: "0.5px solid #0CA6A6B2",
              }}
              key={index}
              label={format}
              variant="outlined"
            />
          ))}
          <Chip
            label={size}
            sx={{
              color: "#666666",
              background: "#0CA6A612",
              border: "0.5px solid #0CA6A6B2",
            }}
            variant="outlined"
          />
        </Stack>
      </Box>

      <Stack direction="row" spacing={1} mb={3}>
        <Button
          variant="outlined"
          sx={{ borderRadius: "5px", textTransform: "none" }}
          size="small"
        >
          Lihat PDF
        </Button>
      </Stack>

      <Stack direction="row" spacing={1}>
        <Button
          sx={{ borderRadius: "5px", textTransform: "none" }}
          variant="contained"
          size="small"
        >
          Muat Turun PDF
        </Button>
        <Button
          sx={{ borderRadius: "5px", textTransform: "none" }}
          variant="contained"
          size="small"
        >
          Muat Turun eBook
        </Button>
      </Stack>
    </Paper>
  );
}
