import {
  AcceptedDateStringFormats,
  CitizenshipStatus,
  MeetingTypeOption,
  DocumentCategoryOptions
} from "./enums";
import dayjs from "./dayjs";

import {
  IAddressNode,
  IMeetingOptions,
  ISocietyCategory,
  ISection,
} from "@/types";

import { MALAYSIA, SectionOptions, IdTypes, ListGender } from "./enums";
import { PORTAL_EXTERNAL, PORTAL_INTERNAL } from "./constants";

export const convertDate = (date: Date, locale = "en-MY") => {
  const formatter = new Intl.DateTimeFormat(locale, {
    year: "numeric",
    month: "long",
    day: "numeric",
  });

  return formatter.format(date);
};

export const filterEmptyValuesOnObject = <T extends Record<string, any>>(
  obj: T
): Partial<T> => {
  return Object.entries(obj).reduce((acc, [key, value]) => {
    if (
      value !== "" &&
      value !== null &&
      value !== undefined &&
      (Array.isArray(value) ? value.length > 0 : true)
    ) {
      acc[key as keyof T] = value;
    }
    return acc;
  }, {} as Partial<T>);
};

export const debounce = <T extends (...args: any[]) => void>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timer: NodeJS.Timeout;

  return function (...args: Parameters<T>): void {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func(...args);
    }, delay);
  };
};

export interface DefaultFormatDateOptions {
  /**
   * @default null
   */
  parseFormat?: string | null;
}

/**
 * @description this method parse input values from BE to standardized date string (DD-MM-YYYY).
 */
export const formatDate = <
  Options extends DefaultFormatDateOptions = DefaultFormatDateOptions
>(
  input: string | string[] | number[] | null | undefined = null,
  format = "DD-MM-YYYY",
  options?: Options
) => {
  const parseFormat = options?.parseFormat ?? null;

  let dateInput: dayjs.Dayjs | null = null;

  if (Array.isArray(input)) {
    switch (input.length) {
      case 6: {
        const [year, month, day, hour, minute, second] = input.map((val) =>
          String(val).padStart(2, "0")
        );
        dateInput = dayjs(
          `${year}-${month}-${day} ${hour}:${minute}:${second}`
        );
        break;
      }
      case 5: {
        const [year, month, day, hour, minute] = input.map((val) =>
          String(val).padStart(2, "0")
        );
        dateInput = dayjs(`${year}-${month}-${day} ${hour}:${minute}`);
        break;
      }
      case 3: {
        const [year, month, day] = input.map((val) =>
          String(val).padStart(2, "0")
        );
        dateInput = dayjs(`${year}-${month}-${day}`);
        break;
      }
    }
  } else if (typeof input === "string") {
    dateInput = parseFormat === null ? dayjs(input) : dayjs(input, parseFormat);
  }

  if (!dateInput) {
    return "-";
  }
  return dateInput.format(format);
};

/**
 * @deprecated please use {@link formatDate} instead
 * @description this method parse input values from BE to standardized date string (DD-MM-YYYY).
 */
export const formatArrayDate = (
  input: string | string[] | number[] | null | undefined = null,
  format = "DD-MM-YYYY",
  options?: DefaultFormatDateOptions
) => formatDate(input, format, options);

export const urlParams = (
  baseUrl: string,
  params: Record<string, any>
): string => {
  const url = new URL(baseUrl);
  Object.keys(params).forEach((key) => {
    if (params[key]) {
      url.searchParams.append(key, params[key]);
    }
  });
  return url.toString();
};

export const setLocalStorage = (key: string, value: any): void => {
  try {
    const jsonValue = JSON.stringify(value);
    localStorage.setItem(key, jsonValue);
  } catch (error) {
    console.error(`Error setting localStorage key "${key}":`, error);
  }
};

/**
 * @todo add generic return
 */
export function getLocalStorage(key: string, defaultValue: any) {
  try {
    const jsonValue = localStorage.getItem(key);
    return jsonValue ? JSON.parse(jsonValue) : defaultValue;
  } catch (error) {
    console.error(`Error getting localStorage key "${key}":`, error);
    return defaultValue;
  }
}

export const removeLocalStorage = (key: string): void => {
  try {
    localStorage.removeItem(key);
  } catch (error) {
    console.error(`Error removing localStorage key "${key}":`, error);
  }
};

export const toDollarFormat = (amount: number): string => {
  const isNegative = amount < 0;
  const absoluteAmount = Math.abs(amount);

  const formattedAmount = absoluteAmount
    .toFixed(2) // Ensure 2 decimal places
    .replace(/\d(?=(\d{3})+\.)/g, "$&,"); // Add commas

  return (isNegative ? "-" : "") + formattedAmount;
};

export interface GetDateDurationInput {
  dateStart: dayjs.ConfigType;
  dateEnd: dayjs.ConfigType;
  unit?: dayjs.QUnitType | dayjs.OpUnitType;
  float?: boolean;
}

export const getDateDuration = <
  Input extends GetDateDurationInput = GetDateDurationInput
>(
  input: Input
) => {
  const { dateStart, dateEnd, unit = "hour", float = false } = input;
  return dayjs(dateEnd).diff(dayjs(dateStart), unit, float);
};

export const convertFileToBinary = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
    reader.readAsArrayBuffer(file);
  });
};

export const omitKeysFromObject = <T extends { [key: string]: any }>(
  obj: T,
  keysToSkip: string[]
): Omit<T, keyof T> => {
  const newObj = { ...obj };
  keysToSkip.forEach((key) => {
    delete newObj[key];
  });
  return newObj;
};

export const truncateFilename = (filename: string, maxLength = 20) => {
  const parts = filename.split(".");
  const extension = parts.pop();
  const name = parts.join(".");

  if (name.length <= maxLength) {
    return filename;
  }

  const truncatedName = name.substring(0, maxLength) + "....";
  return `${truncatedName}.${extension}`;
};
/**
 * @description method to capitalize words
 * @todo set transformUppercasedWord default option to `true`
 * @param input text to be capitalized
 * @param customTransform function to custom transform each word
 * @param transformUppercasedWord options to allow/ignore transform the uppercased word
 * @returns string
 * @example
 * ## 1. default
 * ```ts
 * const input = 'hello world'
 * const output = capitalizeWords(input) // output: 'Hello World'
 * ```
 * ## 2. with `customTransform` 1 parameter
 * ```ts
 * const input = 'senarai AJK bukan warga negara'
 * // in order return 'Senarai AJK Bukan Warga Negara'
 * const output = capitalizeWords(input, (text) => {
 *   if (text === 'AJK') {
 *     return text
 *   }
 *   return null // means use default capitalized instead
 * })
 * ```
 * ## 3. with `customTransform` 2 parameters
 * ```ts
 * const input = 'Belum Dibayar'
 * // in order return 'Belum dibayar'
 * const output = capitalizeWords(input, (text, index) => {
 *   if (index > 0) {
 *     return text.toLowerCase()
 *   }
 *   return null // means use default capitalized instead
 * })
 * ```
 * ## 4. with `transformUppercasedWord` options
 * ```ts
 * const input = 'BELUM DIBAYAR'
 * // in order return 'Belum dibayar'
 * const output = capitalizeWords(input, null, true)
 * ```
 */
export function capitalizeWords(
  input: string,
  customTransform:
    | null
    | ((word: string, index: number) => string | null) = null,
  transformUppercasedWord = false
): string {
  return input
    .split(" ")
    .map((word, index) => {
      const customTransformText =
        customTransform !== null ? customTransform(word, index) : null;

      if (typeof customTransformText === "string") {
        return customTransformText;
      }

      // Check if the word is fully uppercase and don't transform it
      if (!transformUppercasedWord && word.toUpperCase() === word) {
        return word;
      }

      // Capitalize normally for other words
      if (word.startsWith("(")) {
        return `(${word[1].toUpperCase()}${word.slice(2).toLowerCase()}`;
      }

      return `${word[0].toUpperCase()}${word.slice(1).toLowerCase()}`;
    })
    .join(" ");
}

export const getAddressList = (): IAddressNode[] => {
  const addressList: IAddressNode[] = getLocalStorage("address_list", []);

  return addressList;
};

export const getMalaysiaAddressList = () => {
  const addressList: IAddressNode[] = getLocalStorage("address_list", []);
  const malaysiaList = addressList.filter(
    (address) => address.pid === MALAYSIA
  );

  return malaysiaList;
};

export const getStateNameById = (id: string): string => {
  const malaysiaList = getMalaysiaAddressList();

  const stateNameMap = malaysiaList.reduce(
    (acc: Record<string, string>, state: any) => {
      acc[state.id] = state.name;
      return acc;
    },
    {}
  );

  return stateNameMap[id] || "-";
};

export const getDistrictNameById = (id: string): string => {
  const addressList = getAddressList();
  const address = addressList
    .filter((items: any) => items.id === Number(id))
    .map((item: any) => ({ value: item.id, label: item.name }));
  return address?.[0]?.label || "-";
};

export const getGlobalStateNameById = (id: string): string => {
  const addressList = getLocalStorage("address_list", []);

  const stateNameMap = addressList.reduce(
    (acc: Record<string, string>, state: any) => {
      acc[state.id] = state.name;
      return acc;
    },
    {}
  );

  return stateNameMap[id] || "-";
};

export const getStateNamesByIds = (ids: string[]): string => {
  const malaysiaList = getMalaysiaAddressList();

  const stateNameMap = malaysiaList.reduce(
    (acc: Record<string, string>, state: any) => {
      acc[state.id] = state.name;
      return acc;
    },
    {}
  );

  const names = ids.map((id) => stateNameMap[id] || "-");
  return names.join(", ");
};

export const getAddressListName = (id: number | null): string => {
  if (!id) return "-";
  const addressList: IAddressNode[] = getLocalStorage("address_list", []);

  const address = addressList.find((item) => item.id === id);
  return address?.name ?? "-";
};

export const getMeetingType = (
  id: number | null
): (typeof MeetingTypeOption)[number] | undefined => {
  return MeetingTypeOption.find((meeting) => meeting.value === id);
};

export const getMeetingName = (meetingId: number): string => {
  const meetingOptions: IMeetingOptions[] = getLocalStorage("meeting_list", []);
  const meeting = meetingOptions.find((m) => m.id === meetingId);

  if (meeting) return meeting.nameBm;

  return "-";
};

export const getMainCategories = (id: number): ISocietyCategory | undefined => {
  const categories: ISocietyCategory[] = getLocalStorage("category_list", []);
  const mainCategories = categories.filter((cat: any) => cat.level === 1);

  return mainCategories.find((category) => category.id === id);
};

export const getSubCategories = (id: number): ISocietyCategory | undefined => {
  const categories: ISocietyCategory[] = getLocalStorage("category_list", []);
  const subCategories = categories.filter((cat: any) => cat.level === 2);

  return subCategories.find((category) => category.id === id);
};

export const getIdTypeLabel = (value: string): string => {
  const found = IdTypes.find((idType) => idType.value === value);
  return found?.label ?? "-";
};

export const getGenderLabel = (value: string): string => {
  const found = ListGender.find((gender) => gender.value === value);
  return found?.label ?? "-";
};

export const getCitizenshipLabel = (value: number): string => {
  const found = CitizenshipStatus.find((citizen) => citizen.value === value);
  return found?.label ?? "-";
};

export const getDocumentCategoryLabel = (value: number): string => {
  const found = DocumentCategoryOptions.find((document) => document.value === value);
  return found?.label ?? "-";
};

export function autoGenderSetByIC(
  idType: number,
  currGender: string | null | undefined,
  idNumber: string
) {
  let gender = currGender;
  if (
    (Number(idType) === 1 || Number(idType) === 4) &&
    idNumber?.length === 12
  ) {
    if (parseInt(idNumber?.slice(-1)) % 2 === 0) {
      // Even female
      gender = "P";
    } else {
      // Odd male
      gender = "L";
    }
  }
  return gender;
}

export function autoDOBSetByIC(
  idType: number,
  currDOB: any,
  idNumber: string,
  DDMMYYYY?: boolean
) {
  let DOB: any = currDOB;
  if ((Number(idType) === 1 || Number(idType) === 4) && idNumber) {
    if (idNumber.length >= 6) {
      const yearPart = idNumber.substring(0, 2);
      const monthDayPart = idNumber.substring(2, 6);

      const currentYear = new Date().getFullYear();
      const currentCentury = Math.floor(currentYear / 100);
      const currentTwoDigitYear = currentYear % 100;

      const twoDigitYear = parseInt(yearPart, 10);

      // Calculate possible years (current century and previous century)
      const yearCurrentCentury = currentCentury * 100 + twoDigitYear;
      const yearPrevCentury = (currentCentury - 1) * 100 + twoDigitYear;

      // Determine which year is more likely based on age limits (12 years)
      let fullYear;
      const ageCurrentCentury = currentYear - yearCurrentCentury;
      const agePrevCentury = currentYear - yearPrevCentury;

      // Check current century first
      if (ageCurrentCentury >= 12 && ageCurrentCentury <= 120) {
        fullYear = yearCurrentCentury;
      }
      // check previous century
      else if (agePrevCentury >= 12 && agePrevCentury <= 120) {
        fullYear = yearPrevCentury;
      } else {
        return DOB;
      }

      const parsedDate = dayjs(`${fullYear}${monthDayPart}`, "YYYYMMDD");

      // Additional checking if date isn't in the future
      if (parsedDate.isValid() && parsedDate.isBefore(dayjs())) {
        DOB = parsedDate.toDate();
      }
    }
  }

  if (!DDMMYYYY) {
    return DOB;
  } else {
    return dayjs(DOB).format("YYYY-MM-DD");
  }
}

export const downloadFile = ({
  data,
  name,
  mimeType = "application/pdf",
}: {
  data: Blob | string;
  name: string;
  mimeType?: string;
}) => {
  if (!data) return console.error("No data provided for download.");

  let url: string;

  if (typeof data === "string") {
    if (data.startsWith("data:")) {
      url = data;
    } else {
      try {
        const byteCharacters = atob(data);
        const byteNumbers = new Uint8Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const blob = new Blob([byteNumbers], { type: mimeType });
        url = URL.createObjectURL(blob);
      } catch (error) {
        console.error("Invalid Base64 string:", error);
        return;
      }
    }
  } else {
    url = URL.createObjectURL(data);
  }

  const link = document.createElement("a");
  link.href = url;
  link.download = name;
  link.style.display = "none";

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  if (data instanceof Blob || typeof data === "string") {
    URL.revokeObjectURL(url);
  }
};

export const formatDateToDDMMYYYY = (dateString: string): string => {
  if (!dateString) return "";
  const [year, month, day] = dateString.split("-");
  return `${day}-${month}-${year}`;
};

export const parseDateToISO8601 = (
  input: string | number[]
): dayjs.Dayjs | string => {
  if (Array.isArray(input)) {
    // Convert array to individual arguments
    const [year, month, day, hour = 0, minute = 0, second = 0] = input;
    return dayjs(new Date(year, month - 1, day, hour, minute, second));
  } else {
    return dayjs(input, AcceptedDateStringFormats, true).format(
      "YYYY-MM-DDTHH:mm:ss"
    );
  }
};

// Encoding to Base64 (handling Unicode):
export function toBase64(str: string) {
  const encoder = new TextEncoder();
  const uint8Array = encoder.encode(str) as unknown as number[];
  const base64 = btoa(String.fromCharCode.apply(null, uint8Array));
  return base64;
}

// Decoding from Base64 (handling Unicode):
export function fromBase64(base64: string) {
  const binaryString = atob(base64);
  const uint8Array = new Uint8Array(binaryString.length);
  for (let i = 0; i < binaryString.length; i++) {
    uint8Array[i] = binaryString.charCodeAt(i);
  }
  const decoder = new TextDecoder();
  const decodedString = decoder.decode(uint8Array);
  return decodedString;
}

// export const formatCurrencyWithCommas = (num:string) => {
//   return num.replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, ",");
// };

export const formatAndValidateNumber = (input: string): string => {
  const cleaned = input.replace(/,/g, "");

  if (!/^\d*\.?\d*$/.test(cleaned)) {
    return "";
  }

  const [intPart, decimalPart] = cleaned.split(".");

  const formattedInt = Number(intPart).toLocaleString("en-US");

  return decimalPart !== undefined
    ? `${formattedInt}.${decimalPart}`
    : formattedInt;
};

export const getSectionByCode = (code: string): ISection | undefined => {
  return SectionOptions.find((item) => item.code === code);
};

export const toTitleCase = (text: string): string => {
  return text
    .toLowerCase()
    .split(" ")
    .filter((word) => word.trim() !== "")
    .map((word) => word[0].toUpperCase() + word.slice(1))
    .join(" ");
};

export const isInternalPortal = () => {
  return localStorage.getItem("portal") === PORTAL_INTERNAL;
};

export const isExternalPortal = () => {
  return localStorage.getItem("portal") === PORTAL_EXTERNAL;
};
