import { CSSProperties, ReactNode } from "react"

import Box from "@mui/material/Box/Box"
import Typography from "@mui/material/Typography/Typography"

export interface CardQAPropsOnlyTextConfiguration {
  text: ReactNode
  /**
   * @default 'TEXT'
   */
  type?: 'TITLE' | 'TEXT'
}

export interface CardQAProps<
  TextConfigurationType extends CardQAPropsOnlyTextConfiguration = CardQAPropsOnlyTextConfiguration
> {
  containerStyle?: CSSProperties
  imageContainerStyle?: CSSProperties
  textConfigurations?: TextConfigurationType[]
}

export const CardQA = <
  TextConfigurationType extends CardQAPropsOnlyTextConfiguration = CardQAPropsOnlyTextConfiguration,
  PropType extends CardQAProps<TextConfigurationType> = CardQAProps<TextConfigurationType>
>({ containerStyle = {}, imageContainerStyle = {}, textConfigurations = [] }: PropType) => {
  return (
    <Box
      sx={{
        borderRadius: "1rem",
        padding: "1rem",
        backgroundColor: "var(--primary-color)",
        boxShadow: "0px 12px 12px 0px #EAE8E866",
        ...containerStyle
      }}
    >
      <Box
        sx={{
          fontSize: "14px",
          display: "flex",
          alignItems: "center",
          justifyContent: "end",
          mb: 3,
          ...imageContainerStyle
        }}
      >
        <img src="/qa.svg" width={70} alt="qa" />
      </Box>

      {textConfigurations.map((config, index) => {
        const { text, type = 'TEXT' } = config
        if (type === 'TEXT') {
          return (
            <Typography key={`card-qa-text-${index}`} fontSize="12px" color="white" fontWeight={300} marginBottom="1rem" lineHeight="1rem">{text}</Typography>
          )
        }
        return (
          <Typography key={`card-qa-title-${index}`} color="white" textTransform="uppercase" fontWeight={500} marginBottom="1rem" lineHeight="1rem">{text}</Typography>
        )
      })}
    </Box>
  )
}
