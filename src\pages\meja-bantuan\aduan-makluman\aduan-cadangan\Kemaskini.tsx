import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Box, Typography, Grid } from "@mui/material";
import Input from "../../../../components/input/Input";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import ModalSejarahAduan from "./ModalSejarahAduan";
import { toCapitalCase } from "@/helpers/string";
import {
  capitalizeWords,
  DocumentUploadType,
  FeedbackComplainType_New,
  FeedbackLevel,
  FeedbackStatus,
  GenderType,
  IdTypes,
  MALAYSIA,
  useQuery,
} from "@/helpers";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import { DialogConfirmation, FileUploader } from "@/components";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "16px",
  fontWeight: "600 !important",
};

const labelStyle = {
  fontSize: "14px",
  color: "#666666",
  fontWeight: "400 !important",
};

const Kemaskini: React.FC = () => {
  interface TrailValues {
    id: number | undefined;
    feedbackRecipientId: number | undefined;
    feedbackRecipientBranchId: number | undefined;
    feedbackNote: string | undefined;
    status: number | undefined;
    createdDate: string | undefined;
    feedbackRecipientName: string | undefined;
    feedbackType: string | undefined;
  }
  interface FormValues {
    id: number;
    name?: string;
    title?: string;
    details?: string;
    type?: string;
    status?: number;
    satisfaction?: string | null;
    techFeedback?: string | null;
    note?: string | undefined;
    contactNo?: string;
    homeContactNo?: string;
    email?: string;
    gender?: string;
    identificationNo?: string;
    identificationType?: string;
    durationOfComplaintsReceived?: number;
    city?: string;
    districtCode?: string;
    postcode?: string;
    stateCode?: string;
    parliament?: string | null;
    location?: string | null;
    picName?: string | null;
    createdBy?: number;
    createdDate?: string;
    modifiedBy?: number;
    modifiedDate?: string;
    creatorJppmBranchId?: number | null;
  }

  const { t } = useTranslation();
  const navigate = useNavigate();
  const { id } = useParams();
  const [decodedId, setDecodedId] = useState("");
  const [feedbackData, setFeedbackData] = useState<any>({});
  const [feedbackOfficer, setFeedbackOfficer] = useState<any>([]);
  const [trailListData, setTrailListData] = useState<any>([]);
  const [openModalSejarahAduan, setOpenModalSejarahAduan] = useState(false);
  const [openModalUpdate, setOpenModalUpdate] = useState(false);

  const [formValues, setFormValues] = useState<Partial<FormValues>>({});

  const [trailValues, setTrailValues] = useState<Partial<TrailValues>>({});

  const [
    feedbackComplainTypesTranslatedList,
    setFeedbackComplainTypesTranslatedList,
  ] = useState<{ value: string; label: string }[]>([]);

  const [feedbackStatusTranslatedList, setFeedbackStatusTranslatedList] =
    useState<{ value: number; label: string }[]>([]);

  const [branchList, setBranchList] = useState<
    { value: number; label: string }[]
  >([]);

  const [feedbackBranchList, setFeedbackBranchList] = useState<
    { value: number; label: string }[]
  >([]);

  const [FeedbackOfficerList, setFeedbackOfficerList] = useState<
    { value: number; label: string }[]
  >([]);

  const [idTypeTranslatedList, setIdTypeTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  const [genderTranslatedList, setGenderTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  const {
    data,
    isLoading,
    refetch: refetchFeedback,
  } = useCustom<any>({
    url: `${API_URL}/complaint/feedback/${decodedId}`,
    method: "get",
    queryOptions: {
      enabled: decodedId !== "",
      retry: false,
      cacheTime: 0,
    },
    successNotification(data) {
      if (data?.data?.data) {
        console.log("feedbackData", data?.data?.data);
        setFeedbackData(data?.data?.data);
        setFormValues(data?.data?.data);
      }
      return false;
    },
    errorNotification(error) {
      return false;
    },
  });

  const { data: FeedbackOfficer, isLoading: isLoadingFeedbackOfficer } =
    useCustom<any>({
      url: `${API_URL}/complaint/feedback/getFeedbackOfficer/${trailValues?.feedbackRecipientBranchId}`,
      method: "get",
      queryOptions: {
        enabled: trailValues?.feedbackRecipientBranchId !== undefined,
        retry: false,
        cacheTime: 0,
      },
      successNotification(data) {
        if (data?.data?.data) {
          setFeedbackOfficer(data?.data?.data);
        }
        return false;
      },
      errorNotification(error) {
        return false;
      },
    });

  const {
    data: trail,
    isLoading: isLoadingTrail,
    refetch,
  } = useCustom<any>({
    url: `${API_URL}/complaint/feedbacktrail/getAll`,
    method: "get",
    queryOptions: {
      enabled: decodedId !== "",
      retry: false,
      cacheTime: 0,
    },
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
      filters: [
        {
          field: "feedBackId",
          operator: "eq",
          value: Number(decodedId),
        },
      ],
    },
    successNotification(data) {
      console.log("trail data", data);
      const trailListData = data?.data?.data;
      if (trailListData?.length > 0) {
        setTrailListData(trailListData);
      }
      return false;
    },
    errorNotification(error) {
      return false;
    },
  });

  useEffect(() => {
    if (id) {
      setDecodedId(atob(id ?? ""));
    }
  }, [id]);

  const { data: branchListData, isLoading: isLoadingBranchListData } = useQuery(
    {
      url: "society/admin/branch/list",
      filters: [{ field: "includeAll", value: 1, operator: "eq" }],
    }
  );

  const {
    data: feedbackBranchListData,
    isLoading: isLoadingFeedbackBranchListData,
  } = useQuery({
    url: "society/lookup/jppmBranch/allActive",
  });

  useEffect(() => {
    const branchData = branchListData?.data?.data;
    if (branchData) {
      const newBList = [
        { value: 0, label: "-" },
        ...branchData.map((item: any) => ({
          value: Number(item.id),
          label: capitalizeWords(item.description, null, true),
        })),
      ];
      setBranchList(newBList);
    }
  }, [branchListData?.data]);

  useEffect(() => {
    const branchData = feedbackBranchListData?.data?.data;
    if (branchData) {
      const newBList = branchData.map((item: any) => ({
        value: Number(item.id),
        label: capitalizeWords(item.description, null, true),
      }));
      setFeedbackBranchList(newBList);
    }
  }, [feedbackBranchListData?.data]);

  useEffect(() => {
    if (feedbackOfficer?.length > 0) {
      const newBList = feedbackOfficer.map((item: any) => ({
        value: Number(item?.id),
        label: item?.name,
      }));
      setFeedbackOfficerList(newBList);
    }
  }, [feedbackOfficer]);

  useEffect(() => {
    const newFCTList = FeedbackComplainType_New.map((item) => ({
      ...item,
      label: toCapitalCase(t(item.label)),
    }));
    setFeedbackComplainTypesTranslatedList(newFCTList);

    const newFLList = FeedbackLevel.map((item) => ({
      ...item,
      label: t(item.label),
    }));

    const newFSList = FeedbackStatus.map((item) => ({
      ...item,
      label: t(item.label),
    }));

    setFeedbackStatusTranslatedList(newFSList);

    const newIDTList = IdTypes.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setIdTypeTranslatedList(newIDTList);

    const newGList = GenderType.map((item) => ({
      value: item.code,
      label: t(item.identName),
    }));
    setGenderTranslatedList(newGList);
  }, [t]);

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = addressList?.data?.data || [];

  const stateOption = addressData
    .filter((item: any) => item.pid === MALAYSIA)
    .map((item: any) => ({
      label: item.name,
      value: item.id,
    }));

  const handleChange = (e: any) => {
    const { name, value } = e.target;
    setTrailValues({
      ...trailValues,
      [name!]: value,
    });
  };

  const { mutate: create, isLoading: isLoadingCreate } = useCustomMutation();
  const Create = (): void => {
    const currentDate = new Date();
    const formattedDate = currentDate.toISOString().split("T")[0];
    create(
      {
        url: `${API_URL}/complaint/feedbacktrail/create`,
        method: "post",
        values: {
          feedbackId: Number(decodedId),
          feedbackRecipientId: trailValues?.feedbackRecipientId,
          feedbackRecipientBranchId: trailValues?.feedbackRecipientBranchId,
          feedbackNote: trailValues?.feedbackNote,
          status: trailValues?.status,
          feedbackType: trailValues?.feedbackType,
        },
        config: {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.data) {
            refetch();
            refetchFeedback();
            setOpenModalUpdate(false);
            return {
              message: data?.data?.msg,
              type: "success",
            };
          } else {
            return {
              message: t("error") + data?.data?.msg,
              type: "error",
            };
          }
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
      }
    );
  };

  const handleSubmit = () => {
    try {
      // if (trailValues?.id) {
      //   Edit();
      // } else {
      //   Create();
      // }
      Create();
    } catch (error) {
      console.error("error:", error);
      return null;
    }
  };

  const isFormValid =
    // !!trailValues?.feedbackRecipientBranchId &&
    // !!trailValues?.feedbackRecipientId &&
    !!trailValues?.feedbackType &&
    !!trailValues?.status &&
    !!trailValues?.feedbackNote?.trim();

  const handleReset = () => {
    setTrailValues((prev) => ({
      ...prev,
      feedbackRecipientBranchId: undefined,
      feedbackRecipientId: undefined,
      feedbackType: undefined,
      status: undefined,
      feedbackNote: "",
    }));
  };

  return (
    <>
      <Box
        sx={{
          px: 3,
          py: 4,
          mb: 1,
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            px: 4,
            py: 4,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {toCapitalCase(t("maklumatAduanCadangan"))}
          </Typography>

          <Grid container>
            <Input
              label={toCapitalCase(t("referenceNumber"))}
              name="referenceNumber"
              value={decodedId}
              disabled
            />
            <Input
              label={toCapitalCase(t("tarikhDanMasaAduanDihantar"))}
              name="createdDate"
              //@ts-ignore
              value={formValues?.createdDate}
              type="datetime"
              disabled
            />
            <Input
              label={toCapitalCase(t("tempohAduanDiterimaHari"))}
              name="durationOfComplaintsReceived"
              value={formValues?.durationOfComplaintsReceived}
              disabled
            />
            <Input
              label={toCapitalCase(t("jenis"))}
              name="type"
              value={feedbackData?.type}
              options={feedbackComplainTypesTranslatedList}
              type="select"
              disabled
            />
            <Input
              label={toCapitalCase(t("tajuk"))}
              name="title"
              value={formValues?.title}
              disabled
            />
            <Input
              label={toCapitalCase(t("butiranText"))}
              name="details"
              value={formValues?.details}
              disabled
            />
            <Input
              label={toCapitalCase(t("state"))}
              name="stateCode"
              type="select"
              options={stateOption}
              value={Number(formValues?.stateCode)}
              disabled
            />
            <Grid
              container
              spacing={2}
              marginBottom={1}
              alignItems="flex-start"
            >
              <Grid item xs={12} sm={4} sx={{ mt: 2 }}>
                <Typography sx={labelStyle}>
                  {toCapitalCase(t("lampiran"))}
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <FileUploader
                  key={decodedId}
                  type={DocumentUploadType.FEEDBACK}
                  withAuthHeaders={
                    localStorage.getItem("refine-auth") ? true : false
                  }
                  // uploadAfterSubmit={true}
                  // uploadAfterSubmitIndicator={feedbackId}
                  feedbackId={Number(decodedId)}
                  disabled
                  sxContainer={{
                    border: "1px solid #ccc",
                    background: "#fff",
                    mb: 3,
                  }}
                  validTypes={[
                    "application/pdf",
                    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "application/msword",
                    "text/plain",
                  ]}
                />
              </Grid>
            </Grid>
          </Grid>

          {/* <Box
            sx={{
              my: 3,
              display: "flex",
              justifyContent: "flex-end",
              gap: 2,
            }}
          >
            <ButtonPrimary onClick={() => setOpenModalUpdate(true)}>
              {toCapitalCase(t("update"))}
            </ButtonPrimary>
          </Box> */}
        </Box>
      </Box>

      <Box
        sx={{
          px: 3,
          py: 4,
          mb: 1,
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            px: 4,
            py: 4,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {toCapitalCase(t("maklumatPenghantar"))}
          </Typography>

          <Grid container>
            <Input
              label={toCapitalCase(t("fullName"))}
              name="name"
              value={formValues?.name}
              disabled
            />
            <Input
              label={toCapitalCase(t("idType"))}
              name="identificationType"
              type="select"
              options={idTypeTranslatedList}
              value={formValues?.identificationType}
              disabled
            />
            <Input
              label={toCapitalCase(t("identificationNumber"))}
              name="identificationNo"
              value={formValues?.identificationNo}
              disabled
            />
            <Input
              label={toCapitalCase(t("gender"))}
              type="select"
              name="gender"
              options={genderTranslatedList}
              value={feedbackData.gender}
              disabled
            />
            <Input
              label={toCapitalCase(t("email"))}
              name="email"
              value={feedbackData.email}
              disabled
            />
            <Input
              label={toCapitalCase(t("phoneNumber"))}
              name="contactNo"
              value={feedbackData.contactNo}
              disabled
            />
            <Input
              label={toCapitalCase(t("cawanganJPPM"))}
              name="cawanganJPPM"
              // type="select"
              // options={branchList}
              isLoadingData={isLoadingBranchListData}
              value={capitalizeWords(
                branchListData?.data?.data?.find(
                  (item: any) => item.id === feedbackData?.creatorJppmBranchId
                )?.description ?? "-",
                null,
                true
              )}
              disabled
            />
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          px: 3,
          py: 4,
          mb: 1,
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            px: 4,
            py: 4,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {toCapitalCase(t("chronologyOfComplaints"))}
          </Typography>

          <Grid container>
            <Grid item xs={12} sm={4.05} my={"auto"}>
              <Typography sx={labelStyle}>
                {toCapitalCase(t("auditTrailaduan"))}
              </Typography>
            </Grid>
            <Grid item xs={12} sm={7.95} my={"auto"}>
              <ButtonOutline
                onClick={() => setOpenModalSejarahAduan(true)}
                variant="text"
                disabled={trailListData?.length <= 0}
                startIcon={
                  <img
                    src={"/file-add.svg"}
                    alt="Filter Icon"
                    width="24"
                    height="24"
                  />
                }
                sx={{
                  flex: 1,
                  gap: 2,
                  height: "36px",
                  color: "#6B7280",
                  textTransform: "none",
                  textWrap: "nowrap",
                  fontWeight: 400,
                  padding: "18px",
                  width: "100%",
                  borderRadius: "20px",
                  "&:hover": {
                    backgroundColor: "#F9FAFB",
                  },
                }}
              >
                {toCapitalCase(t("paparSejarahAduanCadangan"))}
              </ButtonOutline>
            </Grid>
          </Grid>
        </Box>
      </Box>

      <Box
        sx={{
          px: 3,
          py: 4,
          mb: 1,
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            px: 4,
            py: 4,
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
          }}
        >
          <Typography variant="subtitle1" sx={sectionStyle}>
            {toCapitalCase(t("keputusanAduanCadangan"))}
          </Typography>

          <Grid container>
            <Input
              label={toCapitalCase(t("penerimaAduan"))}
              name="picName"
              value={feedbackData?.picName ? feedbackData?.picName : "-"}
              disabled
            />
            <Input
              label={toCapitalCase(t("negeriMenerimaAduan"))}
              name="negeriMenerimaAduan"
              value={Number(feedbackData?.stateCode)}
              options={stateOption}
              type="select"
              disabled
            />
            <Input
              label={toCapitalCase(t("flowToJPPMDivisionState"))}
              name="feedbackRecipientBranchId"
              value={Number(trailValues?.feedbackRecipientBranchId)}
              type="select"
              options={feedbackBranchList}
              onChange={handleChange}
              // required
            />
            <Input
              label={toCapitalCase(t("flowToOfficerAction"))}
              name="feedbackRecipientId"
              value={trailValues?.feedbackRecipientId}
              type="select"
              options={FeedbackOfficerList}
              onChange={handleChange}
              // required
            />
            <Input
              label={toCapitalCase(t("jenis"))}
              name="feedbackType"
              value={trailValues?.feedbackType}
              options={feedbackComplainTypesTranslatedList}
              onChange={handleChange}
              type="select"
              required
            />
            <Input
              label={toCapitalCase(t("status"))}
              name="status"
              value={trailValues?.status}
              type="select"
              options={feedbackStatusTranslatedList}
              onChange={handleChange}
              required
            />

            <Input
              multiline
              rows={3}
              label={toCapitalCase(t("catatanPegawaiCaraPengyelesaian"))}
              name="feedbackNote"
              value={trailValues?.feedbackNote}
              onChange={handleChange}
              required
            />
          </Grid>
          <Box
            sx={{
              mt: 3,
              display: "flex",
              justifyContent: "flex-end",
              gap: 2,
            }}
          >
            <ButtonOutline onClick={() => handleReset()}>
              {toCapitalCase(t("semula"))}
            </ButtonOutline>
            <ButtonPrimary
              disabled={!isFormValid}
              onClick={() => setOpenModalUpdate(true)}
            >
              {toCapitalCase(t("update"))}
            </ButtonPrimary>
          </Box>
        </Box>
      </Box>

      <ModalSejarahAduan
        open={openModalSejarahAduan}
        onClose={() => setOpenModalSejarahAduan(false)}
        onConfirm={() => {}}
        complaintData={trailListData}
      />
      <DialogConfirmation
        open={openModalUpdate}
        onClose={() => setOpenModalUpdate(false)}
        // title={toCapitalCase(t("pembayaranKaunter"))}
        // message={toCapitalCase(t("aduanCadanganDialogMsg1"))}
        isMutating={isLoadingCreate}
        onAction={() => handleSubmit()}
        // onCancel={() => setOpenModalUpdate(false)}
        // turn={true}
        // buttonPrimaryLabel="ya"
        // buttonTextLabel="no"
      />
    </>
  );
};

export default Kemaskini;
