import React, { createContext, useState, ReactNode } from "react";

interface SearchContextType {
  searchResult: any;
  setSearchResult: (result: any) => void;
  searchPendingResult: any;
  setSearchPendingResult: (result: any) => void;
  searchExternalResult: any;
  setSearchExternalResult: (result: any) => void;
  searchInactiveResult: any;
  setSearchInactiveResult: (result: any) => void;
  page: number;
  setPage: (result: any) => void;
  pageSize: number;
  setPageSize: (result: any) => void;
  pagePending: number;
  setPagePending: (result: any) => void;
  pageSizePending: number;
  setPageSizePending: (result: any) => void;
  pageInactive: number;
  setPageInactive: (result: any) => void;
  pageSizeInactive: number;
  setPageSizeInactive: (result: any) => void;
  pageExternal: number;
  setPageExternal: (result: any) => void;
  pageSizeExternal: number;
  setPageSizeExternal: (result: any) => void;
}

export const SearchContext = createContext<SearchContextType>({
  page: 1,
  setPage: () => {},
  pageSize: 5,
  setPageSize: () => {},
  pagePending: 1,
  setPagePending: () => {},
  pageSizePending: 5,
  setPageSizePending: () => {},
  pageInactive: 1,
  setPageInactive: () => {},
  pageSizeInactive: 5,
  setPageSizeInactive: () => {},
  pageExternal: 1,
  setPageExternal: () => {},
  pageSizeExternal: 10,
  setPageSizeExternal: () => {},
  searchResult: null,
  setSearchResult: () => {},
  searchInactiveResult: null,
  setSearchInactiveResult: () => {},
  searchExternalResult: null,
  setSearchExternalResult: () => {},
  searchPendingResult: null,
  setSearchPendingResult: () => {},
});

export const SearchProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [searchResult, setSearchResult] = useState<any>(null);
  const [searchPendingResult, setSearchPendingResult] = useState<any>(null);
  const [searchInactiveResult, setSearchInactiveResult] = useState<any>(null);
  const [searchExternalResult, setSearchExternalResult] = useState<any>(null);
  const [page, setPage] = useState<number>(1);
  const [pageSize, setPageSize] = useState<number>(5);
  const [pageInactive, setPageInactive] = useState<number>(1);
  const [pageSizeInactive, setPageSizeInactive] = useState<number>(5);
  const [pagePending, setPagePending] = useState<number>(1);
  const [pageSizePending, setPageSizePending] = useState<number>(5);
  const [pageExternal, setPageExternal] = useState<number>(1);
  const [pageSizeExternal, setPageSizeExternal] = useState<number>(10);

  return (
    <SearchContext.Provider
      value={{
        page,
        setPage,
        pageSize,
        setPageSize,
        pageInactive,
        setPageInactive,
        pageSizeInactive,
        setPageSizeInactive,
        pagePending,
        setPagePending,
        pageSizePending,
        setPageSizePending,
        pageExternal,
        setPageExternal,
        pageSizeExternal,
        setPageSizeExternal,
        searchResult,
        setSearchResult,
        searchPendingResult,
        setSearchPendingResult,
        searchInactiveResult,
        setSearchInactiveResult,
        searchExternalResult,
        setSearchExternalResult,
      }}
    >
      {children}
    </SearchContext.Provider>
  );
};
