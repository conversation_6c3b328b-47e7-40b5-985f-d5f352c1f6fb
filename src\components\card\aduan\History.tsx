import { Box, Button, Grid, Typography, useTheme } from "@mui/material"
import { useState } from "react";
import { useTranslation } from "react-i18next"
import { ButtonOutline } from "@/components/button";
import { AddIcon } from "@/components/icons";
import { formatDate } from "@/helpers";
import ModalSejarahAduan, { TrailValues } from "@/pages/meja-bantuan/aduan-makluman/aduan-cadangan/ModalSejarahAduan";

export interface FeedbackHistoryResponseBodyGet {
  creatorName: string
}

export interface CardAduanHistoryProps<
  Data extends FeedbackHistoryResponseBodyGet = FeedbackHistoryResponseBodyGet
> {
  /**
   * @default []
   */
  data?: Data[]
}

export const CardAduanHistory = <
  Data extends FeedbackHistoryResponseBodyGet = FeedbackHistoryResponseBodyGet,
  PropType extends CardAduanHistoryProps<Data> = CardAduanHistoryProps<Data>,
  ComplaintData extends TrailValues = TrailValues
>({ data = [{
  creatorName: "Sample Creator"
} as Data] }: PropType) => {
  const { t } = useTranslation();
  const theme = useTheme();
  const [showModal, setShowModal] = useState(false);

  const primary = theme.palette.primary.main;
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important"
  };
  const complaintData: ComplaintData[] = data.map<ComplaintData>((item, index) => ({
      id: index + 1,
      createdDate: formatDate("2025-02-20", undefined, { parseFormat: "YYYY-MM-DD" }),
      feedbackRecipientName: item?.creatorName ?? "Ahmad Ali",
      feedbackContent: (
        <div style={{ color: "#666", fontSize: 14 }}>
          <p>{`"Status" telah dikemaskini ke "Selesai"`}</p>
          <p>{`Aduan ini telah dirujuk ke agensi Bomba`}</p>
          <p>{`"Tindakan" telah dikemaskini ke "Rujuk Agensi Lain (ROA)"`}</p>
        </div>
      )
    } as ComplaintData))

  const toggleModal = () =>
    setShowModal(prev => !prev);

  return (
    <>
      <Box
        sx={{
          borderRadius: "1rem",
          backgroundColor: "white",
          boxShadow: "0 0 0.75rem rgba(234, 232, 232, 0.4)",
          padding: "1rem",
          display: "flex",
          flexDirection: "column",
          rowGap: "1rem",
          marginBottom: "0.5rem"
        }}
      >
        <Box
          sx={{
            border: "1px solid #DADADA",
            borderRadius: "0.5rem",
            padding: "1.5rem"
          }}
        >
          <Typography className="title" mb="1.25rem">
            {t("complaintHistory")}
          </Typography>
          <Grid container spacing={2}>
            <Grid item justifyContent="center" md={4} xs={12}>
              <Typography sx={labelStyle}>
                {t("auditTrailaduan")}
              </Typography>
            </Grid>
            <Grid item md={8} xs={12}>
              <Button
                variant="outlined"
                fullWidth
                startIcon={<AddIcon color={primary} />}
                sx={{ textTransform: "capitalize" }}
                onClick={toggleModal}
              >
                {t("paparSejarahAduanCadangan")}
              </Button>
            </Grid>
          </Grid>
        </Box>
      </Box>
      <ModalSejarahAduan
        title={t("complaintHistory")}
        open={showModal}
        onClose={toggleModal}
        complaintData={complaintData}
        showFullViewButton={false}
      />
    </>
  )
}
