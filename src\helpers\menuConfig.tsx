import DashboardIcon from "@mui/icons-material/Dashboard";
import SettingsIcon from "@mui/icons-material/Settings";
import { useTranslation } from "react-i18next";

import {
  CawangIcon,
  DokumenIcon,
  EnforcementIcon,
  PertubuhanIcon,
  PematuhanIcon,
  RayuanIcon,
  CarianIcon,
  MaklumBalasIcon,
  HelpIcon,
  PaymentIcon,
  ReportIcon,
} from "../components/icons";
import {
  AccountTree,
  Assignment,
  Business,
  Gavel,
  Settings,
  DoNotDisturbAlt,
  Forum,
} from "@mui/icons-material";
import { Dispatch, SetStateAction } from "react";
import { TrainingIcon } from "@/components/icons/training";
import {HebahanIcon} from "@/components/icons/hebahan";

export interface NavItem {
  id: string;
  label: string;
  path: string;
  permissions: string[];
  icon?: (isActive: boolean) => React.ReactNode | string;
  action?: () => void;
  keyword?: string[];
  subItems?: NavItem[];
  menuType?: "administration" | "tetapan";
}

export const getNavItem = (menuItems: NavItem[], id: string): NavItem[] => {
  for (const item of menuItems) {
    if (item.id === id) {
      return item.subItems ?? [];
    }
  }

  for (const item of menuItems) {
    if (item.subItems) {
      const found = getNavItem(item.subItems, id);
      if (found) return found;
    }
  }

  return [];
};

// Menu - Pengguna Dalaman
export const MENU_INTERNAL = (
  handleComingSoon?: Dispatch<SetStateAction<boolean>>
): NavItem[] => {
  const { t } = useTranslation();

  return [
    {
      id: "dashboard",
      label: "Dashboard",
      path: "/internal-user",
      icon: (active: boolean) => (
        <DashboardIcon sx={{ color: active ? "#fff" : "#55556D" }} />
      ),
      permissions: [],
    },
    {
      id: "pertubuhan",
      label: t("pertubuhan"),
      path: "/pengurus-pertubuhan",
      icon: (active: boolean) => (
        <PertubuhanIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [], // TODO: Set permission
      keyword: [
        "/pengurus-pertubuhan",
        "pengurusan-pertubuhan",
        "/senarai/maklumat",
        "/senarai/perlembagaan/maklumat",
        "/senarai/perlembagaan/migrasi",
        "/senarai/mesyuarat",
        "/senarai/ajk/jawatankuasa",
        "/senarai/penyataTahunan",
        "/senarai/ajk/ahli",
        "/senarai/ajk/pemegang-amanah",
        "/senarai/ajk/pegawai",
        "/senarai/ajk/juruaudit",
        "/senarai/ajk/aliran-tugas",
        "/senarai/dokumen",
      ],
      subItems: [
        {
          id: "keputusan-pertubuhan",
          label: "Keputusan pertubuhan",
          path: "/pengurus-pertubuhan/keputusan-pertubuhan",
          icon: (active: boolean) => <Assignment />,
          permissions: [],
          subItems: [
            {
              id: "keputusan-induk",
              label: "Keputusan Induk",
              path: "/pengurus-pertubuhan/keputusan-induk",
              icon: (active: boolean) => <Business />,
              permissions: [],
            },
            {
              id: "pembubaran-cawangan",
              label: "Keputusan Cawangan",
              path: "/pengurus-pertubuhan/pembubaran-cawangan",
              icon: (active: boolean) => <AccountTree />,
              permissions: [],
            },
            {
              id: "rayuan",
              label: "Rayuan",
              path: "/pengurus-pertubuhan/rayuan",
              icon: (active: boolean) => <Gavel />,
              permissions: [],
            },
            {
              id: "senarai-hitam",
              label: "Senarai Hitam",
              path: "/pengurus-pertubuhan/senarai-hitam",
              icon: (active: boolean) => <DoNotDisturbAlt />,
              permissions: [],
            },
            {
              id: "kuiri",
              label: "Kuiri",
              path: "/pengurus-pertubuhan/kuiri",
              icon: (active: boolean) => <Forum />,
              permissions: [],
            },
          ],
        },
        {
          id: "maklumat-pertubuhan",
          label: "Maklumat pertubuhan",
          path: "/pengurus-pertubuhan/maklumat-pertubuhan",
          icon: (active: boolean) => <Assignment />,
          permissions: [], //Change based on new permission name when added
        },
        {
          id: "semakan-umum",
          label: "Semakan umum",
          // path: "/pengurus-pertubuhan/semakan-umum",
          path: "/pengurus-pertubuhan/penyelenggara-pertubuhan",
          icon: (active: boolean) => <Assignment />,
          permissions: [], //Change based on new permission name when added
        },
        {
          id: "penyelenggara-pertubuhan",
          label: "Penyelenggaraan pertubuhan",
          path: "/pengurus-pertubuhan/penyelenggara-pertubuhan",
          icon: (active: boolean) => <Settings />,
          permissions: [],
        },
      ],
    },
    // {
    //   label: "Cawangan",
    //   path: "pertubuhan/paparan-pertubuhan/cawangan/main-list-cawangan",
    //   // action: () => setShowComingSoon(true),
    //   icon: (active: boolean) => (
    //     <CawangIcon
    //       sx={{
    //         color: active ? "#fff" : "#55556D",
    //         width: "21px",
    //         height: "21px",
    //       }}
    //     />
    //   ),
    // },
    {
      id: "selenggara",
      label: t("selenggara"),
      path: "/pertubuhan/settings",
      icon: (active: boolean) => (
        <SettingsIcon sx={{ color: active ? "#fff" : "#55556D" }} />
      ),
      permissions: [], //TODO: Set permission
      keyword: ["/settings"],
      subItems: [
        {
          id: "pengguna",
          label: t("pengurusanPengguna"),
          path: "/pertubuhan/settings/list-pengurusan-pengguna-jpm",
          permissions: [],
          keyword: [
            "list-pengurusan-pengguna-jpm",
            "list-pengurusan-pengguna-luar",
            "create-pengurusan-pengguna-jpm",
            "ro-approval-pengurusan-pengguna-jpm",
            "edit-pengurusan-pengguna-luar",
            "mail-pengurusan-pengguna-luar",
          ],
        },
        {
          id: "pengurusanPeranan",
          label: t("pengurusanPeranan"),
          path: "/pertubuhan/settings/category-pengurusan-peranan-jpm",
          permissions: [],
          keyword: [
            "pengurusan-peranan",
            "category-pengurusan-peranan-jpm",
            "pengurusan-peranan-roleHandling",
            // "create-category-pengurusan-peranan-luar",
          ],
        },
        {
          id: "umum",
          label: t("penyelenggaraanUmum"),
          path: "/pertubuhan/settings/penyelenggaran-umum",
          permissions: [],
        },
        {
          id: "integrasi",
          label: t("pengurusanIntegrasi"),
          path: "/pertubuhan/settings/integrasi",
          permissions: [],
        },
        {
          id: "audit-trail",
          label: t("auditTrail"),
          path: "/pertubuhan/settings/audit-trial",
          permissions: [],
        },
        {
          id: "piagam",
          label: t("ketetapanPiagam"),
          path: "#",
          permissions: [],
        },
      ],
    },
    {
      id: "meja-bantuan",
      label: t("mejaBantuan"),
      path: "/meja-bantuan/aduan-makluman",
      icon: (active: boolean) => (
        <HelpIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
      keyword: ["/meja-bantuan"],
      subItems: [
        {
          id: "aduan",
          label: t("aduanMakluman"),
          path: `/meja-bantuan/aduan-makluman`,
          icon: (active: boolean) => "/aduan-makluman.png",
          permissions: [],
          subItems: [
            {
              id: "aduan-cadangan",
              label: t("aduanCadangan"),
              path: `meja-bantuan/aduan-makluman/aduan-cadangan`,
              icon: (active: boolean) => "/pembayaran-kaunter.png",
              permissions: [],
            },
            {
              id: "kepuasan-pelanggan",
              label: t("kepuasanPelanggan"),
              path: `meja-bantuan/aduan-makluman/kepuasan-pelanggan`,
              icon: (active: boolean) => "/rekod-pembayaran.png",
              permissions: [],
            },
            {
              id: "faq-makluman",
              label: t("faqMakluman"),
              path: `meja-bantuan/aduan-makluman/faq-makluman`,
              icon: (active: boolean) => "/semakan-kaunter-individu.png",
              permissions: [],
            },
          ],
        },
        // {
        //   id: "perkhidmatan",
        //   label: t("perkhidmatanKaunter"),
        //   path: `/meja-bantuan/perkhidmatan`,
        //   icon: (active: boolean) => "/perkhidmatan-kaunter.png",
        //   permissions: [],
        //   subItems: [
        //     {
        //       id: "pembayaran-kaunter",
        //       label: "pembayaranKaunter",
        //       path: `meja-bantuan/perkhidmatan/pembayaran-kaunter`,
        //       icon: (active: boolean) => "/pembayaran-kaunter.png",
        //       permissions: [],
        //     },
        //     {
        //       id: "rekod-pembayaran",
        //       label: "rekodPembayaran",
        //       path: `meja-bantuan/perkhidmatan/rekod-pembayaran`,
        //       icon: (active: boolean) => "/rekod-pembayaran.png",
        //       permissions: [],
        //     },
        //     // {
        //     //   id: "semakan-kaunter-individu",
        //     //   label: t("semakanKaunterIndividu"),
        //     //   path: `meja-bantuan/perkhidmatan/semakan-kaunter-individu`,
        //     //   icon: "/semakan-kaunter-individu.png",
        //     //   permissions: [],
        //     // },
        //   ],
        // },
        {
          id: "feedback",
          label: t("feedback"),
          path: `/feedback`,
          permissions: [],
        },
      ],
    },
    {
      id: "penguatkuasaan",
      label: t("enforcement"),
      path: "/penguatkuasaan",
      icon: (active: boolean) => (
        <EnforcementIcon
          bgcolor={active ? "#fff" : "#55556D"}
          style={{
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
      keyword: ["/penguatkuasaan"],
    },
    {
      id: "warta",
      label: t("warta"),
      /**
       * @todo change the path if warta module is ready
       */
      path: "/coming_soon",
      icon: (active: boolean) => (
        <DokumenIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
    },
    {
      id: "pembayaran",
      label: t("pembayaran"),
      path: "pembayaran",
      keyword: ["/meja-bantuan"],
      icon: (active: boolean) => (
        <PaymentIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
      subItems: [
        {
          id: "pembayaran-kaunter",
          label: "pembayaranKaunter",
          path: `pembayaran/perkhidmatan/pembayaran-kaunter`,
          icon: (active: boolean) => "/pembayaran-kaunter.png",
          permissions: [],
        },
        {
          id: "rekod-pembayaran",
          label: "rekodPembayaran",
          path: `pembayaran/perkhidmatan/rekod-pembayaran`,
          icon: (active: boolean) => "/rekod-pembayaran.png",
          permissions: [],
        },
      ],
    },
    {
      id: "latihan",
      label: t("latihan"),
      path: "/latihan-internal",
      //action: () => handleComingSoon?.(true),
      icon: (active: boolean) => (
        <TrainingIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
    },
    {
      id: "hebahan",
      label: t("hebahan"),
      path: "/hebahan-internal",
      //action: () => handleComingSoon?.(true),
      icon: (active: boolean) => (
        <HebahanIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
    },
    {
      id: "laporan",
      label: t("laporan"),
      path: "/laporan/statistik",
      icon: (active: boolean) => (
        <ReportIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
      keyword: ["/laporan"],
    }
  ];
};

// Menu - Pengguna Pertubuhan
export const MENU_ORGANIZATION = (
  handleComingSoon?: Dispatch<SetStateAction<boolean>>
): NavItem[] => {
  const { t } = useTranslation();

  return [
    {
      id: "dashboard",
      label: "Dashboard",
      path: "/pertubuhan",
      icon: (active: boolean) => (
        <DashboardIcon sx={{ color: active ? "#fff" : "#55556D" }} />
      ),
      permissions: [],
    },
    {
      id: "pertubuhan",
      label: t("pertubuhan"),
      path: "pengurus-pertubuhan/pertubuhan",
      icon: (active: boolean) => (
        <PertubuhanIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
      keyword: [
        "/pengurus-pertubuhan",
        "pengurusan-pertubuhan",
        "/senarai/maklumat",
        "/senarai/perlembagaan/maklumat",
        "/senarai/perlembagaan/migrasi",
        "/senarai/mesyuarat",
        "/senarai/ajk/jawatankuasa",
        "/senarai/ajk/ahli",
        "/senarai/ajk/pemegang-amanah",
        "/senarai/ajk/pegawai",
        "/senarai/ajk/juruaudit",
        "/senarai/ajk/aliran-tugas",
        "/senarai/penyataTahunan",
        "/senarai/dokumen",
        "/senarai/pembubaran",
      ],
    },
    {
      id: "cawangan",
      label: t("cawangan"),
      path: "pertubuhan/paparan-pertubuhan/cawangan/main-list-cawangan",
      // action: () => setShowComingSoon(true),
      icon: (active: boolean) => (
        <CawangIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
      keyword: ["/cawangan"],
    },
    {
      id: "rayuan",
      label: t("rayuan"),
      path: "pertubuhan/paparan-pertubuhan/rayuan/list-data",
      // action: () => setShowComingSoon(true),
      icon: (active: boolean) => (
        <RayuanIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
      keyword: ["/rayuan"],
    },
    {
      id: "carian",
      label: t("carian"),
      path: "/carian",
      // action: () => setShowComingSoon(true),
      icon: (active: boolean) => (
        <CarianIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
      keyword: ["/carian"],
    },
    {
      id: "feedback",
      label: t("jenis_MaklumBalas"),
      path: "/feedback",
      // action: () => setShowComingSoon(true),
      icon: (active: boolean) => (
        <MaklumBalasIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "24px",
            height: "24px",
          }}
        />
      ),
      permissions: [],
      keyword: ["/feedback"],
    },
    {
      id: "perlembagaan",
      label: t("perlembagaan"),
      /**
       * @todo change the path if perlembagaan module is ready
       */
      path: "/coming_soon",
      // action: () => handleComingSoon?.(true),
      icon: (active: boolean) => (
        <PematuhanIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
    },
    {
      id: "warta",
      label: t("warta"),
      /**
       * @todo change the path if warta module is ready
       */
      path: "/coming_soon",
      icon: (active: boolean) => (
        <DokumenIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
    },
    {
      id: "latihan",
      label: t("latihan"),
      path: "/latihan",
      //action: () => handleComingSoon?.(true),
      icon: (active: boolean) => (
        <TrainingIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
    },
    {
      id: "hebahan",
      label: t("hebahan"),
      path: "/hebahan",
      //action: () => handleComingSoon?.(true),
      icon: (active: boolean) => (
        <HebahanIcon
          sx={{
            color: active ? "#fff" : "#55556D",
            width: "21px",
            height: "21px",
          }}
        />
      ),
      permissions: [],
    }
  ];
};
