import { useState, useCallback } from "react";
import reportService, {
  IRegisteredUserEmbedUrlRequest,
  IRegisteredUserEmbedUrlResponse,
  IDashboardItem,
  IDashboardQueryParams,
} from "@/services/reportService";
import { IApiResponse } from "@/types/api";

interface UseReportServiceReturn {
  // State
  loading: boolean;
  error: string | null;
  data: any;

  // Report Methods
  getRegisteredUserEmbedUrl: (request: IRegisteredUserEmbedUrlRequest) => Promise<IApiResponse<IRegisteredUserEmbedUrlResponse>>;
  getDashboards: (params: IDashboardQueryParams) => Promise<IApiResponse<IDashboardItem[]>>;

  // Utility
  resetState: () => void;
}

/**
 * Custom hook for report service operations
 * Provides a React-friendly interface to the report service
 */
export const useReportService = (): UseReportServiceReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);

  // Helper function to handle async operations
  const handleAsyncOperation = useCallback(async <T>(
    operation: () => Promise<IApiResponse<T>>
  ): Promise<IApiResponse<T>> => {
    setLoading(true);
    setError(null);

    try {
      const result = await operation();
      setData(result.data);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An error occurred";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get registered user embed URL
  const getRegisteredUserEmbedUrl = useCallback(
    (request: IRegisteredUserEmbedUrlRequest) =>
      handleAsyncOperation(() => reportService.getRegisteredUserEmbedUrl(request)),
    [handleAsyncOperation]
  );

  // Get dashboards
  const getDashboards = useCallback(
    (params: IDashboardQueryParams) =>
      handleAsyncOperation(() => reportService.getDashboards(params)),
    [handleAsyncOperation]
  );

  // Reset state
  const resetState = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
    reportService.resetState();
  }, []);

  return {
    // State
    loading,
    error,
    data,

    // Methods
    getRegisteredUserEmbedUrl,
    getDashboards,
    resetState,
  };
};

export default useReportService;
