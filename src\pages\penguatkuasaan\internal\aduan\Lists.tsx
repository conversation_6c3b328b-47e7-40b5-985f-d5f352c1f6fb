import { Button<PERSON>ut<PERSON>, Form<PERSON>ieldRow, Label, TextFieldController } from "@/components"
import { DataGridUI } from "@/components/datagrid/UI"
import { getMalaysiaAddressList, toBase64, useDataGrid } from "@/helpers"
import { Box, IconButton, MenuItem, Typography } from "@mui/material"
import { GridColDef } from "@mui/x-data-grid"
import { useForm } from "react-hook-form"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import { EditIcon, TrashIcon } from "@/components/icons";
import dayjs from "@/helpers/dayjs"
import { PenguatkuasaanSekatanLiabilitiResponseBodyGet } from "../sekatan-liabiliti/Base"
import DownloadIcon from "@/assets/svg/icon-download.svg?react"
import EyeIcon from "@/assets/svg/icon-eye-primary.svg?react"
import { useControllerFormManagementCoreConfirmationBeforeSubmit } from "@/controllers"
import { useState } from "react"

export interface ComplaintListRequestBodyGet {
  id: number
  referenceNo: string
  topic: string
  receivedDate: string | Date
  receivedTemp: number
  state: string
}

export interface ComplaintListRequestBodyParams {
  negeri: number | null
  referenceNo: string | null
  receivedDate: string | null
}

export const PenguatkuasaanInternalAduanLists = <
  /**
   * @todo use endpoint data for aduan instead of sekatan liabiliti one.
   */
  // TableData extends ComplaintListRequestBodyGet = ComplaintListRequestBodyGet,
  TableData extends PenguatkuasaanSekatanLiabilitiResponseBodyGet = PenguatkuasaanSekatanLiabilitiResponseBodyGet,
  Params extends ComplaintListRequestBodyParams = ComplaintListRequestBodyParams
>() => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { getValues, control, reset } = useForm<Params>({
    defaultValues: {
      // @ts-expect-error
      negeri: null,
      referenceNo: null,
      receivedDate: null
    }
  });
  const [selectedData, setSelectedData] = useState<TableData | null>(null);
  const { negeri, referenceNo, receivedDate } = getValues();
  const { dataGridProps: { sx, ...dataGridProps } } = useDataGrid<TableData>({
    /**
     * @todo use endpoint data for aduan instead of sekatan liabiliti one.
     */
    resource: "society/blacklist/getAll",
    meta: {
      params: {
        ...(negeri && { negeri }),
        ...(referenceNo && { referenceNo }),
        ...(receivedDate && { receivedDate }),
      },
    },
  });
  const getConfirmationTitle = (withHTMLTag = true) => {
    if (!selectedData) {
      return ""
    }
    const initialTitle = `${selectedData.name} (${selectedData?.branchNo ?? selectedData?.societyNo ?? ""})`
    return withHTMLTag
      ? `<b>${initialTitle}?</b>`
      : initialTitle
  }
  const {
      toggleDialogOpen,
      ConfirmationModal
    } = useControllerFormManagementCoreConfirmationBeforeSubmit({
      onAction: async () => {
        const { resolve, promise } = Promise.withResolvers();
        setTimeout(() => {
          resolve(true);
        }, 3000);
        await promise;
        return undefined;
      },
      redirectURL: null,
      onConfirmationText: t("areYouSureYouWantToDeleteThisDraftComplaint", {
        title: getConfirmationTitle()
      }),
      onSuccessText: t("draftComplaintSuccessfullyDeleted", {
        title: getConfirmationTitle(false)
      }),
      onClose: () => setSelectedData(null)
    })

const columns: GridColDef<TableData>[] = [
    {
      field: "name",
      headerName: t("referenceNumber"),
      headerAlign: "center",
      minWidth: 51.6 * 4
    },
    {
      field: "societyNo",
      headerName: t("tajuk"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row }) => row?.branchNo ?? row?.societyNo ?? "-",
      minWidth: 51.6 * 4
    },
    {
      field: "effectiveDate",
      headerName: t("tarikhDiterima"),
      renderCell: ({ row }) => row?.effectiveDate ?? "-",
      align: "center",
      minWidth: 51.6 * 3
    },
    {
      field: "section",
      headerName: t("acceptedPeriodDays"),
      headerAlign: "center",
      align: "center",
      minWidth: 51.6 * 4,
      renderCell: ({ row }) => {
        const initialValue = row?.section.split("_")
        const [_section, ...values] = initialValue
        return values.reduce((acc, val, index) => (`${acc}${index === 0 ? val : `(${val.toLowerCase()})`}`), "") ?? "-"
      }
    },
    {
      field: "removalStatus",
      headerName: t("status"),
      headerAlign: "center",
      align: "center",
      minWidth: 51.6 * 3,
      renderCell: ({ id }) => {
        const code = generateStatusFromRandomData(id as number);
        return (
          <Typography
            className="status-pertubuhan-text"
            sx={{
              backgroundColor: "#fff",
              border: `2px solid ${getColorFromStatus(code)}`,
              textAlign: "center"
            }}
          >
            {t(getTranslationTextFromStatus(code))}
          </Typography>
        )
      }
    },
    {
      field: "actions",
      headerName: t("action"),
      headerAlign: "center",
      align: "center",
      renderCell: ({ row, id }) => {
        const code = generateStatusFromRandomData(id as number);
        const getURL = (mode: "UPDATE" | "VIEW" | "VIEW_WITH_ACTIONS" = "VIEW") => `${mode === "UPDATE" ? "update/" : mode === "VIEW_WITH_ACTIONS" ? "./" : "view/"}${toBase64(row.id.toString())}`
        const whitelistDate = row?.whitelistDate ? dayjs(row.whitelistDate, "DD-MM-YYYY") : null
        const showEditIcon = whitelistDate !== null && dayjs().isBefore(whitelistDate)

        const handleButtonDeleteClicked = () => {
          setSelectedData(row);
          toggleDialogOpen();
        }
        return (
          <div style={{
            display: "flex",
            columnGap: "0.5rem"
          }}>
            {code === "DRAFT" && (
              <>
                <IconButton
                  color="primary"
                  sx={{ width: "3rem", height: "3rem" }}
                  onClick={() => navigate(getURL("UPDATE"))}
                >
                  <EditIcon width="1rem" height="1rem" />
                </IconButton>
                <IconButton
                  color="error"
                  sx={{ width: "3rem", height: "3rem" }}
                  onClick={handleButtonDeleteClicked}
                >
                  <TrashIcon width="1rem" height="1rem" />
                </IconButton>
                <ConfirmationModal />
              </>
            )}
            {code !== "DRAFT" && (
              <>
                <IconButton
                  color="primary"
                  sx={{ width: "3rem", height: "3rem" }}
                  onClick={() => navigate(getURL("VIEW_WITH_ACTIONS"))}
                >
                  <DownloadIcon width="1.5rem" height="1.5rem" />
                </IconButton>
                <IconButton
                  color="primary"
                  sx={{ width: "3rem", height: "3rem" }}
                  onClick={() => navigate(getURL())}
                >
                  <EyeIcon width="1.5rem" height="1.5rem" />
                </IconButton>
              </>
            )}
          </div>
        )
      },
    },
  ];

  const generateStatusFromRandomData = (id: number) => {
    const code = id % 5;
    switch (code) {
      case 1:
        return "NEW"
      case 2:
        return "INQUIRY"
      case 3:
        return "INVESTIGATION"
      case 4:
        return "DONE"
      case 0:
      default:
        return "DRAFT"
    }
  }
  const getColorFromStatus = (code: ReturnType<typeof generateStatusFromRandomData>) => {
    switch (code) {
      case "NEW":
        return "#3483F9";
      case "DONE":
        return "var(--primary-color)";
      case "INQUIRY":
        return "#9747FF";
      case "INVESTIGATION":
        return "#FD8A37";
      case "DRAFT":
      default:
        return "var(--indicator-yellow)";
    }
  }
  const getTranslationTextFromStatus = (code: ReturnType<typeof generateStatusFromRandomData>) => {
    switch (code) {
      case "NEW":
        return "NEW";
      case "DONE":
        return "COMPLETE";
      case "INQUIRY":
        return "inquiry";
      case "INVESTIGATION":
        return "investigation";
      case "DRAFT":
      default:
        return "draft";
    }
  }

  return (
    <div
      style={{
        display: "flex",
        rowGap: "0.5rem",
        flexDirection: "column"
      }}
    >
      <Box
        sx={{
          borderRadius: "1rem",
          p: "1rem",
          backgroundColor: "white"
        }}
      >
        <Box
          sx={{
            borderRadius: "1rem",
            p: "1.5rem",
            border: "1px solid #DADADA"
          }}
        >
          <Typography className="title">{t("complaintSearch")}</Typography>
          <FormFieldRow
            label={
              <Label text={t("negeri")} />
            }
            // @ts-expect-error
            value={<TextFieldController control={control} name="negeri" select limitWord={undefined}>
              {getMalaysiaAddressList()
                .map((item, index) =>
                  <MenuItem key={`negeri-${index}`} value={item.code}>{item.name}</MenuItem>
                )
              }
            </TextFieldController>
            }
          />
          <FormFieldRow
            label={
              <Label text={t("referenceNumber")} />
            }
            // @ts-expect-error
            value={<TextFieldController control={control} name="referenceNo" />}
          />
          <FormFieldRow
            label={
              <Label text={t("tarikhTerimaAduan")} />
            }
            // @ts-expect-error
            value={<TextFieldController control={control} name="receivedDate" />}
          />
          <div
            style={{
              display: "flex",
              justifyContent: "flex-end"
            }}
          >
            <ButtonOutline onClick={() => reset()}>
              {t("semula")}
            </ButtonOutline>
          </div>
        </Box>
      </Box>
      <Box
        sx={theme => ({
          borderRadius: "1rem",
          p: "1rem",
          backgroundColor: theme.palette.primary.main,
          color: "white",
          textAlign: "center",
          display: "flex",
          flexDirection: "column"
        })}
      >
        <Typography
          sx={{
            fontSize: "2.125rem"
          }}
        >
          {dataGridProps.rowCount}
        </Typography>
        <Typography>Jumlah Keseluruhan Aduan</Typography>
      </Box>
      <Box
        sx={{
          borderRadius: "1rem",
          p: "1rem",
          backgroundColor: "white"
        }}
      >
        <Box
          sx={{
            borderRadius: "1rem",
            p: "1.5rem",
            border: "1px solid #DADADA"
          }}
        >
          <Typography className="title">{t("complaintList")}</Typography>
          <DataGridUI
            {...dataGridProps}
            autoHeight
            columns={columns}
            noResultMessage={t("noData")}
          />
        </Box>
      </Box>
    </div>
  )
}
