import { Box, Grid, Typography } from "@mui/material"
import { useMemo } from "react";
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom";

import { FeedbackPublicSelectLanguage } from "@/components"
import { FeedbackMainCard, FeedbackMainCardProps } from "@/components/card/FeedbackMain";

import { useSelector } from "react-redux";
import { getUserPortal, getUserToken } from "@/redux/userReducer";
import { toBase64 } from "@/helpers";

import feedbackIcon001 from "/feedbackBoxIcon1.svg";
import feedbackIcon002 from "/feedbackBoxIcon2.svg";
import feedbackIcon003 from "/feedbackBoxIcon3.svg";

export const FeedbackPublicMain = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const userToken = useSelector(getUserToken);
  const userPortal = useSelector(getUserPortal);

  const feedbackLinks: FeedbackMainCardProps[] = useMemo(() => ([
    {
      imageIcon: feedbackIcon001,
      title: t("cadanganMaklumbalasbaru"),
      onClick: () => navigate("/feedback/cadanganBaru"),
    },
    {
      imageIcon: feedbackIcon002,
      title: t("SemakCadangan"),
      onClick: () => navigate("/feedback/cadanganSemak"),
    },
    {
      imageIcon: feedbackIcon003,
      title: t("complaintOfOrganizationalMisconduct"),
      onClick: () => {
        if (!userToken) {
          navigate("aduan")
          return;
        }
        if (userPortal === 1) {
          const id = Math.floor(Math.random() * 1000_000)
          const encodedId = toBase64(id.toString())
          navigate(`aduan/create/${encodedId}`)
        }
      },
    }
  ]), [t, navigate])

  return (
    <Grid item sx={{
      display: "flex",
      flex: 1
    }}>
      <Box
        sx={(theme) => ({
          display: "flex",
          flexDirection: "column",
          alignItems: "center",
          justifyContent: "space-between",
          width: "100%",
          maxWidth: "1200px",
          flex: 1,
          [theme.breakpoints.up("md")]: {
            margin: "auto",
            flexDirection: "row",
            alignItems: "stretch",
          },
        })}
      >
        <Box
          sx={{
            display: "flex",
            color: "white",
            textAlign: { xs: "center", md: "left" },
            flexDirection: "column",
          }}
        >
          {/* language select */}
          <FeedbackPublicSelectLanguage />
          <Box
            sx={(theme) => ({
              display: "flex",
              width: "55%",
              color: "white",
              textAlign: { xs: "center", md: "left" },
              flexDirection: "column",
              alignItems: "center",
              height: "100%",
              justifyContent: "center",
              [theme.breakpoints.down("md")]: {
                py: "1rem",
                width: "100%"
              }
            })}
          >
            <Typography
              variant="h4"
              component="h1"
              gutterBottom
              sx={{
                mb: 0,
                color: "#fff",
                fontFamily: "Poppins, sans-serif",
                fontSize: { xs: "25px" },
                fontWeight: 500,
                lineHeight: "30px",
                textShadow: "black 0 0 0.5rem"
              }}
            >
              {t("feedbackHerotext")}
            </Typography>
          </Box>
        </Box>

        <Box
          sx={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            width: { xs: "100%", sm: "398px" },
            gap: 2,
          }}
        >
          {feedbackLinks?.map((item, index) => (
            <FeedbackMainCard
              key={`feedback-link-${index + 1}`}
              {...item}
            />
          ))}
        </Box>
      </Box>
    </Grid>
  )
}
