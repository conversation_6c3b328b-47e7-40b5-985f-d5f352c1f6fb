import { useTranslation } from "react-i18next";
import { date, number, object, string } from "yup";

export function useFormManagementCommitteeNonCitizenSocietyByIdValidationSchema() {
  const { t } = useTranslation();

  const getValidationSchema = () =>
    object()
      .shape({
        societyNo: string()
          .notRequired(),
        societyName: string()
          .notRequired(),
        name: string()
          .required(),
        citizenshipStatus: number()
          .required(),
        identificationType: string()
          .required(),
        identificationNo: string()
          .required(),
        gender: string()
          .required(),
        applicantCountryCode: number()
          .required(),
        residentialAddress: string()
          .required(),
        residentialCity: string()
          .notRequired()
          .nullable(),
        visaNo: string()
          .notRequired()
          .nullable(),
        visaExpirationDate: date()
          .notRequired()
          .nullable(),
        permitNo: string()
          .notRequired()
          .nullable(),
        permitExpirationDate: date()
          .notRequired()
          .nullable(),
        tujuanDMalaysia: string()
          .required(),
        stayDurationDigit: number()
          .min(1, () => t("fieldRequired"))
          .required(),
        stayDurationUnit: string()
          .required(),
        designationCode: string()
          .required(),
        activeCommitteeId: number()
          .required(),
        otherDesignationCode: string()
          .required()
      })
      .required()

  return {
    getValidationSchema
  }
}
