import React, { useEffect, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import {
  Box,
  Typography,
  IconButton,
  Table,
  TableHead,
  TableBody,
  TableRow,
  TableCell,
  Menu,
  MenuItem,
  useMediaQuery,
  Theme,
  SelectChangeEvent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress,
} from "@mui/material";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import CloseIcon from "@mui/icons-material/Close";
import FilterListIcon from "@mui/icons-material/FilterList";
import { ButtonOutline, ButtonPrimary, ButtonText } from "@/components/button";
import VisibilityIcon from "@mui/icons-material/Visibility";
import DeleteIcon from "@mui/icons-material/Delete";
import EditIcon from "@mui/icons-material/Edit";
import { Switch as CustomSwitch } from "@/components/switch";
import Input from "@/components/input/Input";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import {
  ApplicationStatusList,
  COMMITTEE_TASK_TYPE,
  formatDate,
  OrganisationPositionLabel,
  useQuery,
} from "@/helpers";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";
import { usejawatankuasaContext } from "./component/CawanganAJKKeahlian/jawatankuasa/jawatankuasaProvider";
import { useBranchContext } from "../BranchProvider";
import { DialogConfirmation } from "@/components";
import { Ajk } from "../pernyata-tahunan/interface";

export const ALiranTugas = ({
  disabled = false,
  module = COMMITTEE_TASK_TYPE.PENGURUSAN_AJK,
  branchId: brandIdProp,
}: {
  disabled?: boolean;
  module?: keyof typeof COMMITTEE_TASK_TYPE;
  branchId?: string | number;
}) => {
  const { t } = useTranslation();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [openMenuId, setOpenMenuId] = useState<number | null>(null);
  // @ts-ignore
  const branchDataRedux = useSelector((state) => state?.branchData?.data);
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, id: number) => {
    setAnchorEl(event.currentTarget);
    setOpenMenuId(id);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setOpenMenuId(null);
  };
  const open = Boolean(anchorEl);
  const id = open ? "simple-menu" : undefined;

  // const {
  //   ajkList,
  //   aliranTugas,
  //   isAliranModuleStatus,
  //   fetchAliranTugas,
  //   setModule,
  // } = usejawatankuasaContext();

  const {
    branchId,
    aliranTugasList,
    fetchAliranTugasListWithModule,
    isAliranModuleStatus,
    fetchAliranTugasStatus,
  } = useBranchContext();

  const [shouldFetch, setShouldFetch] = useState<boolean>(true);
  const [isAliranTugas, setIsAliranTugas] = useState<boolean>();

  useEffect(() => {
    fetchAliranTugasListWithModule(module);
    fetchAliranTugasStatus(module);
  }, [module]);

  useEffect(() => {
    setIsAliranTugas(isAliranModuleStatus);
  }, [isAliranModuleStatus]);

  // useEffect(() => {
  //   module ? fetchAliranTugasListWithModule(module) : null;
  //   setShouldFetch(false);
  // }, [shouldFetch]);
  const [isSaveCommitteeTaskSuccess, setIsSaveCommitteeTaskSuccess] =
    useState("");
  const [selectedTask, setSelectedTask] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isBlackListed, setIsBlackListed] = useState(false);
  const [selectedAJK, setSelectedAJK] = useState<any>(null);
  const [isDeactivateModalOpen, setIsDeactivateModalOpen] = useState(false);
  const [openModalActiveFlow, setOpenModalActiveFlow] = useState(false);

  const [eligibleMembers, setEligibleMembers] = useState([]);
  const [taskId, setTaskId] = useState<number>();
  const [committeeId, setCommitteeId] = useState<number>();

  const handleTaskChange = (event: SelectChangeEvent<string>) => {
    setSelectedTask(event.target.value as string);
    const selectedAJK =
      eligibleMembers?.find((item: any) => item.value == event.target.value) ??
      null;
    setSelectedAJK(selectedAJK);
  };
  const handleConfirmDeactivate = (taskId: number, committeeId: number) => {
    setCommitteeId(committeeId);
    setTaskId(taskId);
    setIsDeactivateModalOpen(true);
  };

  const handleDeactivate = () => {
    setIsDeactivateModalOpen(false);
    activeDeactivateTask(false);
  };

  const handleConfirmActivate = (taskId: number, committeeId: number) => {
    setCommitteeId(committeeId);
    setTaskId(taskId);
    setOpenModalActiveFlow(true);
  };

  const handleActivate = () => {
    setOpenModalActiveFlow(false);
    activeDeactivateTask(true);
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleSwitchAliranTugas = (flag: boolean) => {
    setIsAliranTugas(flag);
    switchTask(flag);
  };

  const activeDeActivePopupText = (type: string) => {
    switch (module) {
      case COMMITTEE_TASK_TYPE.PERLEMBAGAAN:
        return type === "active"
          ? t("confirmactivateTaskFlowPerlembagaan")
          : t("confirmDeactivateTaskFlowPerlembagaan");
      case COMMITTEE_TASK_TYPE.PENGURUSAN_AJK:
        return type === "active"
          ? t("confirmactivateTaskFlowAJK")
          : t("confirmDeactivateTaskFlowAJK");
      case COMMITTEE_TASK_TYPE.PENGURUSAN_MESYUARAT:
        return type === "active"
          ? t("confirmactivateTaskFlowMesyurat")
          : t("confirmDeactivateTaskFlowMesyuarat");
      case COMMITTEE_TASK_TYPE.PENYATAAN_TAHUNAN:
        return type === "active"
          ? t("confirmactivateTaskFlowPenyataTahunan")
          : t("confirmDeactivateTaskFlowPenyataTahunan");
      default:
        return;
    }
  };

  const { mutate: switchAliranTugas } = useCustomMutation();

  const switchTask = (flag: boolean) => {
    // Add logic to save the new task
    switchAliranTugas(
      {
        url: flag
          ? // ? `${API_URL}/society/${societyId}/committee_task/activate?module=${module}`
            // : `${API_URL}/society/${societyId}/committee_task/deactivate?module=${module}`,
            `${API_URL}/society/branch/${branchId}/committee-task/enable?&module=${module}`
          : `${API_URL}/society/branch/${branchId}/committee-task/disable?&module=${module}`,
        method: "put",
        values: {
          module: module,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: () => {
          refetchAliranTugas();
          fetchAliranTugasListWithModule(module);
        },
      }
    );
  };

  const handleCloseModal = () => {
    setIsSaveCommitteeTaskSuccess("");
    setIsModalOpen(false);
    // handleSaveNewTask();
    // setNewTaskName("");
  };
  const { id: societyId } = useParams();
  const { mutate: saveCommitteeTask, isLoading: isSaveCommitteeTask } =
    useCustomMutation();

  const handleSaveNewTask = () => {
    // Add logic to save the new task
    saveCommitteeTask(
      {
        url: `${API_URL}/society/committee-task/create?societyId=${societyId}&branchId=${branchId}`,
        method: "post",
        values: {
          // committeeId: selectedTask,
          branchCommitteeId: selectedTask,
          module: module,
          societyId: societyId,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          if (data?.data?.status === "SUCCESS") {
            setIsSaveCommitteeTaskSuccess(data?.data?.msg);
          }
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: () => {
          refetchAliranTugas();
          fetchAliranTugasListWithModule(module);
        },
      }
    );
  };

  const { mutate: deactivate, isLoading: isLoadingDeactive } =
    useCustomMutation();

  const activeDeactivateTask = (flag: boolean) => {
    // Add logic to save the new task
    deactivate(
      {
        url: flag
          ? `${API_URL}/society/committee-task/${taskId}/activate?societyId=${societyId}&branchId=${branchId}`
          : `${API_URL}/society/committee-task/${taskId}/deactivate?societyId=${societyId}&branchId=${branchId}`,
        method: "put",
        values: {
          // committeeId: committeeId,
          // module: module,
        },
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: (data) => {
          return {
            message: data?.data?.msg,
            type: "success",
          };
        },
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onError(error, variables, context) {
          console.log(error);
        },
        onSuccess: () => {
          refetchAliranTugas();
          fetchAliranTugasListWithModule(module);
        },
      }
    );
  };

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const { data: branchData, isLoading: isLoadingData } = useCustom<any>({
    url: `${API_URL}/society/branch/getById/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
    queryOptions: {
      enabled: branchId !== null,
      retry: false,
      cacheTime: 0,
      onSuccess: (data) => {
        setIsBlackListed(data?.data?.data?.subStatusCode === "003" || false);
      },
    },
  });

  const {
    data: aliranTugasListData,
    isLoading: aliranTugasListDataIsLoading,
    refetch: refetchAliranTugas,
  } = useQuery({
    url: "society/branch/committee/getCommitteeTaskEligibleMembers",
    filters: [
      { field: "branchId", operator: "eq", value: branchId },
      { field: "module", operator: "eq", value: module },
    ],
    queryOptions: {
      cacheTime: 0,
    },
    // autoFetch: false,
    onSuccess: (data) => {
      const responseData = data?.data?.data;
      const list = responseData?.map((ajk: any) => ({
        value: ajk.id,
        name: ajk.committeeName,
        label: `${ajk.committeeName} - ${t(
          OrganisationPositionLabel[ajk.designationCode]
        )}`,
      }));
      setEligibleMembers(list);
    },
  });

  const handleAcceptSubmit = () => {
    try {
      if (selectedTask) {
        handleSaveNewTask();
      }
    } finally {
      setSelectedAJK(null);
    }
  };

  const translateTextModal = t(
    `areYouSureFlowManagementTaskToAnotherCommittee_${module}`,
    {
      name: `<b>${selectedAJK?.name}</b>`,
    }
  );

  const isManager = useSelector(getUserPermission);
  const isAccessible = !isBlackListed && isManager;

  if (aliranTugasListDataIsLoading) {
    return (
      <Box
        sx={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "300px",
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  return isManager ? (
    <>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
        }}
      >
        <Box
          sx={{
            border: "1px solid rgba(0, 0, 0, 0.12)",
            borderRadius: "14px",
            p: 3,
            mb: 2,
          }}
        >
          <Box
            sx={{
              display: "flex",
              justifyContent: "space-between",
              alignItems: "center",
              mb: 3,
            }}
          >
            <Typography
              variant="subtitle1"
              sx={{
                color: "var(--primary-color)",
                borderRadius: "16px",
                fontSize: "14px",
              }}
            >
              {t("taskFlow")}
            </Typography>

            <CustomSwitch
              disabled={disabled}
              checked={isAliranTugas}
              onChange={(e) => handleSwitchAliranTugas(e.target.checked)}
            />
          </Box>

          {isAliranTugas && (
            <>
              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    gap: 2,
                    width: "100%",
                  }}
                >
                  <Input
                    label={t("selectTaskFlowTo")}
                    type="select"
                    onChange={(e) => handleTaskChange(e)}
                    value={selectedTask}
                    options={eligibleMembers}
                  />
                </Box>
                <ButtonPrimary
                  onClick={handleOpenModal}
                  disabled={!isAccessible}
                  sx={{
                    color: "white",
                    textTransform: "none",
                    fontWeight: "bold",
                    fontSize: "16px",
                    alignSelf: "flex-end",
                  }}
                >
                  {t("update")}
                </ButtonPrimary>
              </Box>
              <Table
                sx={{
                  backgroundColor: "white",
                  borderRadius: "4px",
                  overflow: "hidden",
                  "& .MuiTableCell-root": { fontSize: "16px" },
                }}
              >
                <TableHead>
                  <TableRow sx={{ backgroundColor: "white" }}>
                    <TableCell sx={{ fontWeight: "500" }}>
                      {t("name")}
                    </TableCell>
                    <TableCell sx={{ fontWeight: "500", textAlign: "center" }}>
                      {t("position")}
                    </TableCell>
                    <TableCell sx={{ fontWeight: "500", textAlign: "center" }}>
                      {t("status")}
                    </TableCell>
                    <TableCell sx={{ fontWeight: "500", textAlign: "center" }}>
                      {t("AKTIF")}
                    </TableCell>
                    <TableCell sx={{ fontWeight: "500", textAlign: "center" }}>
                      {t("deactivationDate")}
                    </TableCell>
                    <TableCell></TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {aliranTugasList
                    ? aliranTugasList
                        .filter((e) => {
                          return e.status === "001";
                        })
                        .map((task, index) => (
                          <TableRow key={index}>
                            <TableCell>{task.name}</TableCell>
                            <TableCell sx={{ textAlign: "center" }}>
                              {t(
                                OrganisationPositionLabel[task.designationCode]
                              )}
                            </TableCell>
                            <TableCell sx={{ textAlign: "center" }}>
                              <Box
                                sx={{
                                  border:
                                    task?.status ===
                                    ApplicationStatusList.find(
                                      (item) => item.value === "AKTIF"
                                    )?.id
                                      ? "0.5px solid #FF0000"
                                      : "0.5px solid #00B69B",
                                  backgroundColor:
                                    task?.status ===
                                    ApplicationStatusList.find(
                                      (item) => item.value === "AKTIF"
                                    )?.id
                                      ? "#FF000080"
                                      : "#00B69B66",
                                  textAlign: "center",
                                  borderRadius: "30px",
                                  p: "5px 3px",
                                  fontSize: "12px",
                                  color: "white",
                                }}
                              >
                                {t("active")}
                              </Box>
                            </TableCell>
                            <TableCell sx={{ textAlign: "center" }}>
                              {task.taskActivateDate
                                ? formatDate(task.taskActivateDate)
                                : "-"}
                            </TableCell>
                            <TableCell sx={{ textAlign: "center" }}>
                              {task.taskDeactivateDate
                                ? formatDate(task.taskDeactivateDate)
                                : "-"}
                            </TableCell>
                            <TableCell sx={{ textAlign: "center" }}>
                              <IconButton
                                onClick={(event) =>
                                  handleMenuOpen(event, task.id)
                                }
                              >
                                <MoreVertIcon sx={{ color: "black" }} />
                              </IconButton>
                              <Menu
                                anchorEl={anchorEl}
                                open={openMenuId === task.id}
                                onClose={handleMenuClose}
                                slotProps={{
                                  paper: {
                                    elevation: 0,
                                    sx: {
                                      backgroundColor: "white",
                                      color: "black",
                                      boxShadow:
                                        "0 4px 4px rgba(0, 0, 0, 0.25)",
                                      "& .MuiMenuItem-root": {
                                        color: "black",
                                        "& .MuiSvgIcon-root": {
                                          color: "black",
                                        },
                                      },
                                      "& .MuiDivider-root": {
                                        my: 0.5,
                                      },
                                    },
                                  },
                                }}
                                transformOrigin={{
                                  horizontal: "right",
                                  vertical: "top",
                                }}
                                anchorOrigin={{
                                  horizontal: "right",
                                  vertical: "bottom",
                                }}
                              >
                                <MenuItem
                                  onClick={() =>
                                    handleConfirmDeactivate(
                                      task.id,
                                      task.societyCommitteeId
                                    )
                                  }
                                >
                                  {t("deactivate")}
                                </MenuItem>
                              </Menu>
                            </TableCell>
                          </TableRow>
                        ))
                    : null}
                </TableBody>
              </Table>
            </>
          )}
        </Box>

        {isAliranTugas && (
          <Box
            sx={{
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 3,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("taskFlowList")}
            </Typography>

            <Table
              sx={{
                backgroundColor: "white",
                borderRadius: "4px",
                overflow: "hidden",
                "& .MuiTableCell-root": { fontSize: "16px" },
              }}
            >
              <TableHead>
                <TableRow sx={{ backgroundColor: "white" }}>
                  <TableCell sx={{ fontWeight: "500" }}>{t("name")}</TableCell>
                  <TableCell sx={{ fontWeight: "500", textAlign: "center" }}>
                    {t("position")}
                  </TableCell>
                  <TableCell sx={{ fontWeight: "500", textAlign: "center" }}>
                    {t("status")}
                  </TableCell>
                  <TableCell sx={{ fontWeight: "500", textAlign: "center" }}>
                    {t("AKTIF")}
                  </TableCell>
                  <TableCell sx={{ fontWeight: "500", textAlign: "center" }}>
                    {t("deactivationDate")}
                  </TableCell>
                  <TableCell></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {aliranTugasList
                  ? aliranTugasList
                      .filter((e) => {
                        return e.status == "008";
                      })
                      .map((task, index) => (
                        <TableRow key={index}>
                          <TableCell>{task.name}</TableCell>
                          <TableCell sx={{ textAlign: "center" }}>
                            {t(OrganisationPositionLabel[task.designationCode])}
                          </TableCell>
                          <TableCell sx={{ textAlign: "center" }}>
                            <Box
                              sx={{
                                border: ApplicationStatusList.find(
                                  (item) => item.value === "TIDAK_AKTIF"
                                )?.id
                                  ? "0.5px solid #FF0000"
                                  : "0.5px solid #00B69B",
                                backgroundColor: ApplicationStatusList.find(
                                  (item) => item.value === "TIDAK_AKTIF"
                                )?.id
                                  ? "#FF000080"
                                  : "#00B69B66",
                                textAlign: "center",
                                borderRadius: "30px",
                                p: "5px 3px",
                                fontSize: "12px",
                                color: "white",
                              }}
                            >
                              {t("inactive")}
                            </Box>
                          </TableCell>
                          <TableCell sx={{ textAlign: "center" }}>
                            {task.taskActivateDate
                              ? formatDate(task.taskActivateDate)
                              : "-"}
                          </TableCell>
                          <TableCell sx={{ textAlign: "center" }}>
                            {task.taskDeactivateDate
                              ? formatDate(task.taskDeactivateDate)
                              : "-"}
                          </TableCell>
                          <TableCell sx={{ textAlign: "center" }}>
                            <IconButton
                              onClick={(event) =>
                                handleMenuOpen(event, task.id)
                              }
                            >
                              <MoreVertIcon sx={{ color: "black" }} />
                            </IconButton>
                            <Menu
                              anchorEl={anchorEl}
                              open={openMenuId === task.id}
                              onClose={handleMenuClose}
                              slotProps={{
                                paper: {
                                  elevation: 0,
                                  sx: {
                                    backgroundColor: "white",
                                    color: "black",
                                    boxShadow: "0 4px 4px rgba(0, 0, 0, 0.25)",
                                    "& .MuiMenuItem-root": {
                                      color: "black",
                                      "& .MuiSvgIcon-root": {
                                        color: "black",
                                      },
                                    },
                                    "& .MuiDivider-root": {
                                      my: 0.5,
                                    },
                                  },
                                },
                              }}
                              transformOrigin={{
                                horizontal: "right",
                                vertical: "top",
                              }}
                              anchorOrigin={{
                                horizontal: "right",
                                vertical: "bottom",
                              }}
                            >
                              <MenuItem
                                onClick={() =>
                                  handleConfirmActivate(
                                    task.id,
                                    task.societyCommitteeId
                                  )
                                }
                              >
                                {t("AKTIF")}
                              </MenuItem>
                            </Menu>
                          </TableCell>
                        </TableRow>
                      ))
                  : null}
              </TableBody>
            </Table>
          </Box>
        )}
      </Box>

      {/* <Dialog
        open={isModalOpen}
        onClose={handleCloseModal}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#000",
            maxWidth: "500px",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogContent sx={{ py: 2, px: 2.5, textAlign: "center" }}>
          <Typography variant="body1" sx={{ fontSize: 14, pl: 2.5 }}>
            {t("alirantugasPopText")}
          </Typography>
        </DialogContent>
        <DialogActions
          sx={{
            py: 2,
            px: 3,
            justifyContent: "center",
            flexDirection: "column",
            gap: 1,
          }}
        >
          <ButtonPrimary onClick={handleCloseModal}>{t("yes")}</ButtonPrimary>
          <ButtonText
            onClick={handleCloseModal}
            sx={{
              marginLeft: "0px !important",
            }}
          >
            {t("back")}
          </ButtonText>
        </DialogActions>
      </Dialog> */}

      <DialogConfirmation
        open={isModalOpen}
        isSuccess={isSaveCommitteeTaskSuccess !== ""}
        onSuccessText={isSaveCommitteeTaskSuccess}
        isMutating={isSaveCommitteeTask}
        onClose={handleCloseModal}
        onConfirmationText={translateTextModal}
        onAction={handleAcceptSubmit}
      />

      <DialogConfirmation
        open={isDeactivateModalOpen}
        // isSuccess={isSaveCommitteeTaskSuccess !== ""}
        // onSuccessText={isSaveCommitteeTaskSuccess}
        isMutating={isLoadingDeactive}
        onClose={() => setIsDeactivateModalOpen(false)}
        onConfirmationText={activeDeActivePopupText("deactive")}
        onAction={handleDeactivate}
      />

      <DialogConfirmation
        open={openModalActiveFlow}
        // isSuccess={isSaveCommitteeTaskSuccess !== ""}
        // onSuccessText={isSaveCommitteeTaskSuccess}
        isMutating={isLoadingDeactive}
        onClose={() => setOpenModalActiveFlow(false)}
        onConfirmationText={activeDeActivePopupText("active")}
        onAction={handleActivate}
      />
    </>
  ) : (
    <></>
  );
};

export default ALiranTugas;
