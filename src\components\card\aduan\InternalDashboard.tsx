import { Box, styled, Typography } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate } from "react-router-dom"

import { ButtonPrimary } from "@/components/button";

import SampleIcon from "@/assets/svg/icon-create-complaint.svg?react"

export const CardAduanInternalDashboard = () => {
  const { pathname } = useLocation();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const isAduan = ["aduan", "aduan-saya"].some((path) => pathname.endsWith(path));
  const StyledCreateComplaintIcon = styled(SampleIcon)(() => ({
    "path": {
      stroke: "white",
      // fill: "transparent"
    }
  }))

  const handleButtonCreateComplaintClicked = () => {
    navigate("./aduan/create", { relative: "path" })
  }

  if (!isAduan) {
    return null;
  }
  return (
    <div style={{
      display: "flex",
      flexDirection: "column",
      rowGap: "0.5rem",
      position: "sticky",
      top: "0.5rem"
    }}>
      <Box
        sx={{
          backgroundColor: "#fff",
          borderRadius: "1rem",
          boxShadow: "0 12 12 0 rgba(234, 232, 232, 0.4)",
          padding: "1rem"
        }}
      >
        <Typography className="title" mb="1rem">
          {t("createAComplaint")}
        </Typography>
        <ButtonPrimary
          fullWidth
          sx={{
            fontWeight: "medium",
            justifyContent: "flex-start",
            minHeight: "3.5rem",
            position: "relative"
          }}
          onClick={handleButtonCreateComplaintClicked}
        >
          {t("createAComplaint")}
          <div
            style={{
              position: "absolute",
              right: 0,
              bottom: "-0.25rem",
              zIndex: 0
            }}
          >
            <StyledCreateComplaintIcon />
          </div>
        </ButtonPrimary>
      </Box>

      <Box
        sx={{
          backgroundColor: "#fff",
          borderRadius: "1rem",
          boxShadow: "0 12 12 0 rgba(234, 232, 232, 0.4)",
          padding: "1rem",
          display: "flex",
          flexDirection: "column",
          rowGap: "0.5rem"
        }}
      >
        <Typography className="title">
          {t("organizationComplain")}
        </Typography>
        <Typography
          sx={{
            fontSize: "2.5rem",
            fontWeight: "medium",
            color: "#666"
          }}
        >
          {`${10}%`}
        </Typography>
        <Typography
          sx={{
            fontSize: "0.625rem",
            color: "#666",
            width: "80%"
          }}
        >
          Peningkatan berbanding minggu lalu
        </Typography>
      </Box>

      <Box
        sx={{
          backgroundColor: "#fff",
          borderRadius: "1rem",
          boxShadow: "0 12 12 0 rgba(234, 232, 232, 0.4)",
          padding: "1rem",
          display: "flex",
          flexDirection: "column",
          rowGap: "0.5rem"
        }}
      >
        <Typography className="title">
          {t("parentComplaint")}
        </Typography>
        <Typography
          sx={{
            fontSize: "2.5rem",
            fontWeight: "medium",
            color: "#666"
          }}
        >
          {`${10}%`}
        </Typography>
        <Typography
          sx={{
            fontSize: "0.625rem",
            color: "#666",
            width: "80%"
          }}
        >
          Peningkatan berbanding minggu lalu
        </Typography>
      </Box>

      <Box
        sx={{
          backgroundColor: "#fff",
          borderRadius: "1rem",
          boxShadow: "0 12 12 0 rgba(234, 232, 232, 0.4)",
          padding: "1rem",
          display: "flex",
          flexDirection: "column",
          rowGap: "0.5rem"
        }}
      >
        <Typography className="title">
          {t("branchComplaint")}
        </Typography>
        <Typography
          sx={{
            fontSize: "2.5rem",
            fontWeight: "medium",
            color: "#666"
          }}
        >
          {`${10}%`}
        </Typography>
        <Typography
          sx={{
            fontSize: "0.625rem",
            color: "#666",
            width: "80%"
          }}
        >
          Peningkatan berbanding minggu lalu
        </Typography>
      </Box>
    </div>
  )
}
