import { useState, useCallback } from "react";
import paymentService, {
  IPaymentCalculationRequest,
  IProcessPaymentRequest,
  IPaymentItemsResponse,
  IPaymentCalculationResponse,
  IProcessPaymentResponse,
  IPaymentRecordResponse,
} from "@/services/paymentService";
import { IApiResponse } from "@/types/api";

interface UsePaymentServiceReturn {
  // State
  loading: boolean;
  error: string | null;
  data: any;

  // Payment Items
  getPaymentItems: (category?: string, docCode?: string) => Promise<IApiResponse<IPaymentItemsResponse>>;

  // Payment Calculation
  calculatePayment: (request: IPaymentCalculationRequest) => Promise<IApiResponse<IPaymentCalculationResponse>>;

  // Payment Processing
  processPayment: (request: IProcessPaymentRequest) => Promise<IApiResponse<IProcessPaymentResponse>>;

  // Payment Record
  getPaymentRecord: (transactionId: string) => Promise<IApiResponse<IPaymentRecordResponse>>;

  // Utility
  resetState: () => void;
}

/**
 * Custom hook for payment service operations
 * Provides a React-friendly interface to the payment service
 */
export const usePaymentService = (): UsePaymentServiceReturn => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);

  // Helper function to handle async operations
  const handleAsyncOperation = useCallback(async <T>(
    operation: () => Promise<IApiResponse<T>>
  ): Promise<IApiResponse<T>> => {
    setLoading(true);
    setError(null);

    try {
      const result = await operation();
      setData(result.data);
      return result;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "An error occurred";
      setError(errorMessage);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get payment items
  const getPaymentItems = useCallback(
    (category?: string, docCode?: string) =>
      handleAsyncOperation(() => paymentService.getPaymentItems(category, docCode)),
    [handleAsyncOperation]
  );

  // Calculate payment
  const calculatePayment = useCallback(
    (request: IPaymentCalculationRequest) =>
      handleAsyncOperation(() => paymentService.calculatePayment(request)),
    [handleAsyncOperation]
  );

  // Process payment
  const processPayment = useCallback(
    (request: IProcessPaymentRequest) =>
      handleAsyncOperation(() => paymentService.processPayment(request)),
    [handleAsyncOperation]
  );

  // Get payment record
  const getPaymentRecord = useCallback(
    (transactionId: string) =>
      handleAsyncOperation(() => paymentService.getPaymentRecord(transactionId)),
    [handleAsyncOperation]
  );

  // Reset state
  const resetState = useCallback(() => {
    setLoading(false);
    setError(null);
    setData(null);
    paymentService.resetState();
  }, []);

  return {
    // State
    loading,
    error,
    data,

    // Methods
    getPaymentItems,
    calculatePayment,
    processPayment,
    getPaymentRecord,
    resetState,
  };
};

export default usePaymentService;
