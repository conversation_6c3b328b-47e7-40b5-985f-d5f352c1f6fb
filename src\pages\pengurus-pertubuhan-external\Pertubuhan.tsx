import React, { useState } from "react";
import { useCustomMutation } from "@refinedev/core";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  HideOrDisplayInherit,
  NewSocietyBranchStatus,
  ApplicationStatusList,
  OrganisationPositions,
} from "../../helpers/enums";
import { API_URL } from "../../api";
import {
  Box,
  Card,
  Dialog,
  DialogContent,
  DialogTitle,
  IconButton,
  InputAdornment,
  Stack,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TextField,
  Tooltip,
  Typography,
} from "@mui/material";
import ConfirmationDialog from "../../components/dialog/confirm";
import { ButtonPrimary } from "../../components/button";
import { ComingSoon } from "../login/comingSoon";

import CloseIcon from "@mui/icons-material/Close";
import InfoIcon from "@mui/icons-material/Info";
import SearchIcon from "@mui/icons-material/Search";
import { tempSocietyData } from "../dashboard/external-user";
import { EditIcon, EyeIcon, TrashIcon } from "@/components/icons";
import { FieldValues, useForm } from "react-hook-form";
import { capitalizeWords, useMutation, useQuery } from "@/helpers";
import { DataTable } from "@/components";
import FilterBar from "@/components/filter";

export const PengurusPertubuhanExternalLayout: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const active = [NewSocietyBranchStatus.AKTIF_1];

  const [openSertaModal, setOpenSertaModal] = useState(false);
  const [showNoResults, setShowNoResults] = useState(true);
  const [showComingSoon, setShowComingSoon] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const [selectedSocietyId, setSelectedSocietyId] = useState<string | null>(
    null
  );
  const [openConfirmDialog, setOpenConfirmDialog] = useState(false);
  const [searchSertaPertubuhan, setSearchSertaPertubuhan] = useState<any>("");
  const [societyList, setSocietyList] = useState<tempSocietyData[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [applicationStatusCodeQuery, setApplicationStatusCodeQuery] = useState<
    number | string
  >("");
  const [statusCodeQuery, setStatusCodeQuery] = useState<number | string>("");
  const [registeredYearQuery, setRegisteredYearQuery] = useState<
    number | string
  >("");
  const [designationCodeQuery, setDesignationCodeQuery] = useState<
    number | string
  >("");

  const handleCloseSertaModal = () => setOpenSertaModal(false);
  const handleDeleteClick = (societyId: string) => {
    setSelectedSocietyId(societyId);
    setOpenConfirmDialog(true);
  };

  const { fetch: deleteSociety, isLoading: isDeletingSociety } = useMutation({
    url: `society/${selectedSocietyId}`,
    method: "delete",
    onSuccess: (data) => {
      setRefreshKey((prev) => prev + 1);
      setOpenConfirmDialog(false);
      refetchSocietyData();
    },
  });

  const handleConfirmDelete = () => {
    if (selectedSocietyId) {
      deleteSociety();
    }
  };

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 5,
      searchQuery: undefined,
    },
  });

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    permohonan: "permohonan",
    pertubuhan: "pertubuhan",
    tarikhMohon: "tarikhMohon",
    jawatan: "jawatan",
  });

  const page = watch("page");
  const pageSize = watch("pageSize");
  const {
    data: societyListDataResponse,
    isLoading: isLoadingSocietyListData,
    refetch: refetchSocietyData,
  } = useQuery({
    url: "society/getUserSociety",
    filters: [
      {
        field: "pageSize",
        value: pageSize,
        operator: "eq",
      },
      {
        field: "pageNo",
        value: page,
        operator: "eq",
      },
      {
        field: "searchQuery",
        value: searchQuery,
        operator: "eq",
      },
      {
        field: "applicationStatusCode",
        value: applicationStatusCodeQuery,
        operator: "eq",
      },
      {
        field: "statusCode",
        value: statusCodeQuery,
        operator: "eq",
      },
      {
        field: "registeredYear",
        value: registeredYearQuery,
        operator: "eq",
      },
      {
        field: "designationCode",
        value: designationCodeQuery,
        operator: "eq",
      },
    ],
  });

  const mohonOptions = [
    { value: 2, label: "Menunggu Keputusan" },
    { value: 1, label: "Belum dihantar" },
    { value: 3, label: "Lulus" },
    { value: 4, label: "Tolak" },
    { value: 5, label: "MENUNGGU_BAYARAN_KAUNTER" },
    { value: 6, label: "MENUNGGU_BAYARAN_ONLINE" },
    { value: 11, label: "AKTIF" },
    { value: 36, label: "KUIRI" },
    { value: 41, label: "MENUNGGU_ULASAN_AGENSI_LUAR" },
  ];

  const pertubuhanOption = [
    { label: "AKTIF", value: "001" },
    { label: "TIDAK_AKTIF", value: "008" },
  ];

  const filterOptions = {
    permohonan: mohonOptions,
    pertubuhan: pertubuhanOption,
    tarikhMohon: Array.from({ length: 50 }, (_, i) => ({
      label: `${new Date().getFullYear() - i}`,
      value: `${new Date().getFullYear() - i}`,
    })),
    jawatan: OrganisationPositions,
  };

  const onFilterChange = (filter: string, value: number | string) => {
    setValue("page", 1);
    switch (filter) {
      case "permohonan":
        setApplicationStatusCodeQuery(value);
        break;
      case "pertubuhan":
        setStatusCodeQuery(value);
        break;
      case "tarikhMohon":
        setRegisteredYearQuery(value);
        break;
      case "jawatan":
        setDesignationCodeQuery(value);
        break;
    }
  };

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const totalList = societyListDataResponse?.data?.data?.total ?? 0;
  const rowData = societyListDataResponse?.data?.data?.data ?? [];

  const columns = [
    {
      field: "societyName",
      headerName: t("namaPertubuhan"),
      flex: 1,
      align: "left",
      headerAlign: "left",
      renderCell: (params: any) => {
        const row = params?.row;
        const isActive = active.includes(params?.row?.statusCode);
        return (
          <Box
            onClick={() => {
              isActive &&
                navigate(`/pertubuhan/society/${row.id}/senarai/maklumat`);
            }}
            style={{
              textDecoration: "none",
              cursor: isActive ? "pointer" : "not-allowed",
              pointerEvents: isActive ? "auto" : "none",
            }}
          >
            {params?.row?.societyName}
          </Box>
        );
      },
    },
    {
      field: "societyNo",
      headerName: t("OrganizationNo"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const societyNo = params?.row?.societyNo;
        const applicationNo = params?.row?.applicationNo;

        return societyNo ?? applicationNo ?? t("-");
      },
      cellClassName: "custom-cell",
    },
    {
      field: "applicationStatusCode",
      headerName: t("applicationStatus"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const status = ApplicationStatusList.find(
          (item) => item.id === params?.row?.applicationStatusCode
        );
        return status
          ? capitalizeWords(t(status.value), (word, sentenceIndex) =>
              sentenceIndex > 0 ? word.toLowerCase() : null
            )
          : t("-");
      },
      cellClassName: "custom-cell",
    },
    {
      field: "statusCode",
      headerName: t("organizationStatus"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const isActive = active.includes(params?.row?.statusCode);
        return (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
            }}
          >
            <Typography
              className="status-pertubuhan-text"
              sx={{
                backgroundColor: "#fff",
                border: `2px solid ${
                  isActive ? "var(--success)" : "var(--error)"
                }`,
              }}
            >
              {isActive ? t("active") : t("inactive")}
            </Typography>
          </Box>
        );
      },
    },
    {
      field: "createdDate",
      headerName: t("dateApplied"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        return params?.row?.createdDate;
      },
      cellClassName: "custom-cell",
    },
    {
      field: "designationCode",
      headerName: t("position"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params?.row;
        return t(
          `${
            OrganisationPositions.find(
              (item) => item?.value === Number(row?.designationCode)
            )?.label || "-"
          }`
        );
      },
      cellClassName: "custom-cell",
    },
    {
      field: "actions",
      headerName: t("action"),
      flex: 1,
      align: "right",
      headerAlign: "right",
      renderCell: (params: any) => {
        const row = params?.row;
        const isActive = active.includes(row?.statusCode);

        if (!isActive) {
          switch (row?.applicationStatusCode) {
            case 2:
            case 40:
            case 20:
            case 4:
            case 3:
              return (
                <IconButton
                  // size="small"
                  sx={{
                    width: "3rem",
                    height: "3rem",
                  }}
                  onClick={() =>
                    navigate(`/pertubuhan/society/${row?.id}/senarai/maklumat`)
                  }
                >
                  <EyeIcon
                    color="#147C7C"
                    style={{ width: "1rem", height: "1rem" }}
                  />
                </IconButton>
              );
            case 36:
            case 1:
              if (row?.isManageAuthorized) {
                return (
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "right",
                      justifyContent: "flex-end",
                    }}
                  >
                    <IconButton
                      sx={{
                        width: "3rem",
                        height: "3rem",
                        color: "var(--primary-color)",
                      }}
                      onClick={() =>
                        navigate(
                          `/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am?disabled=true&id=${btoa(
                            row.id
                          )}`
                        )
                      }
                    >
                      <EditIcon
                        style={{
                          color: "#147C7C !important",
                          width: "1rem",
                          height: "1rem",
                        }}
                      />
                    </IconButton>
                    <IconButton
                      sx={{
                        width: "3rem",
                        height: "3rem",
                        display: HideOrDisplayInherit,
                      }}
                      onClick={() => handleDeleteClick(row.id)}
                    >
                      <TrashIcon
                        style={{
                          width: "1rem",
                          height: "1rem",
                          color: "red",
                          display: HideOrDisplayInherit,
                        }}
                      />
                    </IconButton>
                  </Box>
                );
              }
              break;
            case 5:
            case 6:
              if (row?.isManageAuthorized) {
                return (
                  <IconButton
                    sx={{
                      width: "3rem",
                      height: "3rem",
                      color: "var(--primary-color)",
                    }}
                    onClick={() =>
                      navigate(
                        `/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am?disabled=true&id=${btoa(
                          row.id
                        )}`
                      )
                    }
                  >
                    <EditIcon
                      style={{
                        color: "#147C7C !important",
                        width: "1rem",
                        height: "1rem",
                      }}
                    />
                  </IconButton>
                );
              }
              break;
            default:
              return null;
          }
        } else {
          if (row?.applicationStatusCode === 3) {
            return (
              <IconButton
                sx={{
                  width: "3rem",
                  height: "3rem",
                }}
                onClick={() =>
                  navigate(`/pertubuhan/society/${row?.id}/senarai/maklumat`)
                }
              >
                <EyeIcon
                  sx={{
                    color: "#147C7C !important",
                    width: "1rem",
                    height: "1rem",
                  }}
                />
              </IconButton>
            );
          }
        }
      },
    },
  ];

  const handleDialogSearchChange = async () => {
    //setShowNoResults(e.target.value.length > 0);
    try {
      //setIsLoadingData(true);
      const params: any = {
        searchQuery: searchSertaPertubuhan,
      };
      // @ts-ignore
      const query = Object.keys(params)
        .map((k) => `${k}=${params[k]}`)
        .join("&");
      const response = await fetch(
        `${API_URL}/society/searchJoinSociety?${query}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      const data = await response.json();

      if (data.status.toUpperCase() === "SUCCESS") {
        setSocietyList(data?.data);
        setShowNoResults(false);
      } else if (data.code == 404) {
        //setSocietyList({societyName: "", societyNo: "", id:0});
        setSocietyList([]);
        setShowNoResults(true);
      }
    } catch (error) {
      console.error("Error fetching society data:", error);
      return false;
    }

    if (societyList) {
      //const exist = await checkUserExist() ?? false;
      setShowNoResults(false);
      //setUserExisted(exist);
    } else {
      setShowNoResults(true);
    }
    /*await setTimeout(async ()=> {

    },250)*/
  };

  const { mutate: joinSociety } = useCustomMutation();

  const handleSertaClick = (i: number, val: boolean) => {
    const join = val ? "Join" : "Cancel";
    joinSociety(
      {
        method: "post",
        url: `${API_URL}/society/${i}/join?joinOrCancel=${join}`,
        values: {},
        config: {
          headers: {
            portal: localStorage.getItem("portal"),
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        },
        successNotification: {
          message: t("success"),
          type: "success",
        },
        errorNotification: {
          message: t("error"),
          type: "error",
        },
      },
      {
        onSuccess: () => {
          handleDialogSearchChange();
          //setRefreshKey((prev) => prev + 1);
          //setOpenConfirmDialog(false);
        },
      }
    );
  };

  const renderButtons = (e: tempSocietyData) => {
    if (
      e.committeeStatus === null ||
      e.committeeStatus === "-1" ||
      e.committeeStatus === "43"
    ) {
      return (
        <ButtonPrimary onClick={() => handleSertaClick(e.id, true)}>
          {t("join")}
        </ButtonPrimary>
      );
    } else if (e.committeeStatus === "2") {
      return (
        <ButtonPrimary
          onClick={() => handleSertaClick(e.id, false)}
          sx={{
            backgroundColor: "#FF0000",
            "&:hover": { backgroundColor: "#FF0000" },
          }}
        >
          {t("cancel")}
        </ButtonPrimary>
      );
    }
    return <></>;
  };

  // @ts-ignore
  const handleKeyDown = (event) => {
    if (event.key === "Enter") {
      //console.log('do validate')
      handleDialogSearchChange();
    }
  };

  const onSearchKeyDown = (event: React.KeyboardEvent) => {
    if (event.key === "Enter") {
      const value = (event.target as HTMLInputElement).value;
      setValue("page", 1);
      setSearchQuery(value);
    }
  };

  const goRegister = () => {
    navigate("/pertubuhan/pengurusan-pertubuhan/pendaftaran/maklumat-am");
  };

  return (
    <>
      {showComingSoon ? (
        <ComingSoon onBack={() => setShowComingSoon(false)} />
      ) : (
        <Box
          sx={{
            width: "100%",
            display: "flex",
            flexDirection: "column",
            gap: "20px",
            // minHeight: "calc(100vh - 60px - 83px)",
          }}
        >
          <Box
            sx={{
              width: "inherit",
              height: "100%",
              flex: 3,
              display: "flex",
              flexDirection: "column",
            }}
          >
            <Card
              sx={{
                p: 3,
                borderRadius: "15px",
                boxShadow: "none",
                height: "100%",
                width: "100%",
              }}
            >
              <Typography
                variant="h6"
                sx={{
                  fontFamily: "Poppins",
                  fontSize: "20px",
                  fontWeight: 500,
                  color: "#55556D",
                  marginBottom: 2,
                }}
              >
                {t("pertubuhan")}
              </Typography>
              <TextField
                fullWidth
                variant="outlined"
                placeholder={t("namaPertubuhan")}
                sx={{
                  display: "block",
                  boxSizing: "border-box",
                  maxWidth: 570,
                  marginInline: "auto",
                  height: "40px",
                  background: "var(--border-grey)",
                  opacity: 0.5,
                  border: "1px solid var(--text-grey)",
                  borderRadius: "10px",
                  "& .MuiOutlinedInput-root": {
                    height: "40px",
                    "& fieldset": {
                      border: "none",
                    },
                  },
                }}
                onKeyDown={onSearchKeyDown}
                onChange={(e) => setSearchSertaPertubuhan(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon
                        sx={{
                          color: "var(--text-grey-disabled)",
                          marginLeft: "8px",
                        }}
                      />
                    </InputAdornment>
                  ),
                }}
              />
              <FilterBar
                filterOptions={filterOptions}
                onFilterChange={onFilterChange}
                selectedFilters={selectedFilters}
                onSelectedFiltersChange={handleSelectedFiltersChange}
              />

              <DataTable
                columns={columns as any}
                rows={rowData}
                page={page}
                rowsPerPage={pageSize}
                totalCount={totalList}
                onPageChange={(newPage) => setValue("page", newPage)}
                onPageSizeChange={(newPageSize) => {
                  setValue("page", 1);
                  setValue("pageSize", newPageSize);
                }}
                isLoading={isLoadingSocietyListData}
              />
            </Card>
          </Box>

          <Box sx={{ borderRadius: 2.5, backgroundColor: "#fff", p: 2, mt: 1 }}>
            <Box
              sx={{
                p: 2,
                pl: { xs: 2, md: 6 },
                border: "1px solid #D9D9D9",
                borderRadius: "14px",
              }}
            >
              <Typography
                sx={{
                  color: "var(--primary-color)",
                  pt: 3,
                  fontWeight: "500",
                  fontSize: 18,
                }}
              >
                {t("RegisteraNewOrganization")}
              </Typography>
              <Stack
                sx={{ py: 4 }}
                direction="row"
                alignItems="center"
                justifyContent="space-between"
              >
                <Typography>{t("RegisteraNewOrganization")}</Typography>
                <ButtonPrimary
                  sx={{ px: 4, py: 1, mr: 4 }}
                  onClick={goRegister}
                >
                  {t("registerButton")}
                </ButtonPrimary>
              </Stack>
            </Box>
          </Box>
        </Box>
      )}

      <Dialog
        open={openSertaModal}
        onClose={handleCloseSertaModal}
        PaperProps={{
          style: {
            borderRadius: "8px",
            backgroundColor: "#fff",
            color: "#000",
            width: "80%",
            maxWidth: "1000px",
            height: "auto",
            maxHeight: "80vh",
          },
        }}
        slotProps={{
          backdrop: {
            style: {
              backgroundColor: "rgba(0, 0, 0, 0.5)",
              backdropFilter: "blur(4px)",
            },
          },
        }}
      >
        <DialogTitle sx={{ pb: 2.5 }}>
          <Box
            sx={{
              px: 2.5,
              py: 0.5,
              borderRadius: 2.5,
            }}
            display="flex"
            justifyContent="space-between"
            alignItems="center"
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{
                color: "var(--primary-color)",
                borderRadius: "16px",
                fontSize: "16px",
              }}
            >
              {t("sertaPertubuhan")}
            </Typography>
            <IconButton onClick={handleCloseSertaModal} size="small">
              <CloseIcon sx={{ color: "black" }} />
            </IconButton>
          </Box>
        </DialogTitle>
        <DialogContent sx={{ py: 2, px: 5, overflowY: "auto" }}>
          <Box
            sx={{
              display: "flex",
              alignItems: "center",
              mb: 2,
              pl: 5,
              pr: 5,
            }}
          >
            <Tooltip title={t("enterOrgNameOrNumber")} arrow>
              <InfoIcon
                sx={{
                  fontSize: 18,
                  color: "var(--primary-color)",
                  cursor: "pointer",
                  "&:hover": {
                    color: "var(--primary-color)",
                  },
                }}
              />
            </Tooltip>
            <TextField
              fullWidth
              placeholder={t("searchOrganization")}
              sx={{
                display: "block",
                boxSizing: "border-box",
                width: "90%",
                height: "40px",
                marginInline: "auto",
                marginTop: "12px",
                background: "rgba(132, 132, 132, 0.3)",
                opacity: 0.5,
                border: "1px solid rgba(102, 102, 102, 0.8)",
                borderRadius: "10px",
                "& .MuiOutlinedInput-root": {
                  height: "40px",
                  "& fieldset": {
                    border: "none",
                  },
                },
              }}
              onKeyDown={handleKeyDown}
              onChange={(e) => setSearchSertaPertubuhan(e.target.value)}
              InputProps={{
                endAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon
                      onClick={handleDialogSearchChange}
                      sx={{
                        color: "#9CA3AF",
                        marginLeft: "8px",
                        "&:hover": { cursor: "pointer" },
                      }}
                    />
                  </InputAdornment>
                ),
              }}
            />
          </Box>

          <TableContainer sx={{ maxHeight: 440 }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>{t("organizationName")}</TableCell>
                  <TableCell>{t("organizationNumber")}</TableCell>
                  <TableCell>{t("status")}</TableCell>
                  <TableCell></TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {showNoResults ? (
                  <TableRow>
                    <TableCell colSpan={3} align="center">
                      {t("noOrganizationFound")}
                    </TableCell>
                  </TableRow>
                ) : (
                  <>
                    {societyList.map((e, i) => (
                      <TableRow>
                        <TableCell>{e.societyName ?? ""}</TableCell>
                        <TableCell>{e.societyNo ?? ""}</TableCell>
                        <TableCell>
                          {e.committeeStatus == null || e.committeeStatus == "2"
                            ? t("notMember")
                            : t("member")}
                        </TableCell>
                        <TableCell>{renderButtons(e)}</TableCell>
                      </TableRow>
                    ))}
                  </>
                )}
              </TableBody>
            </Table>
          </TableContainer>
        </DialogContent>
      </Dialog>

      <ConfirmationDialog
        open={openConfirmDialog}
        onClose={() => setOpenConfirmDialog(false)}
        isMutation={isDeletingSociety}
        onConfirm={handleConfirmDelete}
        onCancel={() => setOpenConfirmDialog(false)}
        title={t("confirmDelete")}
        message={t("deleteOrganizationConfirmation")}
      />
    </>
  );
};

export default PengurusPertubuhanExternalLayout;
