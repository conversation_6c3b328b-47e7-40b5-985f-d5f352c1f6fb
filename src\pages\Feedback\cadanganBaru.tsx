import {
  <PERSON>,
  <PERSON>rid,
  <PERSON>u<PERSON><PERSON>,
  <PERSON>ack,
  TextField,
  Typography,
  FormControl,
  Select,
  FormHelperText,
  Fade,
  Theme,
  useMediaQuery,
} from "@mui/material";
import React, { useEffect, useState } from "react";
import ChevronLeftIcon from "@mui/icons-material/ChevronLeft";
import { useNavigate } from "react-router-dom";
import { t } from "i18next";
import { useCustom, useCustomMutation, CreateResponse } from "@refinedev/core";
import { API_URL } from "../../api";
import {
  IdTypes,
  ListGender,
  MALAYSIA,
  OccupationList,
} from "../../helpers/enums";
import { LoadingOverlay } from "../../components/loading";
import {
  ButtonOutline,
  ButtonPrimary,
  ButtonText,
} from "../../components/button";
import { DocumentUploadType, formatArrayDate, useQuery } from "@/helpers";
import FileUploader from "@/components/input/fileUpload";
import { FeedbackResponseBodyCreate } from "@/types";
import { toCapitalCase, toCapitalFirstLetter } from "@/helpers/string";
import { DialogConfirmation } from "@/components";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "18px",
  fontWeight: "500 !important",
};

const labelSuccessStyle = {
  fontSize: "18px",
  fontWeight: "500 !important",
  color: "#666666",
  pr: 1,
};

const FeedbackSuccess = <
  DataType extends FeedbackResponseBodyCreate = FeedbackResponseBodyCreate
>({
  data,
}: {
  data: CreateResponse<DataType>;
}) => {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [deletePopup, setDeletePopup] = useState(false);
  const { timeStamp, data: rujukanNo } = data?.data;

  const date = new Date(timeStamp);

  // const myDate = date.toLocaleDateString("en-MY");
  const myDate = formatArrayDate(timeStamp);
  const formattedTime = date.toLocaleTimeString("ms-MY", {
    hour: "2-digit",
    minute: "2-digit",
    hour12: true,
  });

  // const myTime = formattedTime.replace("AM", t("AM")).replace("PTG", t("PM"));
  const myTime = formatArrayDate(timeStamp, "HH:mm A");

  const navigate = useNavigate();

  const goBack = (url: string) => {
    if (url) {
      navigate(url);
    } else {
      navigate(-1);
    }
  };

  return (
    <Fade in={true} timeout={500}>
      <Box
        sx={{
          backgroundColor: "white",
          p: 3,
          borderRadius: "15px",
          maxWidth: "881px",
          width: "100%",
        }}
      >
        <Box
          sx={{
            p: { xs: 1, sm: 2, md: 3 },
            border: "1px solid #D9D9D9",
            borderRadius: "14px",
            mb: 2,
          }}
        >
          <Typography sx={sectionStyle}>
            {toCapitalCase(t("feedBackSuccessSent"))}
          </Typography>

          <Box sx={{ display: "flex", alignItems: "center" }}>
            <Typography sx={labelSuccessStyle}>
              {toCapitalCase(t("noRujukanMaklumbalas"))}:
            </Typography>
            <Typography>{rujukanNo}</Typography>
          </Box>

          <Stack flexDirection={"column"} sx={{ my: 4 }}>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Typography sx={labelSuccessStyle}>
                {toCapitalCase(t("date"))}:
              </Typography>
              <Typography>{myDate}</Typography>
            </Box>
            <Box sx={{ display: "flex", alignItems: "center" }}>
              <Typography sx={labelSuccessStyle}>
                {toCapitalCase(t("time"))}:
              </Typography>
              <Typography>{myTime}</Typography>
            </Box>
          </Stack>

          <Box>
            <Typography sx={labelSuccessStyle}>
              {toCapitalCase(t("silaNoSemak"))}
            </Typography>
          </Box>

          <Typography sx={labelSuccessStyle}>
            {toCapitalCase(t("thankyou"))}!
          </Typography>
        </Box>

        <Grid
          item
          xs={12}
          sx={{
            mt: 2,
            display: "flex",
            flexDirection: isMobile ? "column" : "row",
            justifyContent: "flex-end",
            gap: 1,
          }}
        >
          <ButtonPrimary
            variant="contained"
            sx={{
              width: isMobile ? "100%" : "auto",
            }}
            onClick={() => goBack("/feedback")}
          >
            {toCapitalCase(t("back2"))}
          </ButtonPrimary>
        </Grid>
      </Box>
    </Fade>
  );
};

function FeedbackBaru<
  SuccessDataType extends FeedbackResponseBodyCreate = FeedbackResponseBodyCreate
>() {
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );

  const navigate = useNavigate();

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const inputStyle = {};

  const labelStyle = {
    padding: "0px",
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
    display: "flex",
    alignItems: "center",
  };

  const [identificationType, setIdentificationType] = useState("");
  const [type, setType] = useState("");
  const [title, setTitle] = useState("");
  const [gender, setGender] = useState("");
  const [occupation, setOccupation] = useState("");
  //
  const [district, setDistrict] = useState("");
  const [state, setState] = useState("");
  const [changeState, setChangeState] = useState(true);
  const [postcode, setPostcode] = useState("");
  const [dialcode, setDialcode] = useState("+60");
  const [phone, setPhone] = useState("");

  const [user, setUser] = useState<any>("");
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [feedbackId, setFeedbackId] = useState<any>();

  const token = localStorage.getItem("refine-auth");

  const { refetch: fetchUser } = useQuery({
    url: "user/auth/me",
    autoFetch: false,
    onSuccess: (data) => {
      const userData = data?.data?.data;

      if (userData) setUser(userData);
    },
  });

  useEffect(() => {
    if (typeof window !== "undefined" && token) {
      fetchUser();
    }
  }, [fetchUser]);

  const [successData, setSuccessData] =
    useState<CreateResponse<SuccessDataType> | null>(null);

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [formData, setFormData] = useState({
    stateCode: "",
    districtCode: "",
    type: "",
    title: "",
    details: "",
    name: "",
    identificationType: "",
    identificationNo: "",
    gender: "",
    occupation: "",
    city: "",
    postcode: "",
    email: "",
    dialcode: "",
    phoneNumber: "",
    homeDialcode: "",
    homePhonenumber: "",
  });

  /**
   * @todo remove this code
   * why we need this combination, while `t` method is reactive when language changed?
   */
  // <-- [START] -->
  const [occupationTranslatedList, setOccupationTranslatedList] = useState<
    { value: string; label: string }[]
  >([]);

  useEffect(() => {
    const newOList = OccupationList.map((item) => ({
      ...item,
      label: t(item.label),
    }));
    setOccupationTranslatedList(newOList);
  }, [t]);
  // <-- [END] -->

  /**
   * @todo create separate method to set initial value of the feedback form
   */
  // <-- [START] -->
  useEffect(() => {
    if (user) {
      setIdentificationType(user.identificationType);
      setGender(user.gender);
      setOccupation(user.occupation);
      setState(user.stateCode);
      setDistrict(user.districtCode);
      setPostcode(user.postcode);
      const phoneNumber = user?.mobilePhone?.replace(/^\+\d{2}/, "");
      const homePhonenumber = user?.homePhonenumber?.replace(/^\+\d{2}/, "");

      setFormData((prev) => ({
        ...prev,
        name: user.name || "",
        identificationType: user.identificationType || "",
        identificationNo: user.identificationNo || "",
        stateCode: user.stateCode || "",
        districtCode: user.districtCode || "",
        details: user.details || "",
        gender: user.gender || "",
        occupation: user.occupation || "",
        city: user.city || "",
        postcode: user.postcode || "",
        email: user.email || "",
        phoneNumber: phoneNumber || "",
        homePhonenumber: homePhonenumber || "",
      }));
    }
  }, [user]);
  // <-- [END] -->

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };

  const goBack = () => {
    navigate("/feedback");
  };

  const { mutate: createFeedback, isLoading: createFeedBackIsloading } =
    useCustomMutation<SuccessDataType>();

  const handleSave = () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    const { phoneNumber, homePhonenumber, ...rest } = formData;
    const combinedPhoneNumber = `+60${phoneNumber}`;
    const combinedHomePhoneNumber = `+60${homePhonenumber}`;
    const data = {
      ...rest,
      contactNo: combinedPhoneNumber,
      homeContactNo: combinedHomePhoneNumber,
    };
    const config = token
      ? {
          headers: {
            "Content-Type": "application/json",
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${token}`,
          },
        }
      : undefined;

    createFeedback(
      {
        method: "post",
        url: `${API_URL}/complaint/feedback/create`,
        values: { ...data },
        config,
        // successNotification: (data) => {},
        errorNotification: (data) => {
          return {
            message: data?.response?.data?.msg,
            type: "error",
          };
        },
      },
      {
        onSuccess(data) {
          if (data?.data?.status === "SUCCESS") {
            setFeedbackId(data?.data?.data);

            setTimeout(() => {
              setSuccessData(data);
            }, 5000);

            return {
              message: data?.data?.msg ?? "",
              type: "success",
            };
          } else {
            return {
              message: data?.data?.msg,
              type: "error",
            };
          }
        },
      }
    );
  };

  const handleReset = () => {
    // Reset form data to initial state
    setFormData({
      stateCode: "",
      districtCode: "",
      type: "",
      title: "",
      details: "",
      name: "",
      identificationType: "",
      identificationNo: "",
      gender: "",
      occupation: "",
      city: "",
      postcode: "",
      email: "",
      dialcode: "+60",
      phoneNumber: "",
      homeDialcode: "+60",
      homePhonenumber: "",
    });
    setType("");
    setTitle("");
    setGender("");
    setOccupation("");
    setDistrict("");
    setState("");
    setIdentificationType("");
    setChangeState(false);
    setFormErrors({});
  };

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!formData.type) errors.type = t("requiredValidation");
    if (!formData.title) errors.title = t("requiredValidation");
    if (!formData.details) errors.details = t("requiredValidation");
    if (!formData.name) errors.name = t("requiredValidation");
    if (!formData.identificationType)
      errors.identificationType = t("requiredValidation");
    if (!formData.identificationNo)
      errors.identificationNo = t("requiredValidation");
    if (!formData.gender) errors.gender = t("requiredValidation");
    if (!formData.occupation) errors.occupation = t("requiredValidation");
    if (!formData.stateCode) errors.stateCode = t("requiredValidation");
    if (!formData.districtCode) errors.districtCode = t("requiredValidation");
    // if (!formData.city) errors.city = t("requiredValidation");
    if (!formData.districtCode) errors.districtCode = t("requiredValidation");

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email) {
      if (!emailRegex.test(formData.email)) {
        errors.email = t("invalidEmail");
      }
    }
    // Add postcode validation
    if (!formData.postcode) {
      errors.postcode = t("requiredValidation");
    } else if (!/^\d{5}$/.test(formData.postcode)) {
      errors.postcode = t("postcodeValidation");
    }

    return errors;
  };

  const { data, isLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
      },
    },
  });

  const addressData = data?.data?.data || [];

  const onConfirmSubmit = () => {
    try {
      handleSave();
    } finally {
      setIsPopupOpen(false);
    }
  };

  return (
    <>
      <Box
        sx={{
          backgroundImage: 'url("/bg-feedback.jpg")',
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
          backgroundPosition: "center center",
          "&::before": {
            content: '""',
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            background: "rgba(0, 0, 0, 0.3)",
          },
          "& > *": {
            position: "relative",
            zIndex: 2,
          },
          display: "flex",
          justifyContent: "center",
          alignContent: "center",
          pt: 4,
          height: "100vh",
          overflowY: "auto",
        }}
      >
        <LoadingOverlay isLoading={isLoading} />
        <Box>
          <Box
            sx={{
              maxWidth: "881px",
              width: "100%",
              flex: 1,
              p: { xs: 2, sm: 0 },
            }}
          >
            <Box
              sx={{
                display: "flex",
                alignItems: "center",
                flexDirection: "row",
                gap: 1,
              }}
            >
              <ChevronLeftIcon
                sx={{ cursor: "pointer", color: "#fff" }}
                onClick={goBack}
              />
              <Typography
                sx={{
                  fontSize: "20px",
                  fontWeight: 500,
                  py: 3,
                  color: "#fff",
                }}
              >
                {toCapitalCase(t("cadanganMaklumbalasbaru"))}
              </Typography>
            </Box>
            {successData ? (
              <FeedbackSuccess data={successData} />
            ) : (
              <Box>
                {/* form */}
                <Fade in={true} timeout={500}>
                  <Box
                    sx={{
                      backgroundColor: "white",
                      p: 3,
                      borderRadius: "15px",
                    }}
                  >
                    <Box
                      sx={{
                        p: { xs: 1, sm: 2, md: 3 },
                        border: "1px solid #D9D9D9",
                        borderRadius: "14px",
                        mb: 2,
                      }}
                    >
                      <Typography variant="subtitle1" color="primary">
                        {t("cadanganDanMaklumBalas")}
                      </Typography>

                      <Grid container spacing={2} sx={{ pl: 0, mb: 6 }}>
                        {/* jenis */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("jenis"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <FormControl
                            fullWidth
                            required
                            error={!!formErrors.type}
                          >
                            <Select
                              sx={inputStyle}
                              value={type}
                              displayEmpty
                              required
                              onChange={(e) => {
                                setType(e.target.value);
                                setFormData((prevState) => ({
                                  ...prevState,
                                  type: e.target.value,
                                }));
                                setFormErrors((prev) => ({
                                  ...prev,
                                  type: "",
                                }));
                              }}
                            >
                              <MenuItem value="" disabled>
                                {toCapitalCase(t("pleaseSelect"))}
                              </MenuItem>
                              <MenuItem value={"MAKLUM_BALAS"}>
                                {toCapitalCase(t("jenis_MaklumBalas"))}
                              </MenuItem>
                              <MenuItem value={"ADUAN"}>
                                {toCapitalCase(t("aduan"))}
                              </MenuItem>
                              <MenuItem value={"ISU_SISTEM"}>
                                {toCapitalCase(t("jenis_IsuSistem"))}
                              </MenuItem>
                            </Select>

                            {formErrors.type && (
                              <FormHelperText>{formErrors.type}</FormHelperText>
                            )}
                          </FormControl>
                        </Grid>

                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("titleFeedback"))}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>

                        <Grid item xs={12} sm={8}>
                          <TextField
                            fullWidth
                            required
                            name="title"
                            value={formData.title}
                            onChange={handleInputChange}
                            error={!!formErrors.title}
                            helperText={formErrors.title}
                          />
                        </Grid>

                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("butiranText"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <TextField
                            multiline
                            rows={4}
                            fullWidth
                            required
                            name="details"
                            value={formData.details}
                            onChange={handleInputChange}
                            error={!!formErrors.details}
                            helperText={formErrors.details}
                          />
                        </Grid>
                        {/* negeri */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("state"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <FormControl
                            fullWidth
                            required
                            error={!!formErrors.stateCode}
                          >
                            <Select
                              sx={inputStyle}
                              value={state}
                              displayEmpty
                              required
                              disabled={isLoading}
                              onChange={(e) => {
                                setState(e.target.value);
                                setFormData((prevState) => ({
                                  ...prevState,
                                  stateCode: e.target.value,
                                }));
                                setFormErrors((prev) => ({
                                  ...prev,
                                  stateCode: "",
                                }));
                                setChangeState(false);
                              }}
                            >
                              <MenuItem value="" disabled>
                                {isLoading
                                  ? "Loading..."
                                  : toCapitalCase(t("pleaseSelect"))}
                              </MenuItem>
                              {!isLoading &&
                                addressData
                                  .filter((item: any) => item.pid === MALAYSIA)
                                  .map((item: any) => (
                                    <MenuItem key={item.id} value={item.id}>
                                      {item.name}
                                    </MenuItem>
                                  ))}
                            </Select>

                            {formErrors.stateCode && (
                              <FormHelperText>
                                {formErrors.stateCode}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Grid>

                        {/* --------------------- */}
                      </Grid>
                    </Box>
                    {/* FILE UPLOAD */}
                    <FileUploader
                      title="lampiranCadanganDanMaklumbalas"
                      type={DocumentUploadType.FEEDBACK}
                      withAuthHeaders={token ? true : false}
                      uploadAfterSubmit={true}
                      uploadAfterSubmitIndicator={feedbackId}
                      feedbackId={feedbackId}
                      sxContainer={{
                        border: "1px solid #ccc",
                        background: "#fff",
                        mb: 3,
                      }}
                      validTypes={[
                        "application/pdf",
                        "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        "application/msword",
                        "text/plain",
                      ]}
                    />
                  </Box>
                </Fade>
                <Fade in={true} timeout={500}>
                  <Box
                    sx={{
                      backgroundColor: "white",
                      p: 3,
                      mt: 3,
                      borderRadius: "15px",
                    }}
                  >
                    <Box
                      sx={{
                        p: { xs: 1, sm: 2, md: 3 },
                        border: "1px solid #D9D9D9",
                        borderRadius: "14px",
                        mb: 2,
                      }}
                    >
                      <Typography variant="subtitle1" color="primary">
                        {toCapitalCase(t("personalInfo"))}
                      </Typography>

                      <Grid container spacing={2} sx={{ pl: 0, mb: 6 }}>
                        {/* name penuh */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {t("fullName")}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <TextField
                            sx={inputStyle}
                            fullWidth
                            required
                            name="name"
                            value={formData.name}
                            onChange={handleInputChange}
                            error={!!formErrors.name}
                            helperText={formErrors.name}
                          />
                        </Grid>
                        {/* jenis pengenalan diri */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {t("idType")}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <FormControl
                            fullWidth
                            required
                            error={!!formErrors.identificationType}
                          >
                            <Select
                              sx={inputStyle}
                              value={identificationType}
                              displayEmpty
                              required
                              onChange={(e) => {
                                setIdentificationType(e.target.value);
                                setFormData((prevState) => ({
                                  ...prevState,
                                  identificationType: e.target.value,
                                }));
                                setFormErrors((prev) => ({
                                  ...prev,
                                  identificationType: "",
                                }));
                              }}
                            >
                              <MenuItem value="" disabled>
                                {toCapitalCase(t("pleaseSelect"))}
                              </MenuItem>
                              {IdTypes.map((item, index) => {
                                const { label, value } = item;
                                return (
                                  <MenuItem key={index + label} value={value}>
                                    {t(label)}
                                  </MenuItem>
                                );
                              })}
                            </Select>

                            {formErrors.identificationType && (
                              <FormHelperText>
                                {formErrors.identificationType}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Grid>
                        {/* no pengenalan diri */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {t("idNumber")}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <TextField
                            sx={inputStyle}
                            fullWidth
                            required
                            name="identificationNo"
                            value={formData.identificationNo}
                            onChange={handleInputChange}
                            error={!!formErrors.identificationNo}
                            helperText={formErrors.identificationNo}
                          />
                        </Grid>
                        {/* gender */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("gender"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <FormControl
                            fullWidth
                            required
                            error={!!formErrors.gender}
                          >
                            <Select
                              sx={inputStyle}
                              value={gender}
                              displayEmpty
                              required
                              onChange={(e) => {
                                setGender(e.target.value);
                                setFormData((prevState) => ({
                                  ...prevState,
                                  gender: e.target.value,
                                }));
                                setFormErrors((prev) => ({
                                  ...prev,
                                  gender: "",
                                }));
                              }}
                            >
                              <MenuItem value="" disabled>
                                {toCapitalCase(t("pleaseSelect"))}
                              </MenuItem>
                              {ListGender.map((item) => (
                                <MenuItem key={item.value} value={item.value}>
                                  {t(item.label)}
                                </MenuItem>
                              ))}
                            </Select>
                            {formErrors.gender && (
                              <FormHelperText>
                                {formErrors.gender}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Grid>
                        {/* pekerjaan */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("pekerjaan"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <FormControl
                            fullWidth
                            required
                            error={!!formErrors.occupation}
                          >
                            <Select
                              sx={inputStyle}
                              value={occupation}
                              displayEmpty
                              required
                              onChange={(e) => {
                                setOccupation(e.target.value);
                                setFormData((prevState) => ({
                                  ...prevState,
                                  occupation: e.target.value,
                                }));
                                setFormErrors((prev) => ({
                                  ...prev,
                                  occupation: "",
                                }));
                              }}
                            >
                              <MenuItem value="" disabled>
                                {toCapitalCase(t("pleaseSelect"))}
                              </MenuItem>
                              {occupationTranslatedList.map(
                                (
                                  item // use direct `OccupationList` instead
                                ) => (
                                  <MenuItem key={item.value} value={item.value}>
                                    {
                                      item.label /** replace with `t(item.label)` instead */
                                    }
                                  </MenuItem>
                                )
                              )}
                            </Select>
                            {formErrors.occupation && (
                              <FormHelperText>
                                {formErrors.occupation}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Grid>
                        {/* negeri */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("state"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <FormControl
                            fullWidth
                            required
                            error={!!formErrors.stateCode}
                          >
                            <Select
                              sx={inputStyle}
                              value={state}
                              displayEmpty
                              required
                              disabled={isLoading}
                              onChange={(e) => {
                                setState(e.target.value);
                                setFormData((prevState) => ({
                                  ...prevState,
                                  stateCode: e.target.value,
                                }));
                                setFormErrors((prev) => ({
                                  ...prev,
                                  stateCode: "",
                                }));
                                setChangeState(false);
                              }}
                            >
                              <MenuItem value="" disabled>
                                {isLoading
                                  ? "Loading..."
                                  : toCapitalCase(t("pleaseSelect"))}
                              </MenuItem>
                              {!isLoading &&
                                addressData
                                  .filter((item: any) => item.pid === MALAYSIA)
                                  .map((item: any) => (
                                    <MenuItem key={item.id} value={item.id}>
                                      {item.name}
                                    </MenuItem>
                                  ))}
                            </Select>

                            {formErrors.stateCode && (
                              <FormHelperText>
                                {formErrors.stateCode}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Grid>
                        {/* daerah */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("district"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <FormControl
                            fullWidth
                            required
                            error={!!formErrors.districtCode}
                          >
                            <Select
                              sx={inputStyle}
                              value={district}
                              displayEmpty
                              required
                              onChange={(e) => {
                                setDistrict(e.target.value);
                                setFormData((prevState) => ({
                                  ...prevState,
                                  districtCode: e.target.value,
                                }));
                                setFormErrors((prev) => ({
                                  ...prev,
                                  districtCode: "",
                                }));
                              }}
                              disabled={isLoading || !state}
                            >
                              <MenuItem value="" disabled>
                                {isLoading
                                  ? "Loading..."
                                  : toCapitalCase(t("selectPlaceholder"))}
                              </MenuItem>
                              {!isLoading &&
                                addressData
                                  .filter((item: any) => item.pid == state)
                                  .map((item: any) => (
                                    <MenuItem key={item.id} value={item.id}>
                                      {item.name}
                                    </MenuItem>
                                  ))}
                            </Select>
                            {formErrors.districtCode && (
                              <FormHelperText>
                                {formErrors.districtCode}
                              </FormHelperText>
                            )}
                          </FormControl>
                        </Grid>
                        {/* bandar */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>{toCapitalCase(t("bandar"))}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <TextField
                            sx={inputStyle}
                            fullWidth
                            name="city"
                            value={formData.city}
                            onChange={handleInputChange}
                            error={!!formErrors.city}
                            helperText={formErrors.city}
                          />
                        </Grid>
                        {/* poskod */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("poskod"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <TextField
                            sx={inputStyle}
                            fullWidth
                            required
                            name="postcode"
                            // value={formData.postcode}
                            // onChange={handleInputChange}
                            // error={!!formErrors.postcode}
                            value={postcode}
                            error={!!formErrors.postcode}
                            helperText={formErrors.postcode}
                            onChange={(e) => {
                              if (/^\d{0,5}$/.test(e.target.value)) {
                                setPostcode(e.target.value);
                                setFormData((prevState) => ({
                                  ...prevState,
                                  postcode: e.target.value,
                                }));
                              }
                              setFormErrors((prev) => ({
                                ...prev,
                                postcode: "",
                              }));
                            }}
                            inputProps={{ pattern: "\\d{5}" }}
                          />
                        </Grid>
                        {/* emel */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {toCapitalCase(t("emel"))}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={8}>
                          <TextField
                            sx={inputStyle}
                            fullWidth
                            required
                            name="email"
                            value={formData.email}
                            error={!!formErrors.email}
                            helperText={formErrors.email}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              handleInputChange(e);
                              setFormErrors((prev) => ({ ...prev, email: "" }));
                            }}
                            // onBlur={() => validateEmail(formData.email)}
                          />
                        </Grid>

                        {/* phone */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>
                            {t("phoneNumber")}{" "}
                            <span style={{ color: "red" }}>*</span>
                          </Typography>
                        </Grid>
                        <Grid item xs={12} sm={2}>
                          <TextField
                            sx={{
                              backgroundColor: "#E8E9E8",
                              ...inputStyle,
                            }}
                            fullWidth
                            required
                            name="dialcode"
                            placeholder="+60"
                            disabled
                            value={"+60"}
                            inputProps={{
                              inputMode: "numeric",
                              pattern: "[0-9]*",
                            }}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              const value = e.target.value;
                              if (/^\d*$/.test(value)) {
                                handleInputChange(e);
                              }
                            }}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            sx={inputStyle}
                            fullWidth
                            required
                            name="phoneNumber"
                            value={formData.phoneNumber}
                            inputProps={{
                              inputMode: "numeric",
                              pattern: "\\d{8,10}",
                              maxLength: 10,
                            }}
                            error={
                              formData.phoneNumber.length > 0 &&
                              (formData.phoneNumber.length < 8 ||
                                formData.phoneNumber.length > 10)
                            }
                            helperText={
                              formData.phoneNumber.length > 0 &&
                              (formData.phoneNumber.length < 8 ||
                                formData.phoneNumber.length > 10)
                                ? t("feedbackPhoneNoErr")
                                : ""
                            }
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              const value = e.target.value;
                              if (/^\d{0,10}$/.test(value)) {
                                handleInputChange(e);
                              }
                            }}
                          />
                        </Grid>
                        {/* home phone */}
                        <Grid item xs={12} sm={4} sx={labelStyle}>
                          <Typography>{t("nomborTelefonRumah")}</Typography>
                        </Grid>
                        <Grid item xs={12} sm={2}>
                          <TextField
                            sx={{
                              backgroundColor: "#E8E9E8",
                              ...inputStyle,
                            }}
                            fullWidth
                            required
                            name="homeDialcode"
                            value={"+60"}
                            inputProps={{
                              inputMode: "numeric",
                              pattern: "[0-9]*",
                            }}
                            placeholder="+60"
                            disabled
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              const value = e.target.value;
                              if (/^\d*$/.test(value)) {
                                handleInputChange(e);
                              }
                            }}
                          />
                        </Grid>
                        <Grid item xs={12} sm={6}>
                          <TextField
                            sx={inputStyle}
                            fullWidth
                            required
                            name="homePhonenumber"
                            value={formData.homePhonenumber}
                            inputProps={{
                              inputMode: "numeric",
                              pattern: "[0-9]*",
                            }}
                            onChange={(
                              e: React.ChangeEvent<HTMLInputElement>
                            ) => {
                              const value = e.target.value;
                              if (/^\d*$/.test(value)) {
                                handleInputChange(e);
                              }
                            }}
                          />
                        </Grid>
                      </Grid>
                    </Box>

                    <Grid
                      item
                      xs={12}
                      sx={{
                        mt: 2,
                        display: "flex",
                        flexDirection: isMobile ? "column" : "row",
                        justifyContent: "flex-end",
                        gap: 1,
                      }}
                    >
                      <ButtonOutline
                        sx={{
                          bgcolor: "white",
                          "&:hover": { bgcolor: "white" },
                          width: isMobile ? "100%" : "auto",
                        }}
                        onClick={handleReset}
                      >
                        {toCapitalCase(t("semula"))}
                      </ButtonOutline>
                      <ButtonPrimary
                        variant="contained"
                        sx={{
                          width: isMobile ? "100%" : "auto",
                        }}
                        onClick={() => setIsPopupOpen(true)}
                        disabled={createFeedBackIsloading}
                      >
                        {toCapitalCase(t("hantar"))}
                      </ButtonPrimary>
                    </Grid>
                  </Box>
                </Fade>
              </Box>
            )}
          </Box>
          {/* spacing */}
          <Box sx={{ height: "80px" }} />
        </Box>
      </Box>
      <DialogConfirmation
        open={isPopupOpen}
        onClose={() => setIsPopupOpen(false)}
        onAction={onConfirmSubmit}
        isMutating={createFeedBackIsloading}
        onConfirmationText={t("sureToSubmitFeedback")}
      />
    </>
  );
}

export default FeedbackBaru;
