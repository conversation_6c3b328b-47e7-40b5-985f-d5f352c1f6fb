import { BaseRecord, HttpError } from "@refinedev/core";
import { UseDataGridProps, useDataGrid as useDefaultDataGrid } from "@refinedev/mui";

export function useDataGrid<
  TQueryFnData extends BaseRecord = BaseRecord,
  TError extends HttpError = HttpError,
  TSearchVariables = unknown,
  TData extends BaseRecord = TQueryFnData,
  Options extends UseDataGridProps<
    TQueryFnData,
    TError,
    TSearchVariables,
    TData
  > = UseDataGridProps<
    TQueryFnData,
    TError,
    TSearchVariables,
    TData
  >
>(options: Options) {
  // set default options for our app
  // <-- [START] -->
  const castedOptions = options as any;
  castedOptions.meta ??= {};
  castedOptions.meta.useTable ??= true;
  castedOptions.meta.overrideGetList ??= true;
  castedOptions.meta.headers ??= {};
  castedOptions.meta.headers.portal ??= localStorage.getItem('portal');
  castedOptions.meta.headers["Authorization"] ??= `Bearer ${localStorage.getItem('refine-auth')}`;
  castedOptions.pagination ??= {};
  castedOptions.pagination.pageSize ??= 10;
  castedOptions.syncWithLocation ??= false;
  // <-- [END] -->
  return useDefaultDataGrid<TQueryFnData, TError, TSearchVariables, TData>(castedOptions);
};
