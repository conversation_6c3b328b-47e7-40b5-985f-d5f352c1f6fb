export interface ICancelledSociety {
  id: number;
  societyId: number;
  societyNo: string;
  societyName: string;
  branchId: number;
  branchNo: string;
  branchName: string;
  cancelledDate: string;
  section: string;
  reason: string;
  societyStatusCode: string;
}

export interface ISection {
  code: string;
  description: string;
  section: string;
  isBlacklistPermanent: boolean;
}

export interface IDocumentGuidance {
  id: number;
  name: string;
  referenceNumber: string;
  category: number;
  effectiveDate: string;
  yearOfRelease: number;
  summary: string;
  uploadDate: string;
  uploadedBy: string;
  activationStatus: number;
  visibilityStatus: number;
  url: string;
  createdDate: string;
  createdBy: number;
  modifiedDate: string;
  modifiedBy: string;
  status: boolean;
}
