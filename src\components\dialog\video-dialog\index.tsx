import {
  Box,
  Dialog,
  DialogContent,
  IconButton,
  LinearProgress,
  MenuItem,
  Select,
  SelectChangeEvent,
  Slider,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import PauseIcon from "@mui/icons-material/Pause";
import PlayArrowIcon from "@mui/icons-material/PlayArrow";
import VolumeUpIcon from "@mui/icons-material/VolumeUp";
import VolumeOffIcon from "@mui/icons-material/VolumeOff";
import React, { useEffect, useRef, useState } from "react";

type ConfirmationDialogProps = {
  open: boolean;
  onClose: () => void;
  url: string;
};

const VideoDialog: React.FC<ConfirmationDialogProps> = ({
  open,
  onClose,
  url,
}) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [progress, setProgress] = useState(0);
  const [muted, setMuted] = useState(false);
  const [volume, setVolume] = useState(100);
  const [playbackRate, setPlaybackRate] = useState(1);

  const videoRef = useRef<HTMLVideoElement>(null);

  const handleTimeUpdate = () => {
    if (videoRef.current) {
      const currentTime = videoRef.current.currentTime;
      const duration = videoRef.current.duration;
      setProgress((currentTime / duration) * 100);
    }
  };

  const togglePlayPause = () => {
    if (videoRef.current) {
      if (videoRef.current.paused) {
        videoRef.current.play();
        setIsPlaying(true);
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }
  };

  const handleProgressClick = (event: React.MouseEvent<HTMLDivElement>) => {
    if (videoRef.current) {
      const rect = event.currentTarget.getBoundingClientRect();
      const clickX = event.clientX - rect.left;
      const clickRatio = clickX / rect.width;
      const newTime = clickRatio * videoRef.current.duration;
      videoRef.current.currentTime = newTime;
      setProgress(clickRatio * 100);
    }
  };

  const handleMuteToggle = () => {
    if (videoRef.current) {
      videoRef.current.muted = !muted;
      setMuted(!muted);
    }
  };

  const handleVolumeChange = (_: Event, value: number | number[]) => {
    if (videoRef.current) {
      const volumeValue = Array.isArray(value) ? value[0] : value;
      videoRef.current.volume = volumeValue / 100;
      setVolume(volumeValue);
      if (volumeValue === 0) {
        setMuted(true);
        videoRef.current.muted = true;
      } else {
        setMuted(false);
        videoRef.current.muted = false;
      }
    }
  };

  const handleSpeedChange = (event: SelectChangeEvent<number>) => {
    const rate = Number(event.target.value);
    if (videoRef.current) {
      videoRef.current.playbackRate = rate;
      setPlaybackRate(rate);
    }
  };

  useEffect(() => {
    setProgress(0);
    setIsPlaying(true);
  }, [url]);

  useEffect(() => {
    if (open && videoRef.current) {
      const videoElement = videoRef.current;
      videoElement
        .play()
        .then(() => {
          setIsPlaying(true);
        })
        .catch((error) => {
          console.error("Error attempting to play the video:", error);
        });
    } else if (videoRef.current) {
      videoRef.current.pause();
      setIsPlaying(false);
    }
  }, [open]);

  return (
    <Dialog
      open={open}
      onClose={onClose}
      fullWidth
      maxWidth="lg"
      PaperProps={{
        sx: { borderRadius: 2, overflow: "hidden" },
      }}
    >
      <IconButton
        onClick={onClose}
        sx={{ position: "absolute", top: 10, right: 10, zIndex: 10 }}
      >
        <CloseIcon />
      </IconButton>

      <DialogContent
        sx={{
          padding: 0,
          position: "relative",
          backgroundColor: "black",
          height: 0,
          paddingBottom: "56.25%", // 16:9 ratio
        }}
      >
        <video
          ref={videoRef}
          onTimeUpdate={handleTimeUpdate}
          onCanPlay={() => {
            if (open && videoRef.current) {
              videoRef.current.play();
              videoRef.current.volume = volume / 100;
              videoRef.current.muted = muted;
              videoRef.current.playbackRate = playbackRate;
            }
          }}
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            width: "100%",
            height: "100%",
            objectFit: "cover",
          }}
        >
          <source src={url} type="video/mp4" />
          Your browser does not support the video tag.
        </video>

        {/* Progress Bar */}
        <Box
          onClick={handleProgressClick}
          sx={{
            position: "absolute",
            bottom: { xs: 90, sm: 90, md: 100 },
            left: 0,
            width: "100%",
            cursor: "pointer",
            px: 2,
          }}
        >
          <LinearProgress
            variant="determinate"
            value={progress}
            sx={{
              height: 8,
              borderRadius: 4,
              "& .MuiLinearProgress-bar": { backgroundColor: "#666666" },
              backgroundColor: "#e0e0e0",
            }}
          />
        </Box>

        {/* Control Bar */}
        <Box
          sx={{
            position: "absolute",
            bottom: 20,
            left: 0,
            width: "100%",
            px: 2,
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            zIndex: 10,
            flexWrap: "wrap",
            gap: 1,
          }}
        >
          {/* Left Controls */}
          <Box sx={{ display: "flex", alignItems: "center", gap: 1 }}>
            <IconButton
              onClick={togglePlayPause}
              sx={{
                bgcolor: "rgba(0, 0, 0, 0.5)",
                color: "white",
                "&:hover": { bgcolor: "rgba(0, 0, 0, 0.7)" },
              }}
            >
              {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
            </IconButton>

            <IconButton
              onClick={handleMuteToggle}
              sx={{
                bgcolor: "rgba(0, 0, 0, 0.5)",
                color: "white",
                "&:hover": { bgcolor: "rgba(0, 0, 0, 0.7)" },
              }}
            >
              {muted || volume === 0 ? <VolumeOffIcon /> : <VolumeUpIcon />}
            </IconButton>

            <Slider
              value={volume}
              onChange={handleVolumeChange}
              size="small"
              sx={{
                width: { xs: 80, sm: 100, md: 120 },
                color: "white",
              }}
            />
          </Box>

          {/* Right Controls */}
          <Select<number>
            value={playbackRate}
            onChange={handleSpeedChange}
            size="small"
            sx={{
              bgcolor: "rgba(0,0,0,0.5)",
              color: "white",
              "& .MuiSvgIcon-root": { color: "white" },
              "& .MuiOutlinedInput-notchedOutline": {
                borderColor: "white",
              },
              minWidth: 80,
              height: 40,
            }}
          >
            {[0.25, 0.5, 1, 1.5, 2].map((rate) => (
              <MenuItem key={rate} value={rate}>
                {rate}x
              </MenuItem>
            ))}
          </Select>
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default VideoDialog;
