import { makeStyles } from "@mui/styles";

export const globalStyles = makeStyles(() => ({
  section: {
    background: "#fff",
    borderRadius: 15,
    padding: 16,
  },
  sectionBox: {
    borderRadius: 10,
    border: "0.5px solid #DADADA",
    padding: "22px 34px",
  },
  btnOutline: {
    display: "flex",
    alignItems: "center",
    gap: 1,
    padding: "5px 12px",
    width: "fit-content",
    textTransform: "none",
    color: "#666666",
    fontSize: "12px",
    lineHeight: "20px",
    fontWeight: 400,
    borderRadius: "5px",
    border: "1px solid var(--primary-color)",
    margin: 0,
  },
  statusBadge: {
    background: "none",
    color: "var(--text-grey)",
    padding: "7px 10px",
    borderRadius: "16px",
    fontSize: "10px",
    fontWeight: 500,
    width: "fit-content",
    marginInline: "auto",
  },
  countBadge: {
    background: "var(--primary-color)",
    display: "flex",
    flexDirection: "column",
    alignItems: "center",
    justifyContent: "center",
    color: "#fff",
    borderRadius: "13px",
    padding: "10px 0"
  },
}));
