import React, { useEffect, useState, useRef } from "react";
import { Box, Typography, CircularProgress, Alert, Select, MenuItem, FormControl, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import useReportService from "@/helpers/hooks/useReportService";
import { createEmbeddingContext, DashboardExperience } from "amazon-quicksight-embedding-sdk";
import { IDashboardItem } from "@/services/reportService";
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';

interface DashboardEmbedProps {
  type: string;
  module: string;
  category?: string;
  height?: string;
  containerStyle?: React.CSSProperties;
  // Custom translation keys for labels
  kategoriStatistikKey?: string;
  kategoriKey?: string;
  jenisStatistikKey?: string;
}

const DashboardEmbed: React.FC<DashboardEmbedProps> = ({
  type,
  module,
  category,
  height = "800px",
  containerStyle = {},
  // Default translation keys for backward compatibility
  kategoriStatistikKey = "kategoriStatistik",
  kategoriKey = "kategori",
  jenisStatistikKey = "jenisStatistik"
}) => {
  const { t } = useTranslation();
  const { getRegisteredUserEmbedUrl, getDashboards } = useReportService();
  const [embedUrl, setEmbedUrl] = useState<string>("");
  const [isLoadingGetDashboards, setIsLoadingGetDashboards] = useState(true);
  const [dashboardError, setDashboardError] = useState<string>("");
  const [isDashboardLoading, setIsDashboardLoading] = useState(true);
  const dashboardContainerRef = useRef<HTMLDivElement>(null);
  const dashboardRef = useRef<DashboardExperience | null>(null);

  // New state for filtering functionality
  const [dashboardsData, setDashboardsData] = useState<IDashboardItem[]>([]);
  const [categories, setCategories] = useState<string[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("");
  const [selectedDashboard, setSelectedDashboard] = useState<string>("");
  const [filteredDashboards, setFilteredDashboards] = useState<IDashboardItem[]>([]);
  const [showFilters, setShowFilters] = useState<boolean>(false);

  // Function to format category display name (title case and replace _ with space)
  const formatCategoryDisplayName = (category: string): string => {
    return category
      .replace(/_/g, ' ') // Replace underscores with spaces
      .split(' ')
      .map(word => {
        // Capitalize first letter of each word, rest lowercase
        return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
      })
      .join(' ');
  };

  // Function to embed a dashboard by ID
  const embedDashboard = async (dashboardId: string) => {
    try {
      // Set loading state only for dashboard container
      setIsDashboardLoading(true);
      setDashboardError("");

      const embedResponse = await getRegisteredUserEmbedUrl({
        dashboardId,
      });

      if (embedResponse.data?.embedUrl) {
        setEmbedUrl(embedResponse.data.embedUrl);
        // Keep dashboard loading true until the dashboard actually loads
      } else {
        setDashboardError(t("failedLoadDashboardError"));
        setIsDashboardLoading(false);
      }
    } catch (err) {
      console.error("Failed to get embed URL:", err);
      setDashboardError(t("failedLoadDashboardError"));
      setIsDashboardLoading(false);
    }
  };

  // Handle category selection
  const handleCategoryChange = (category: string) => {
    setSelectedCategory(category);

    // Filter dashboards by selected category
    const filtered = dashboardsData.filter(d => d.category === category);
    setFilteredDashboards(filtered);

    // Auto-select first dashboard in the category
    if (filtered.length > 0) {
      setSelectedDashboard(filtered[0].dashboardId);
      embedDashboard(filtered[0].dashboardId);
    }
  };

  // Handle dashboard selection
  const handleDashboardChange = (dashboardId: string) => {
    setSelectedDashboard(dashboardId);
    embedDashboard(dashboardId);
  };

  // Fetch dashboards data and determine if filters are needed
  useEffect(() => {
    const fetchDashboards = async () => {
      try {
        setIsLoadingGetDashboards(true);
        setDashboardError("");

        // Get the dashboard information based on type, module, and category
        const dashboardsResponse = await getDashboards({
          type,
          module,
          category
        });

        if (!dashboardsResponse.data || dashboardsResponse.data.length === 0) {
          setDashboardError(t("noDashboardFoundError"));
          setIsLoadingGetDashboards(false);
          setIsDashboardLoading(false);
          return;
        }

        const dashboards = dashboardsResponse.data;
        setDashboardsData(dashboards);

        // Extract unique categories
        const uniqueCategories = [...new Set(dashboards.map(d => d.category).filter(Boolean))] as string[];
        setCategories(uniqueCategories);

        // Determine if filters should be shown
        // Show filters if:
        // 1. Multiple categories exist, OR
        // 2. Category is null but multiple dashboards exist (show only Jenis Statistik dropdown)
        if (uniqueCategories.length > 1 || (!category && dashboards.length > 1)) {
          setShowFilters(true);

          if (uniqueCategories.length > 1) {
            // Multiple categories: show both dropdowns
            const firstCategory = uniqueCategories[0];
            setSelectedCategory(firstCategory);
            const firstCategoryDashboards = dashboards.filter(d => d.category === firstCategory);
            setFilteredDashboards(firstCategoryDashboards);
            if (firstCategoryDashboards.length > 0) {
              setSelectedDashboard(firstCategoryDashboards[0].dashboardId);
              await embedDashboard(firstCategoryDashboards[0].dashboardId);
            }
          } else {
            // Single category but multiple dashboards: show only Jenis Statistik dropdown
            setFilteredDashboards(dashboards);
            setSelectedDashboard(dashboards[0].dashboardId);
            await embedDashboard(dashboards[0].dashboardId);
          }
        } else {
          // If only one dashboard, directly embed it
          setShowFilters(false);
          await embedDashboard(dashboards[0].dashboardId);
        }
        setIsLoadingGetDashboards(false);
      } catch (err) {
        console.error("Failed to fetch dashboards:", err);
        setDashboardError(t("failedLoadDashboardError"));
        setIsLoadingGetDashboards(false);
      }
      // Note: Don't set setIsLoadingDashboard(false) here as embedDashboard will handle it
    };

    fetchDashboards();
  }, [type, module, category, getDashboards, t]);

  // Embed QuickSight dashboard using SDK
  useEffect(() => {
    if (embedUrl && dashboardContainerRef.current) {
      const embedDashboardAsync = async () => {
        try {
          setIsDashboardLoading(true);

          // Clean up previous dashboard if exists
          if (dashboardRef.current) {
            // Note: DashboardExperience doesn't have a close method,
            // cleanup is handled by the SDK automatically
            dashboardRef.current = null;
          }

          // Create embedding context
          const embeddingContext = await createEmbeddingContext();

          // Embed the dashboard
          const dashboard = await embeddingContext.embedDashboard({
            url: embedUrl,
            container: dashboardContainerRef.current!,
            height: "100%", // Use 100% to fill container
            width: "100%",
            resizeHeightOnSizeChangedEvent: true,
            className: "quicksight-dashboard"
          }, {
            locale: "en-US",
            toolbarOptions: {
              export: true,
              undoRedo: true,
              reset: true
            },
            onMessage: (messageEvent: any) => {
              if (messageEvent.eventName === "CONTENT_LOADED") {
                setIsDashboardLoading(false);
                console.log("Dashboard loaded successfully");
              } else if (messageEvent.eventName === "ERROR_OCCURRED") {
                console.error("Dashboard error:", messageEvent);
                setDashboardError(t("failedLoadDashboardError"));
                setIsDashboardLoading(false);
              }
            }
          });

          dashboardRef.current = dashboard;

        } catch (error) {
          console.error("Failed to embed dashboard:", error);
          setDashboardError(t("failedLoadDashboardError"));
          setIsDashboardLoading(false);
        }
      };

      embedDashboardAsync();
    }

    // Cleanup function
    return () => {
      if (dashboardRef.current) {
        dashboardRef.current = null;
      }
    };
  }, [embedUrl, t]);

  // Show initial loading only during the very first load
  if (isLoadingGetDashboards) {
    return (
      <Box
        sx={{
          p: 3,
          backgroundColor: "white",
          borderRadius: "1rem",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          minHeight: "400px",
          ...containerStyle
        }}
      >
        <CircularProgress />
        <Typography sx={{ ml: 2 }}>{t("preparingDashboard")}</Typography>
      </Box>
    );
  }

  return (
    <>
      {/* Filter Container - Separate from dashboard */}
      {showFilters && (
        <Box sx={{
          p: 3,
          backgroundColor: "white",
          borderRadius: "1rem",
          mb: 2,
          ...containerStyle
        }}>
          <Typography
            variant="h6"
            sx={{
              color: "#0CA6A6",
              mb: 2,
              fontSize: "14px"
            }}
          >
            {t(kategoriStatistikKey)}
          </Typography>

          <Grid container spacing={3} alignItems="center">
            {/* Category Filter - Only show if multiple categories exist */}
            {categories.length > 1 && (
              <Grid item xs={12} sm={6}>
                <Box>
                  <Typography
                    sx={{
                      color: "#666666",
                      fontSize: "14px",
                      mb: 1,
                      fontWeight: "500"
                    }}
                  >
                    {t(kategoriKey)}
                  </Typography>
                  <FormControl fullWidth size="small">
                    <Select
                      value={selectedCategory}
                      onChange={(e) => handleCategoryChange(e.target.value)}
                      IconComponent={KeyboardArrowDownIcon}
                      sx={{
                        backgroundColor: "white",
                        borderRadius: "8px",
                        fontSize: "14px",
                        "& .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#DADADA",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#0CA6A6",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#0CA6A6",
                        },
                        "& .MuiSelect-select": {
                          padding: "10px 14px",
                        },
                      }}
                    >
                      {categories.map((cat) => (
                        <MenuItem key={cat} value={cat}>
                          <Typography sx={{ fontSize: "14px" }}>{formatCategoryDisplayName(cat)}</Typography>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Box>
              </Grid>
            )}

            {/* Dashboard Type Filter */}
            <Grid item xs={12} sm={6}>
              <Box>
                <Typography
                  sx={{
                    color: "#666666",
                    fontSize: "14px",
                    mb: 1,
                    fontWeight: "500"
                  }}
                >
                  {t(jenisStatistikKey)}
                </Typography>
                <FormControl fullWidth size="small">
                  <Select
                    value={selectedDashboard}
                    onChange={(e) => handleDashboardChange(e.target.value)}
                    disabled={categories.length > 1 && !selectedCategory}
                    IconComponent={KeyboardArrowDownIcon}
                    sx={{
                      backgroundColor: (categories.length > 1 && !selectedCategory) ? "#f5f5f5" : "white",
                      borderRadius: "8px",
                      fontSize: "14px",
                      "& .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#DADADA",
                      },
                      "&:hover .MuiOutlinedInput-notchedOutline": {
                        borderColor: (categories.length > 1 && !selectedCategory) ? "#DADADA" : "#0CA6A6",
                      },
                      "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                        borderColor: "#0CA6A6",
                      },
                      "& .MuiSelect-select": {
                        padding: "10px 14px",
                      },
                    }}
                  >
                    {filteredDashboards.map((dashboard) => (
                      <MenuItem key={dashboard.dashboardId} value={dashboard.dashboardId}>
                        <Typography sx={{ fontSize: "14px" }}>{dashboard.name}</Typography>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Box>
            </Grid>
          </Grid>
        </Box>
      )}

      {/* Dashboard Container - Separate container */}
      <Box sx={{
        p: 3,
        backgroundColor: "white",
        borderRadius: "1rem",
        ...containerStyle
      }}>
        {/* Show loading state when dashboard is loading but no embed URL yet */}
        {isDashboardLoading && !embedUrl && (
          <Box
            sx={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              minHeight: height,
              border: "1px solid #e0e0e0",
              borderRadius: "8px",
              backgroundColor: "#f9f9f9"
            }}
          >
            <CircularProgress size={24} />
            <Typography sx={{ ml: 2, fontSize: "14px" }}>{t("loadingDashboard")}</Typography>
          </Box>
        )}

        {embedUrl && !dashboardError && (
          <>
            <Box
              sx={{
                position: "relative",
                width: "100%",
                height: height,
                border: "1px solid #e0e0e0",
                borderRadius: "8px",
                overflow: "auto" // Enable scrolling
              }}
            >
              {/* QuickSight Dashboard Container */}
              <div
                ref={dashboardContainerRef}
                style={{
                  width: "100%",
                  height: "100%",
                  minHeight: height, // Ensure minimum height but allow expansion
                  border: "none"
                }}
              />

              {/* Loading overlay for dashboard content */}
              {isDashboardLoading && (
                <Box
                  sx={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: "rgba(255, 255, 255, 0.95)",
                    display: "flex",
                    justifyContent: "center",
                    alignItems: "center",
                    zIndex: 1000,
                    borderRadius: "8px"
                  }}
                >
                  <CircularProgress size={24} />
                  <Typography sx={{ ml: 2, fontSize: "14px" }}>{t("loadingDashboard")}</Typography>
                </Box>
              )}
            </Box>
          </>
        )}

        {dashboardError && (
          <Alert severity="error">
            {dashboardError}
          </Alert>
        )}
      </Box>
    </>
  );
};

export default DashboardEmbed;
