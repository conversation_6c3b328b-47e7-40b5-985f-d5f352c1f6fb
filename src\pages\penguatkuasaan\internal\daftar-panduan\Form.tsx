import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useForm, FieldValues } from "react-hook-form";
import { CrudFilter, useGetIdentity } from "@refinedev/core";
import {
  globalStyles,
  useMutation,
  useQuery,
  DocumentCategoryOptions,
  filterEmptyValuesOnObject,
  omitKeysFromObject,
  useUploadPresignedUrl,
  DocumentUploadType,
  useGetDocument,
} from "@/helpers";

import {
  FormFieldRow,
  Label,
  TextFieldController,
  ButtonPrimary,
  CustomSkeleton,
  SelectFieldController,
  DialogConfirmation,
  DatePickerController,
  FileUploadController,
} from "@/components";
import { Box, Typography, CircularProgress } from "@mui/material";

import { IApiResponse, IDocumentGuidance, IUser } from "@/types";

const activationStatusOptions = [
  {
    value: 1,
    label: "Aktif",
  },
  {
    value: 2,
    label: "Inaktif",
  },
];

const visibilityStatusOptions = [
  {
    value: 1,
    label: "Dalaman",
  },
  {
    value: 2,
    label: "Umum",
  },
];

const DaftarPanduanForm: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const classes = globalStyles();
  const { t, i18n } = useTranslation();
  const { data: user } = useGetIdentity<IUser>();

  const isMyLanguage = i18n.language === "my";

  const [dialogOpen, setDialogOpen] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);

  const [selectedPDF, setSelectedPDF] = useState<File | null>(null);
  const [PDFPreview, setPDFPreview] = useState("");

  const [selectedEbook, setSelectedEbook] = useState<File | null>(null);
  const [EbookPreview, setEbookPreview] = useState("");

  const submitDocumentUrl = id
    ? `document/external/${id}`
    : "document/external/create";

  const { control, handleSubmit, getValues, setValue } = useForm<FieldValues>({
    defaultValues: {
      id: "",
      name: "",
      referenceNumber: "",
      category: "",
      effectiveDate: "",
      yearOfRelease: "",
      summary: "",
      uploadDate: "",
      uploadedBy: user?.name ?? "",
      activationStatus: "",
      visibilityStatus: "",
    },
  });

  const { upload: uploadPdfFile, isLoading: isUploadingPdf } =
    useUploadPresignedUrl({
      onSuccessUpload: () => {
        setIsSuccess(true);
        setTimeout(() => navigate("../daftar-panduan"), 2000);
      },
    });

  const { upload: uploadEbookFile, isLoading: isUploadingEbook } =
    useUploadPresignedUrl();

  const { getDocument: getPDFDocument } = useGetDocument({
    onSuccess: (data) => {
      if (data) {
        setValue("urlPdf", data?.name ?? "");
        setPDFPreview(data?.url ?? "");
      }
    },
  });

  const { getDocument: getEbookDocument } = useGetDocument({
    onSuccess: (data) => {
      if (data) {
        setValue("urlEbook", data?.name ?? "");
        setEbookPreview(data?.url ?? "");
      }
    },
  });

  const { isLoading: isLoadingDocumentDetail } = useQuery<
    IApiResponse<IDocumentGuidance>
  >({
    url: `document/external/${id}`,
    autoFetch: !!id,
    onSuccess: (res) => {
      const documentDetail = res?.data?.data;

      if (documentDetail) {
        setValue("id", id);
        setValue("name", documentDetail.name);
        setValue("referenceNumber", documentDetail.referenceNumber);
        setValue("category", documentDetail.category);
        setValue("effectiveDate", documentDetail.effectiveDate);
        setValue("yearOfRelease", documentDetail.yearOfRelease);
        setValue("summary", documentDetail.summary);
        setValue("uploadDate", documentDetail.uploadDate);
        setValue("uploadedBy", documentDetail.uploadedBy);
        setValue("activationStatus", documentDetail.activationStatus);
        setValue("visibilityStatus", documentDetail.visibilityStatus);

        const baseFilters: CrudFilter[] = [
          {
            field: "icNo",
            operator: "eq",
            value: user?.identificationNo,
          },
          {
            field: "externalDocumentId",
            operator: "eq",
            value: id,
          },
          {
            field: "type",
            operator: "eq",
            value: DocumentUploadType.EXTERNAL_DOCUMENT,
          },
        ];

        getPDFDocument([
          ...baseFilters,
          {
            field: "code",
            operator: "eq",
            value: "EXTERNAL_DOCUMENT_PDF",
          },
        ]);

        getEbookDocument([
          ...baseFilters,
          {
            field: "code",
            operator: "eq",
            value: "EXTERNAL_DOCUMENT_EBOOK",
          },
        ]);
      }
    },
  });

  const { fetch: submitDocument, isLoading: isSubmittingDocument } =
    useMutation<IApiResponse<number>>({
      url: submitDocumentUrl,
      method: id ? "put" : "post",
      onSuccess: (res) => {
        const documentId = res?.data?.data;
        const responseCode = res?.data?.code;

        if ([200, 201].includes(responseCode) && documentId) {
          if (!selectedPDF && !selectedEbook) {
            setIsSuccess(true);
            setTimeout(() => navigate("../daftar-panduan"), 2000);
          }

          if (selectedPDF) {
            uploadPdfFile({
              params: {
                name: selectedPDF.name,
                icNo: user?.identificationNo,
                externalDocumentId: documentId,
                type: DocumentUploadType.EXTERNAL_DOCUMENT,
                code: "EXTERNAL_DOCUMENT_PDF",
                status: 1,
              },
              file: selectedPDF,
            });
          }

          if (selectedEbook) {
            uploadEbookFile({
              params: {
                name: selectedEbook.name,
                externalDocumentId: documentId,
                icNo: user?.identificationNo,
                type: DocumentUploadType.EXTERNAL_DOCUMENT,
                code: "EXTERNAL_DOCUMENT_EBOOK",
                status: 1,
              },
              file: selectedEbook,
            });
          }
        }
      },
    });

  const handleSaveDocument = () => {
    const keysToSkip = ["urlPdf", "urlEbook"];
    const formValues = getValues();
    const filterPayload = filterEmptyValuesOnObject(formValues);
    const payload = omitKeysFromObject(filterPayload, keysToSkip);

    submitDocument(payload);
  };

  const onSubmit = () => setDialogOpen(true);

  if (isLoadingDocumentDetail) return <CustomSkeleton height={50} number={4} />;

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <Box className={classes.section} mb={2}>
        <Box className={classes.sectionBox} mb={2}>
          <Typography className="title" mb={1}>
            {isMyLanguage ? "Maklumat Dokumen" : "Document Information"}
          </Typography>
          <FormFieldRow
            label={<Label text="Tajuk Dokumen" required />}
            value={
              <TextFieldController control={control} name="name" required />
            }
          />
          <FormFieldRow
            label={<Label text="No Rujukan" required />}
            value={
              <TextFieldController
                control={control}
                name="referenceNumber"
                required
              />
            }
          />
          <FormFieldRow
            label={<Label text="Kategori Dokumen" required />}
            value={
              <SelectFieldController
                control={control}
                name="category"
                options={DocumentCategoryOptions}
                disabled={!!id}
                required
              />
            }
          />
          <FormFieldRow
            label={<Label text="Tarikh Kuatkuasa" required />}
            value={
              <DatePickerController
                control={control}
                formatValue="YYYY-MM-DD"
                name="effectiveDate"
                disabled={!!id}
                required
              />
            }
          />
          <FormFieldRow
            label={<Label text="Tahun Penerbitan" />}
            value={
              <TextFieldController
                inputMode="numeric"
                control={control}
                name="yearOfRelease"
                inputProps={{
                  maxLength: 4,
                }}
              />
            }
          />
          <FormFieldRow
            label={<Label text="Kategori Ringkasan" />}
            value={
              <TextFieldController
                control={control}
                name="summary"
                multiline
                rows={4}
              />
            }
          />
          <FormFieldRow
            label={<Label text="Tarikh Muatnaik" />}
            value={
              <DatePickerController
                control={control}
                formatValue="YYYY-MM-DD"
                disabled={!!id}
                name="uploadDate"
              />
            }
          />
          <FormFieldRow
            label={<Label text="Pegawai Muatnaik" />}
            value={
              <TextFieldController
                control={control}
                name="uploadedBy"
                disabled
              />
            }
          />
          <FormFieldRow
            label={<Label text="Status Kuatkuasa" required />}
            value={
              <SelectFieldController
                control={control}
                name="activationStatus"
                options={activationStatusOptions}
                required
              />
            }
          />
          <FormFieldRow
            label={<Label text="Status Paparan" required />}
            value={
              <SelectFieldController
                control={control}
                name="visibilityStatus"
                options={visibilityStatusOptions}
                required
              />
            }
          />
          <FormFieldRow
            align="flex-start"
            label={<Label text="Dokumen PDF" required />}
            value={
              <FileUploadController
                control={control}
                name="urlPdf"
                filePreview={PDFPreview}
                onFileSelect={(file) => setSelectedPDF(file)}
                accept=".pdf"
                allowedTypes={["application/pdf"]}
                disabled={!!id}
                required
              />
            }
          />
          <FormFieldRow
            align="flex-start"
            label={<Label text="Dokumen eBook" />}
            value={
              <FileUploadController
                control={control}
                name="urlEbook"
                filePreview={EbookPreview}
                onFileSelect={(file) => setSelectedEbook(file)}
                accept=".doc,.docx,.txt,.pdf"
                allowedTypes={[
                  "application/pdf",
                  "application/msword",
                  "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                  "text/plain",
                ]}
                disabled={!!id}
              />
            }
          />
        </Box>

        <ButtonPrimary
          type="submit"
          sx={{
            marginLeft: "auto",
            backgroundColor: "#0CA6A6 !important",
            display: "block",
          }}
          disabled={
            isSubmittingDocument || isUploadingEbook || isUploadingEbook
          }
        >
          {isSubmittingDocument ||
            isUploadingEbook ||
            (isUploadingEbook && <CircularProgress size={15} />)}
          {t("save")}
        </ButtonPrimary>
      </Box>

      <DialogConfirmation
        isMutating={isSubmittingDocument || isUploadingPdf || isUploadingEbook}
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        onAction={handleSaveDocument}
        onConfirmationText={
          isMyLanguage
            ? `Adakah Anda Pasti ${id ? "Kemaskini" : "Cipta"} Dokumen Ini ? ?`
            : `Are You Sure You ${id ? "Updated" : "Created"} This Document ?`
        }
        isSuccess={isSuccess}
        onSuccessText={
          isMyLanguage
            ? `Dokumen Anda Berjaya ${id ? "Dikemaskini" : "Dicipta"}`
            : `Your Document Was Successfully ${id ? "Updated" : "Created"}`
        }
      />
    </form>
  );
};

export default DaftarPanduanForm;
