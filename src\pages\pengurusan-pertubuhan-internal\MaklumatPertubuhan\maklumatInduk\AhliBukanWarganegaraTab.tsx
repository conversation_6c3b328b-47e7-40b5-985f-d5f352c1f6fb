import { Box, FormControl, Grid, IconButton, Typography } from "@mui/material";
import { t } from "i18next";
import { useEffect, useMemo, useState } from "react";
import { formatDate } from "../../../../helpers/utils";
import { useNavigate } from "react-router-dom";
import { GridColDef } from "@mui/x-data-grid";
import {
  OrganisationPositions,
  StatusPermohonan,
} from "../../../../helpers/enums";
import ButtonPrevious from "../../../../components/button/ButtonPrevious";
import { ButtonPrimary } from "../../../../components/button";
import { EyeIcon } from "../../../../components/icons";
import DataTable from "@/components/datatable";
import { useQuery } from "@/helpers";
import Input from "@/components/input/Input";

interface FormValues {
  userId?: number | null;
  negeri?: string | null;
  statusPertubuhan?: string | null;
  jenisPemegangJawatan?: string | null;
  carian?: string | null;
}

function AhliBukanWarganegaraTab() {
  const navigate = useNavigate();

  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [displaySenaraiAjk, setDisplaySenaraiAjk] = useState<any[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);
  const [page, setPage] = useState(0);

  const [formData, setFormData] = useState({
    statusPermohonan: "",
    carian: "",
  });

  const statusPermohonan = Object.entries(StatusPermohonan).map(
    ([key, value]) => ({
      value: key,
      label: value,
    })
  );

  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "30px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };

  const handleClearSearch = () => {
    setFormData({
      statusPermohonan: "",
      carian: "",
    });
  };

  const { refetch: fetchNonCitizenCommittee } = useQuery({
    url: `society/admin/societyNonCitizen/findAllByParam`,
    filters: [
      { field: "pageNo", operator: "eq", value: page + 1 },
      { field: "pageSize", operator: "eq", value: rowsPerPage },
      { field: "searchQuery", operator: "eq", value: formData.carian },
      {
        field: "applicationStatusCode",
        operator: "eq",
        value: formData.statusPermohonan,
      },
    ],
    autoFetch: false,
    onSuccess: (data) => {
      const list = data?.data?.data;
      console.log(list);
      setTotal(list.total);
      setDisplaySenaraiAjk(list.data);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    const newErrors: { [key in keyof FormValues]?: string } = {};

    let data = displaySenaraiAjk;

    // if every parameter is empty
    if (formData.carian == "" && formData.statusPermohonan == "") {
    } else {
      fetchNonCitizenCommittee();
    }
  };

  const columns: GridColDef[] = [
    {
      field: "name",
      headerName: t("applicationName"),
      align: "center",
    },
    {
      field: "identificationNo",
      headerName: t("idNumberPlaceholder"),
      align: "center",
    },
    {
      field: "designationCode",
      headerName: t("jawatan"),
      align: "center",
      renderCell: ({ row }: any) => {
        return t(
          `${
            OrganisationPositions.find(
              (item) => item?.value === Number(row?.designationCode)
            )?.label || "-"
          }`
        );
      },
    },
    {
      field: "societyName",
      headerName: t("organizationName"),
      align: "center",
      renderCell: (params) => <Box>{params.row.societyName ?? "-"}</Box>,
    },
    {
      field: "societyNo",
      headerName: t("OrganizationNo"),
      align: "center",
      renderCell: (params: any) => {
        const societyNo = params?.row?.societyNo;
        const societyApplicationNo = params?.row?.societyApplicationNo;

        return societyNo ?? societyApplicationNo ?? t("-");
      },
    },
    {
      field: "applicationStatusCode",
      headerName: t("statusPermohonan"),
      align: "center",
      renderCell: (params) => (
        <Box>
          {params.row.applicationStatusCode
            ? StatusPermohonan[params.row.applicationStatusCode] ?? "-"
            : "-"}
        </Box>
      ),
    },
    {
      field: "createdDate",
      headerName: t("tarikhCipta"),
      align: "center",
      renderCell: (params) => (
        <Box>{formatDate(params.row.createdDate) ?? "-"}</Box>
      ),
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "right",
      renderCell: (params: any) => {
        return (
          <>
            <IconButton
              sx={{ color: "black" }}
              onClick={() =>
                navigate("pertubuhan/ahli-bukan-warganegara", {
                  state: {
                    nonCitizenCommittee: params.row,
                  },
                })
              }
            >
              <EyeIcon />
            </IconButton>
          </>
        );
      },
    },
  ];

  useEffect(() => {
    // setDisplaySenaraiAjk(localData);
    // setIsloadingData(false);
    // setTotal(localData.length);
    fetchNonCitizenCommittee();
  }, [rowsPerPage, page]);

  return (
    <>
      <Box component="form" onSubmit={handleSubmit}>
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("senaraiPertubuhan")}
            </Typography>
            <Grid container spacing={2}>
              {/* organization category */}
              <Grid item xs={12} sm={12}>
                <FormControl fullWidth error={!!formErrors.statusPermohonan}>
                  <Input
                    value={formData.statusPermohonan}
                    size="small"
                    label={t("statusPermohonan")}
                    options={statusPermohonan}
                    onChange={(e) => {
                      setFormData((prevState) => ({
                        ...prevState,
                        statusPermohonan: e.target.value,
                      }));
                    }}
                    type="select"
                    error={!!formErrors.statusPermohonan}
                    helperText={formErrors.statusPermohonan}
                  />
                </FormControl>
              </Grid>

              {/* finding/carian */}
              <Grid item xs={12} sm={12}>
                <Input
                  value={formData.carian}
                  size="small"
                  label={t("Carian")}
                  onChange={(e) => {
                    setFormData((prevState) => ({
                      ...prevState,
                      carian: e.target.value,
                    }));
                  }}
                  error={!!formErrors.carian}
                  helperText={formErrors.carian}
                />
              </Grid>
            </Grid>
            <Grid container mt={3} spacing={2}>
              <Grid
                item
                xs={12}
                sx={{
                  mt: 2,
                  display: "flex",
                  // flexDirection: isMobile ? "column" : "row",
                  justifyContent: "flex-end",
                  gap: 1,
                }}
              >
                <ButtonPrevious
                  variant="outlined"
                  sx={{
                    bgcolor: "white",
                    "&:hover": { bgcolor: "white" },
                    // width: isMobile ? "100%" : "auto",
                  }}
                  onClick={handleClearSearch}
                >
                  {t("previous")}
                </ButtonPrevious>
                <ButtonPrimary
                  type="submit"
                  variant="contained"
                  sx={{
                    // width: isMobile ? "100%" : "auto",
                    boxShadow: "none",
                  }}
                >
                  {t("search")}
                </ButtonPrimary>
              </Grid>
            </Grid>
          </Box>
        </Box>
        {/* ============= */}
        <Box
          sx={{ backgroundColor: "white", p: 3, borderRadius: "15px", mt: 2 }}
        >
          <Box
            sx={{
              textAlign: "center",
              color: "#fff",
              borderRadius: "13px",
              backgroundColor: "var(--primary-color)",
              py: 2,
            }}
          >
            <Typography variant="h5" gutterBottom>
              {total}
            </Typography>
            <Typography variant="body1" sx={{ fontWeight: "500 !important" }}>
              {t("rekodBukanWarganegaraDijumpai")}
            </Typography>
          </Box>

          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mt: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("")}
            </Typography>

            {displaySenaraiAjk.length > 0 ? (
              <DataTable
                columns={columns as any[]}
                rows={displaySenaraiAjk} // Correctly slice data
                page={page + 1} // Page is 1-indexed for pagination
                rowsPerPage={rowsPerPage}
                totalCount={total}
                onPageChange={(newPage: number) => setPage(newPage - 1)}
                onPageSizeChange={(newRowsPerPage: number) =>
                  setRowsPerPage(newRowsPerPage)
                }
              />
            ) : (
              ""
            )}
          </Box>
        </Box>
      </Box>
      <style>
        {`
      .custom-header {
        white-space: normal;  /* Allow wrapping text */
        overflow: hidden;
        /* text-overflow: ellipsis; */
        line-height: 19.5px;
        font-weight: 500;
      }
      .custom-cell {
        white-space: normal;  /* Allow wrapping text */
        overflow: hidden;
        /* text-overflow: ellipsis; */
        line-height: 19.5px;
      }
    `}
      </style>
    </>
  );
}

export default AhliBukanWarganegaraTab;
