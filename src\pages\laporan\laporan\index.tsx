import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { CustomTabsPanel } from "@/components";
import DashboardEmbed from "@/components/dashboard/DashboardEmbed";

const kategoriStatistikKey = "jenisLaporan";
const jenisStatistikKey = "jenisLaporan";

// Sub-components
const Pertubuhan: React.FC = () => {
  return (
    <DashboardEmbed
      type="L"
      module="PERTUBUHAN"
      kategoriStatistikKey={kategoriStatistikKey}
      jenisStatistikKey={jenisStatistikKey}
    />
  );
};

const Pentadbiran: React.FC = () => {
  return (
    <DashboardEmbed
      type="L"
      module="PENTADBIRAN"
      kategoriStatistikKey={kategoriStatistikKey}
      jenisStatistikKey={jenisStatistikKey}
    />
  );
};

const Pemerkasaan: React.FC = () => {
  return (
    <DashboardEmbed
      type="L"
      module="PEMERKASAAN"
      kategoriStatistikKey={kategoriStatistikKey}
      jenisStatistikKey={jenisStatistikKey}
    />
  );
};

const Penguatkuasaan: React.FC = () => {
  return (
    <DashboardEmbed
      type="L"
      module="PENGUATKUASAAN"
      kategoriStatistikKey={kategoriStatistikKey}
      jenisStatistikKey={jenisStatistikKey}
    />
  );
};

const ApplikasiMobile: React.FC = () => {
  return (
    <DashboardEmbed
      type="L"
      module="APLIKASI_MOBIL"
      kategoriStatistikKey={kategoriStatistikKey}
      jenisStatistikKey={jenisStatistikKey}
    />
  );
};

const Laporan: React.FC = () => {
  const { t } = useTranslation();
  const location = useLocation();

  const [initialTab, setInitialTab] = useState(0);

  useEffect(() => {
    const tabMapping = {
      "pertubuhan": 0,
      "pentadbiran": 1,
      "pemerkasaan": 2,
      "penguatkuasaan": 3,
      "applikasi-mobile": 4
    };

    if (location.state?.tab && location.state.tab in tabMapping) {
      setInitialTab(tabMapping[location.state.tab as keyof typeof tabMapping]);
    }
  }, [location.state]);

  const tabItems = [
    {
      label: t("tabPertubuhan"),
      content: <Pertubuhan />,
    },
    {
      label: t("tabPentadbiran"),
      content: <Pentadbiran />,
    },
    {
      label: t("tabPemerkasaan"),
      content: <Pemerkasaan />,
    },
    {
      label: t("tabPenguatkuasaan"),
      content: <Penguatkuasaan />,
    },
    {
      label: t("tabApplikasiMobile"),
      content: <ApplikasiMobile />,
    },
  ];

  return <CustomTabsPanel tabs={tabItems} initialTab={initialTab} />;
};

export default Laporan;
