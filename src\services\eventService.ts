import { API_URL } from "@/api";
import {
  ApiResponse,
  BannerUploadResponse,
  IEvent,
  IEventOrganiser,
} from "@/types/event";
import { IEventAdmin } from "@/types/eventAdmin";
import {
  AttendanceUpdateRequest,
  AttendanceUpdateResponse,
  AttendeesCancelledName,
  AttendeesName,
  IAttendeeCancelRequest,
  IAttendeeRequest,
  IEventAttendee,
} from "@/types/eventAttendee";
import {
  EventFeedbackAnswer,
  EventFeedbackAnswer2,
  FeedbackScale,
  IEventFeedback,
  IEventFeedbackQuestion,
  IFeedbackQuestion,
} from "@/types/eventFeedbackQuestion";

const EVENT_BASE_ENDPOINT = `${API_URL}/society/event`;

interface CreateEventFeedbackRequest {
  feedbackQuestionIds: number[];
}

interface EventServiceState {
  data: IEvent | IEvent[] | null;
  loading: boolean;
  error: string | null;
}

class EventService {
  private state: EventServiceState = {
    data: null,
    loading: false,
    error: null,
  };

  // State getters
  getData = () => this.state.data;
  getLoading = () => this.state.loading;
  getError = () => this.state.error;

  private setState = (newState: Partial<EventServiceState>) => {
    this.state = { ...this.state, ...newState };
  };

  // Get single event by event number
  getEventByEventNo = async (eventNo: string): Promise<ApiResponse<IEvent>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(`${EVENT_BASE_ENDPOINT}/${eventNo}`, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      const result: ApiResponse<IEvent> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch event");
      }
      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch event";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  // Get all events
  getAllEvents = async (year: number): Promise<IEvent[]> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(`${EVENT_BASE_ENDPOINT}/getAll/${year}`, {
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
      });
      const result: ApiResponse<IEvent[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch events");
      }
      this.setState({ data: result.data });
      return result.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch events";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  // Get published events
  getPublishedEvents = async (year: number): Promise<ApiResponse<IEvent[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/getPublished/${year}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IEvent[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch published events");
      }
      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch published events";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  // Get organiser by ID
  getOrganiserById = async (id: number): Promise<IEventOrganiser> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/organiser/id/${id}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IEventOrganiser> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch organiser");
      }
      return result.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch organiser";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getAttendeesByEventNo = async (
    eventNo: string
  ): Promise<IEventAttendee[]> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/attendees/by-event-no/${eventNo}`,
        {
          headers: {
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IEventAttendee[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch attendees");
      }
      // Remove state update as this method handles attendees, not events
      return result.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch attendees";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getEventAdmin = async (
    identificationNo: string | undefined
  ): Promise<IEventAdmin> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/admin/${identificationNo}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IEventAdmin> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch attendees");
      }
      // Remove state update as this method handles attendees, not events
      return result.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch attendees";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getPastEvent = async (year: number): Promise<IEvent[]> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/getPastEvents/${year}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IEvent[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch attendees");
      }
      // Remove state update as this method handles attendees, not events
      return result.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch attendees";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getFeedbackQuestion = async (): Promise<IFeedbackQuestion[]> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/feedback-question/getAll`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IFeedbackQuestion[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch attendees");
      }
      // Remove state update as this method handles attendees, not events
      return result.data;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch attendees";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  createEvent = async (
    eventData: Partial<IEvent>
  ): Promise<ApiResponse<IEvent>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(`${EVENT_BASE_ENDPOINT}/create`, {
        method: "POST",
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(eventData),
      });

      const result: ApiResponse<IEvent> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to create event");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to create event";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  createEventFeedback = async (
    eventNo: string,
    feedbackQuestionIds: number[]
  ): Promise<ApiResponse<IEventFeedback[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/event-feedback-question/create/${eventNo}`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ feedbackQuestionIds }),
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to create event feedback");
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to create event feedback";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  updateEvent = async (
    eventNo: string,
    eventData: Partial<IEvent>
  ): Promise<ApiResponse<IEvent>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(`${EVENT_BASE_ENDPOINT}/update/${eventNo}`, {
        method: "POST",
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(eventData),
      });

      const result: ApiResponse<IEvent> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to update event");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to update event";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getAttendeesDetailByEventNo = async (
    eventNo: string | undefined
  ): Promise<ApiResponse<AttendeesName[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/attendees-list/by-event-no/${eventNo}`,
        {
          headers: {
            // portal: localStorage.getItem("portal") || "",
            // authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<AttendeesName[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch attendees details");
      }
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch attendees details";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getCanceledAttendeesDetailByEventNo = async (
    eventNo: string | undefined
  ): Promise<ApiResponse<AttendeesCancelledName[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/attendees-canceled-list/by-event-no/${eventNo}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<AttendeesCancelledName[]> =
        await response.json();
      if (!response.ok) {
        throw new Error(
          result.msg || "Failed to fetch canceled attendees details"
        );
      }
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch canceled attendees details";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getAttendeesDetailByIdentificationNo = async (
    identificationNo: string | undefined,
    eventNo: string | undefined
  ): Promise<ApiResponse<IEventAttendee>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/attendees/by-identification-no/${identificationNo}/event/${eventNo}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IEventAttendee> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch attendee details");
      }
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch attendee details";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  //not done yet
  getEventFeedbackQuestionByEventNo = async (
    eventNo: string | undefined
  ): Promise<ApiResponse<IEventFeedbackQuestion[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/get-feedback-question/${eventNo}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IEventFeedbackQuestion[]> =
        await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch feedback questions");
      }
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback questions";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getFeedbackScale = async (): Promise<ApiResponse<FeedbackScale[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/feedback-scale/getAll`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<FeedbackScale[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch feedback scales");
      }
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback scales";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  createEventFeedbackAnswer = async (
    attendanceNo: string,
    answers: EventFeedbackAnswer[]
  ): Promise<ApiResponse<EventFeedbackAnswer[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/event-feedback/create/${attendanceNo}`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(answers),
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to submit event feedback");
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to submit event feedback";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  generateCertificate = async (
    eventNo: string,
    identificationNo: string
  ): Promise<ApiResponse<any>> => {
    this.setState({ loading: true, error: null });
    const templateCode = "SIJILPENGLIBATAN";
    // console.log(`/generate/${templateCode}/${eventNo}/${identificationNo}`);

    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/generate/${templateCode}/${eventNo}/${identificationNo}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to generate certificate");
      }
      return response.json();

      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `certificate-${eventNo}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to generate certificate";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  publishEvent = async (
    eventNo: string | undefined
  ): Promise<ApiResponse<IEvent>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/publish/${eventNo}`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result: ApiResponse<IEvent> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to publish event");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to publish event";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  createEventAttendee = async (
    attendeeData: Partial<IAttendeeRequest>
  ): Promise<ApiResponse<IEventAttendee>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(`${EVENT_BASE_ENDPOINT}/attendees/create`, {
        method: "POST",
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(attendeeData),
      });

      const result: ApiResponse<IEventAttendee> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to create event attendee");
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to create event attendee";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  cancelEventAttendance = async (
    attendeeData: Partial<IAttendeeCancelRequest>
  ): Promise<ApiResponse<IEventAttendee>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(`${EVENT_BASE_ENDPOINT}/attendees/cancel`, {
        method: "POST",
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(attendeeData),
      });

      const result: ApiResponse<IEventAttendee> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to cancel event attendance");
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to cancel event attendance";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  updateEventAttendeePresent = async (
    attendeeData: Partial<AttendanceUpdateRequest>
  ): Promise<ApiResponse<AttendanceUpdateResponse>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/attendees/update-present`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify(attendeeData),
        }
      );

      const result: ApiResponse<AttendanceUpdateResponse> =
        await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to update attendee presence");
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to update attendee presence";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  uploadBanner = async (
    file: FormData
  ): Promise<ApiResponse<BannerUploadResponse>> => {
    this.setState({ loading: true, error: null });
    try {
      console.log("Starting banner upload for file:", file);

      const formData = new FormData();
      // formData.append('file', file);

      console.log("Sending request to upload banner");
      const response = await fetch(`${EVENT_BASE_ENDPOINT}/banner-upload`, {
        method: "POST",
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          // Don't set Content-Type, let the browser set it with the boundary
        },
        body: file,
      });

      console.log("Banner upload response status:", response.status);

      // Get the response text first for debugging
      // const responseText = await response;
      const result: ApiResponse<BannerUploadResponse> = await response.json();
      return result;
      // console.log("Banner upload response text:", responseText);

      // Try to parse the response as JSON
      // let result: ApiResponse<any>;
      // try {
      //   result = JSON.parse(responseText);
      // } catch (parseError) {
      //   console.error("Failed to parse response as JSON:", parseError);
      //   throw new Error(`Invalid response format: ${responseText}`);
      // }

      // if (!response.ok) {
      //   console.error("Banner upload failed with status:", response.status);
      //   throw new Error(result.msg || `Failed to upload banner image (${response.status})`);
      // }

      // console.log("Banner upload successful, result:", result);
      // return result;
    } catch (error) {
      console.error("Banner upload error:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to upload banner image";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  deleteEventFeedbackQuestion = async (
    eventNo: string
  ): Promise<ApiResponse<any>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/delete-feedback-question/${eventNo}`,
        {
          method: "POST",
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );

      const result = await response.json();

      if (!response.ok) {
        throw new Error(
          result.msg || "Failed to delete event feedback questions"
        );
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to delete event feedback questions";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  deleteEvent = async (eventNo: string): Promise<ApiResponse<any>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(`${EVENT_BASE_ENDPOINT}/delete/${eventNo}`, {
        method: "POST",
        headers: {
          portal: localStorage.getItem("portal") || "",
          authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
          "Content-Type": "application/json",
        },
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to delete event");
      }

      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to delete event";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getListAttendeesSubmittedFeedback = async (
    eventNo: string | undefined
  ): Promise<ApiResponse<IEventAttendee[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/attendees-submitted-feedback/by-event-no/${eventNo}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<IEventAttendee[]> = await response.json();
      if (!response.ok) {
        throw new Error(
          result.msg ||
            "Failed to fetch list of attendees who submitted feedback"
        );
      }
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch list of attendees who submitted feedback";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  getFeedbackAnswerByAttendanceNo = async (
    attendanceNo: string | undefined
  ): Promise<ApiResponse<EventFeedbackAnswer2[]>> => {
    this.setState({ loading: true, error: null });
    try {
      const response = await fetch(
        `${EVENT_BASE_ENDPOINT}/get-feedback-answer/by-attendance-no/${attendanceNo}`,
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            "Content-Type": "application/json",
          },
        }
      );
      const result: ApiResponse<EventFeedbackAnswer2[]> = await response.json();
      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch feedback answers");
      }
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to fetch feedback answers";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };
}

export const eventService = new EventService();
