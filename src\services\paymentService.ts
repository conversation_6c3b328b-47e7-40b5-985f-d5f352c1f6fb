import { API_URL } from "@/api";
import { IApiResponse } from "@/types/api";

// Payment Item Interface (matches API response)
export interface IPaymentItem {
  id: number;
  itemCode: string;
  itemName: string;
  description: string | null;
  price: number;
  category: string;
  docCode: string;
  paymentMethod: string;
  sortOrder: number;
  active: boolean;
}

export interface ICalculatePaymentItem {
  itemCode: string;
  quantity: number;
}

// Payment Items Response Interface
export interface IPaymentItemsResponse {
  items: IPaymentItem[];
}

// Payment Calculation Request Interface
export interface IPaymentCalculationRequest {
  items: ICalculatePaymentItem[];
}

// Payment Calculation Response Interface
export interface IPaymentCalculationResponse {
  items: Array<{
    itemCode: string;
    description?: string;
    price: number;
    quantity: number;
    total: number;
    docCode: string;
    paymentMethod: string;
    category: string;
  }>;
  totalAmount: number;
  signature: string;
}

// Process Payment Request Interface
export interface IProcessPaymentRequest {
  societyId?: number;
  branchId?: number;
  appealId?: number;
  propertyOfficerId?: number;
  publicOfficerId?: number;
  amendmentId?: number;
  searchInformationId?: number;
  branchAmendmentId?: number;

  amount: number;
  email?: string;
  bankCode?: string;
  signature: string;
}

// Process Payment Response Interface
export interface IProcessPaymentResponse {
  paymentId: number;
  paymentInfo: {
    urlhost: string | null;
    exchange_id: string | null;
    environment: string | null;
    url_request: string | null;
    namaPertubuhan: string | null;
    alamat: string | null;
    noPertubuhan: string | null;
    tarikhPermohonan: string | null;
    amaun: number;
    jenisBayaran: string | null;
    namaPemohon: string | null;
    email: string | null;
    noKp: string | null;
    kodOsolAmanah: string | null;
    jabatan: string | null;
    ptj: string | null;
    trans_id: string | null;
    payment_mode: string | null;
    kod_doc: string | null;
    kod_status_permohonan: string | null;
    noRujukan: string | null;
    kod_negeri: string | null;
    no_resit_ros: string | null;
    bank_CODE: string | null;
  };
}

// Payment Record Response Interface
export interface IPaymentRecordResponse {
  paymentType: string;
  branchId?: number;
  societyId?: number;
}

// Payment Service State Interface
interface PaymentServiceState {
  data: any;
  loading: boolean;
  error: string | null;
}

class PaymentService {
  private state: PaymentServiceState = {
    data: null,
    loading: false,
    error: null,
  };

  private baseEndpoint = `${API_URL}/payment`;

  // State getters
  getData = () => this.state.data;
  getLoading = () => this.state.loading;
  getError = () => this.state.error;

  private setState = (newState: Partial<PaymentServiceState>) => {
    this.state = { ...this.state, ...newState };
  };

  // Get authentication headers
  private getAuthHeaders = () => ({
    portal: localStorage.getItem("portal") || "",
    authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
    "Content-Type": "application/json",
  });

  /**
   * Get payment items
   * @param category - Optional category filter
   * @param docCode - Optional document code filter
   * @returns Promise<IPaymentItemsResponse>
   */
  getPaymentItems = async (
    category?: string,
    docCode?: string
  ): Promise<IApiResponse<IPaymentItemsResponse>> => {
    this.setState({ loading: true, error: null });

    try {
      const params = new URLSearchParams();
      if (category) params.append("category", category);
      if (docCode) params.append("docCode", docCode);

      const queryString = params.toString();
      const url = `${this.baseEndpoint}/items${queryString ? `?${queryString}` : ""}`;

      const response = await fetch(url, {
        method: "GET",
        headers: this.getAuthHeaders(),
      });

      const result: IApiResponse<IPaymentItemsResponse> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch payment items");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch payment items";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  /**
   * Calculate payment amount
   * @param request - Payment calculation request with items
   * @returns Promise<IPaymentCalculationResponse>
   */
  calculatePayment = async (
    request: IPaymentCalculationRequest
  ): Promise<IApiResponse<IPaymentCalculationResponse>> => {
    this.setState({ loading: true, error: null });

    try {
      const response = await fetch(`${this.baseEndpoint}/calculate`, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(request),
      });

      const result: IApiResponse<IPaymentCalculationResponse> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to calculate payment");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to calculate payment";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  /**
   * Process payment
   * @param request - Payment processing request
   * @returns Promise<IProcessPaymentResponse>
   */
  processPayment = async (
    request: IProcessPaymentRequest
  ): Promise<IApiResponse<IProcessPaymentResponse>> => {
    this.setState({ loading: true, error: null });

    try {
      const response = await fetch(`${this.baseEndpoint}/processPayment`, {
        method: "POST",
        headers: this.getAuthHeaders(),
        body: JSON.stringify(request),
      });

      const result: IApiResponse<IProcessPaymentResponse> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to process payment");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to process payment";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  /**
   * Get payment record by transaction ID
   * @param transactionId - Transaction ID to fetch payment record for
   * @returns Promise<IPaymentRecordResponse>
   */
  getPaymentRecord = async (
    transactionId: string
  ): Promise<IApiResponse<IPaymentRecordResponse>> => {
    this.setState({ loading: true, error: null });

    try {
      const params = new URLSearchParams();
      params.append("transactionId", transactionId);

      const url = `${this.baseEndpoint}/getPaymentRedirectData?${params.toString()}`;

      const response = await fetch(url, {
        method: "GET",
        headers: {
          portal: localStorage.getItem("portal") || "external",
          authorization: `Bearer ${localStorage.getItem("refine-auth") || ""}`,
        },
      });

      const result: IApiResponse<IPaymentRecordResponse> = await response.json();

      if (!response.ok) {
        throw new Error(result.msg || "Failed to fetch payment record");
      }

      this.setState({ data: result.data });
      return result;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Failed to fetch payment record";
      this.setState({ error: errorMessage });
      throw new Error(errorMessage);
    } finally {
      this.setState({ loading: false });
    }
  };

  /**
   * Reset service state
   */
  resetState = () => {
    this.setState({
      data: null,
      loading: false,
      error: null,
    });
  };
}

// Export singleton instance
export const paymentService = new PaymentService();
export default paymentService;
