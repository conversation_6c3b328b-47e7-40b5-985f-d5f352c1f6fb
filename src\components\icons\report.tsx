import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  sx?: React.CSSProperties;
}

export const ReportIcon: React.FC<IconProps> = React.forwardRef<
  SVGSVGElement,
  IconProps
>(({ sx, color = "inherit", ...props }, ref) => {
  return (
    <svg
      ref={ref}
      width="28"
      height="22"
      viewBox="0 0 28 22"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      style={{ color, ...sx }}
      {...props}
    >
      <path d="M15.4998 12.5005C15.3515 12.5005 15.2066 12.4565 15.0832 12.3741C14.9599 12.2917 14.8638 12.1746 14.8071 12.0375C14.7503 11.9005 14.7355 11.7497 14.7644 11.6043C14.7933 11.4588 14.8647 11.3252 14.9696 11.2203L20.2196 5.9703C20.361 5.83368 20.5505 5.75809 20.7471 5.75979C20.9438 5.7615 21.1319 5.84038 21.271 5.97944C21.41 6.11849 21.4889 6.3066 21.4906 6.50325C21.4923 6.6999 21.4167 6.88935 21.2801 7.0308L16.0301 12.2808C15.8895 12.4215 15.6987 12.5005 15.4998 12.5005ZM7.24984 13.2505C7.10153 13.2505 6.95655 13.2065 6.83325 13.1241C6.70994 13.0417 6.61383 12.9246 6.55708 12.7875C6.50033 12.6505 6.48548 12.4997 6.5144 12.3543C6.54333 12.2088 6.61473 12.0752 6.71959 11.9703L11.2196 7.4703C11.361 7.33368 11.5505 7.25809 11.7471 7.25979C11.9438 7.2615 12.1319 7.34038 12.271 7.47944C12.41 7.61849 12.4889 7.8066 12.4906 8.00325C12.4923 8.1999 12.4167 8.38935 12.2801 8.5308L7.78009 13.0308C7.63947 13.1715 7.44874 13.2505 7.24984 13.2505Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
        />

      <path d="M15.5005 12.5005C15.3017 12.5005 15.1109 12.4215 14.9703 12.2808L11.2203 8.5308C11.0837 8.38935 11.0081 8.1999 11.0098 8.00325C11.0115 7.8066 11.0904 7.61849 11.2294 7.47944C11.3685 7.34038 11.5566 7.2615 11.7532 7.25979C11.9499 7.25809 12.1393 7.33368 12.2808 7.4703L16.0308 11.2203C16.1357 11.3252 16.2071 11.4588 16.236 11.6043C16.2649 11.7497 16.2501 11.9005 16.1933 12.0375C16.1366 12.1746 16.0404 12.2917 15.9171 12.3741C15.7938 12.4565 15.6489 12.5005 15.5005 12.5005Z"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
        />
    </svg>
  );
});
