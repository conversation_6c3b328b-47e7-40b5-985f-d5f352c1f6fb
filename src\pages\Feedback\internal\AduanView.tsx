import { Formik, type <PERSON><PERSON><PERSON><PERSON><PERSON> } from "formik";
import { useLocation } from "react-router-dom";

import { type FeedbackAduanPublicCreateRequestBody } from "@/components/form/feedback/aduan/PublicCreate";
import { FormFeedbackAduanInternalCreate } from "@/components/form/feedback/aduan/InternalCreate";

export const FeedbackInternalAduanView = <
  ReqBody extends FeedbackAduanPublicCreateRequestBody = FeedbackAduanPublicCreateRequestBody
>() => {
  const location = useLocation();

  const initialValue = {
    ...location.state?.prevData
  } as ReqBody

  const handleSubmit = async (payload: ReqBody, { setSubmitting }: FormikHelpers<ReqBody>) => {
    const { promise, resolve } = Promise.withResolvers();
    setSubmitting(true);
    setTimeout(() => {
      resolve(payload);
      setSubmitting(false);
    }, 3000);
    await promise;
    // try {
    // } finally {
    //   setSubmitting(false);
    // }
  }

  return (
    <Formik<ReqBody>
      initialValues={{ ...location.state?.prevData } as ReqBody}
      onSubmit={handleSubmit}
    >
      <FormFeedbackAduanInternalCreate initialValue={initialValue} viewOnly />
    </Formik>
  )
}
