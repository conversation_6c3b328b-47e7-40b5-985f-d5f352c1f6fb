import axios from "axios";
import useMutation from "./useMutation";
import { useState } from "react";
import { useNotification } from "@refinedev/core";
import { BaseRecord, CreateResponse } from "@refinedev/core";
import { API_URL } from "@/api";
import { convertFileToBinary } from "../utils";
import { DOCUMENT_MAX_FILE_SIZE } from "../constants";
import { useTranslation } from "react-i18next";

export enum DocumentUploadType {
  SOCIETY = 1,
  BRANCH = 2,
  APPEAL = 3,
  MEETING = 4,
  AMENDMENT = 5,
  STATEMENT = 6,
  LIQUIDATION = 7,
  CITIZEN_COMMITTEE = 8,
  NON_CITIZEN_COMMITTEE = 9,
  FEEDBACK = 10,
  SUPPORTING_DOCUMENT = 11,
  PROFILE_PICTURE = 12,
  TAKWIM_BANNER = 13,
  EVENT_CERT = 14,
  FINANCIAL_STATEMENT = 15,
  ACTIVITY_REPORT_STATEMENT = 16,
  SOCIETY_CANCELLATION_REVERT = 17,
  NOTICE = 18,
  TRAINING_POSTER = 19,
  TRAINING_MATERIAL = 20,
  EXTERNAL_DOCUMENT = 21,
}

interface params {
  type: DocumentUploadType;
  societyId?: number | null;
  societyNo?: string | number | null;
  branchId?: number | null;
  branchNo?: string | number | null;
  meetingId?: number | null;
  societyCommitteeId?: number | null;
  societyNonCitizenCommitteeId?: string | null;
  appealId?: number | null;
  statementId?: number | null;
  amendmentId?: number | null;
  liquidationId?: number | null;
  trainingId?: number | null;
  trainingMaterialId?: number | null;
  icNo?: string | null;
  name?: string | null;
  note?: string | null;
  url?: string | null;
  doc?: string | null;
  status?: number;
  code?: string | null;
  externalDocumentId?: number | null;
}

interface UploadParams {
  params: params;
  file: File;
}

interface UseUploadPresignedUrl {
  onSuccessUpload?: (data: CreateResponse<BaseRecord>) => void;
  onUploadProgress?: (progress: number, name: string) => void;
  withAuthHeaders?: boolean;

  /**
   * @default true
   */
  showSuccessNotification?: boolean;
}

export const useUploadPresignedUrl = ({
  onSuccessUpload,
  onUploadProgress,
  withAuthHeaders = true,
  showSuccessNotification = true,
}: UseUploadPresignedUrl = {}) => {
  const { open } = useNotification();
  const { t } = useTranslation();
  const [progress, setProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [returnId, setReturnId] = useState(null);
  const [returnURL, setReturnURL] = useState("");
  const notifyError = (description: string) =>
    open?.({
      type: "error",
      message: t("error"),
      description,
    });

  const { fetch: registerFileInDatabase } = useMutation({
    url: "society/document/registerFileInDb",
    method: "put",
    showSuccessNotification,
    withAuthHeaders,
    onSuccess: (data) => {
      onSuccessUpload?.(data);
      setReturnURL(data?.data?.data?.url);
    },
  });

  const formatFileSize = (bytes: number): string =>
    `${(bytes / 1024 / 1024).toFixed(1)}`;

  const upload = async ({ params, file }: UploadParams) => {
    if (!params?.type)
      return notifyError("Type is required for document upload.");
    if (!file || !(file instanceof File))
      return notifyError("Invalid file provided for upload.");
    if (file.size === 0) return notifyError("File is empty.");
    if (file.size > DOCUMENT_MAX_FILE_SIZE)
      return notifyError(
        t("validation.documentSize", { maxSize: formatFileSize(DOCUMENT_MAX_FILE_SIZE) })
      );

    try {
      setIsLoading(true);

      const presignedUrlResponse = await axios.post(
        `${API_URL}/society/document/uploadPresignedUrl`,
        {
          ...params,
          name: file.name,
          note: params.note || "",
          url: params.url || "",
          doc: params.doc || "",
          status: params.status || 1,
        },
        {
          headers: {
            portal: localStorage.getItem("portal") || "",
            authorization: `Bearer ${
              localStorage.getItem("refine-auth") || ""
            }`,
          },
        }
      );

      const presignedData = presignedUrlResponse.data?.data;
      if (!presignedData) return notifyError("Failed to get presigned URL.");

      const { presignedUrl, document } = presignedData;
      const binaryFile = await convertFileToBinary(file);

      const uploadResponse = await axios.put(presignedUrl, binaryFile, {
        headers: {
          "Content-Type": file.type,
        },
        onUploadProgress: (event) => {
          if (event.total) {
            const progressValue = Math.round(
              (event.loaded / event.total) * 100
            );
            setProgress(progressValue);
            onUploadProgress?.(progressValue, file.name);
          }
        },
      });

      if (uploadResponse.status !== 200) return notifyError("Upload failed.");
      document.type = params.type;
      setReturnId(presignedData.document.id);
      registerFileInDatabase(document);
    } catch (error) {
      console.error("Upload failed:", error);
      notifyError("An unexpected error occurred during the upload process.");
    } finally {
      setIsLoading(false);
    }
  };

  return {
    upload,
    progress,
    isLoading,
    returnId,
    returnURL,
  };
};
