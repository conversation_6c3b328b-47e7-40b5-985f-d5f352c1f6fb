/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from "react";
import { Box, Typography, Grid } from "@mui/material";
import { useTranslation } from "react-i18next";
import { useLocation, useNavigate, useParams } from "react-router-dom";
import ButtonPrimary from "@/components/button/ButtonPrimary";
import { ButtonOutline } from "@/components/button";
import { useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "@/api";
import Input from "@/components/input/Input";
import { Controller, useForm } from "react-hook-form";
import useMutation from "@/helpers/hooks/useMutation";
import {
  CitizenshipStatus,
  IdTypes,
  ListGelaran,
  ListGender,
  MALAYSIA,
  OrganisationPositions,
} from "@/helpers/enums";
import { getLocalStorage } from "@/helpers";
import dayjs from "dayjs";
import { useSelector } from "react-redux";
import { getUserPermission } from "@/redux/userReducer";

interface ICommitte {
  jobCode: string;
  societyId: number;
  societyNo: string;
  titleCode: string;
  name: string;
  gender: string;
  nationalityStatus: string;
  identificationType: string;
  identificationNo: string;
  dateOfBirth: string; // Format: YYYY-MM-DD
  placeOfBirth: string;
  designationCode: string;
  otherDesignationCode: string;
  employerAddressStatus: string;
  employerName: string;
  employerAddress: string;
  employerPostcode: string;
  employerCountryCode: string;
  employerStateCode: string;
  employerCity: string;
  employerDistrict: string;
  residentialAddress: string;
  residentialPostcode: string;
  residentialAddressStatus: string;
  residentialCountryCode: string;
  residentialStateCode: string;
  residentialDistrictCode: string;
  residentialCity: string;
  email: string;
  telephoneNumber: string;
  phoneNumber: string;
  noTelP: string;
  status: number;
  applicationStatusCode: string;
  pegHarta: string;
  tarikhTukarSu: string; // Format: YYYY-MM-DD
  otherPosition: string;
  batalFlat: boolean;
  blacklistNotice: boolean;
  benarAjk: boolean;
  id: string;
}

export const CreateAjk: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const params = new URLSearchParams(window.location.search);
  const encodedId = params.get("id");
  const [userICCorrect, setUserICCorrect] = useState(true);
  const [userNameMatchIC, setUserNameMatchIC] = useState(true);
  const { id } = useParams();

  const handleSenaraiAjkResets = () => {
    const identificationType = getValues("identificationType");
    reset();
    reset({
      identificationNo: "",
      name: "",
      placeOfBirth: "",
      residentialAddress: "",
      residentialPostcode: "",
      residentialAddressStatus: "",
      residentialCountryCode: "",
      residentialStateCode: "",
      residentialDistrictCode: "",
      residentialCity: "",
      email: "",
      telephoneNumber: "",
      phoneNumber: "",
      noTelP: "",
      employerAddressStatus: "",
      employerName: "",
      employerAddress: "",
      employerCountryCode: "",
      employerStateCode: "",
      employerCity: "",
      employerDistrict: "",
    });
    setValue("identificationType", identificationType);
    // navigate(-1);
  };

  const { data: addressList, isLoading: isAddressLoading } = useCustom({
    url: `${API_URL}/society/admin/address/list`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });
  const addressData = addressList?.data?.data || [];
  const occupationList = getLocalStorage("occupation_list", []);

  const defaultValue = {
    jobCode: "J1234",
    societyId: id,
    societyNo: "S001",
    titleCode: "TC012",
    name: "John Doe",
    gender: "M",
    nationalityStatus: "1",
    identificationType: "Passport",
    identificationNo: "980316145713",
    dateOfBirth: "1985-07-15",
    placeOfBirth: "New York",
    designationCode: "DC001",
    otherDesignationCode: "ODC001",
    employerAddressStatus: "Active",
    employerName: "Tech Corp",
    employerAddress: "123 Tech Street",
    employerCountryCode: "US",
    employerStateCode: "NY",
    employerCity: "New York",
    employerDistrict: "Manhattan",
    residentialAddress: "456 Elm Street",
    residentialPostcode: "10002",
    residentialAddressStatus: "Active",
    residentialCountryCode: "US",
    residentialStateCode: "NY",
    residentialDistrictCode: "MD001",
    residentialCity: "New York",
    email: "<EMAIL>",
    telephoneNumber: "+1234567890",
    phoneNumber: "+1987654321",
    noTelP: "+11234567890",
    status: "001",
    applicationStatusCode: "APPROVED",
    pegHarta: "PH123",
    tarikhTukarSu: "2023-08-01",
    otherPosition: "Board Member",
    batalFlat: false,
    blacklistNotice: false,
    benarAjk: true,
  };

  const form = useForm<ICommitte | any>();
  const [ajkId, setAjkId] = useState<number>();

  const {
    register,
    formState: { errors },
    handleSubmit,
    control,
    watch,
    setValue,
    getValues,
    reset,
  } = form;

  const mutate = useMutation({
    url: "society/committee/draft/create",
    onSuccess: (response: any) => {
      const ajkId = response.data.data.id;
      setAjkId(ajkId);
      navigate(-1);
    },
  });

  const { mutate: updateAJK, isLoading: isUpdateAJK } = useCustomMutation();

  const location = useLocation();
  const ajk: ICommitte = location?.state?.ajk;
  const meetingData = location?.state?.meetingData;
  const view = location?.state?.view;

  useEffect(() => {
    if (ajk) {
      setAjkId(parseInt(ajk.id));
      Object.entries(ajk).forEach(([key, value]) => {
        setValue(key, value);
      });
    }
  }, []);

  const onSubmit = (data: any) => {
    if (ajkId) {
      updateAJK(
        {
          url: `${API_URL}/society/committee/draft/update`,
          method: "put",
          values: {
            ...data,
            nationalityStatus: 1,
            identificationType: "1",
            updateMeetingId: meetingData.id,
            appointedDate: meetingData.meetingDate,
          },
          config: {
            headers: {
              portal: localStorage.getItem("portal"),
              authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
            },
          },
          successNotification: (data) => {
            if (data?.data?.status === "SUCCESS") {
              return {
                message: data?.data?.msg,
                type: "success",
              };
            } else {
              return {
                message: data?.data?.msg,
                type: "error",
              };
            }
          },
          errorNotification: (data) => {
            return {
              message: data?.response?.data?.msg,
              type: "error",
            };
          },
        },
        {
          onError(error, variables, context) {
            console.log(error);
          },
          onSuccess: () => {
            navigate(-1);
          },
        }
      );
    } else {
      mutate.fetch({
        ...defaultValue,
        ...data,
        nationalityStatus: 1,
        identificationType: "1",
      });
    }
  };
  const isManager = useSelector(getUserPermission);
  let identificationNoHelperText: string | undefined = undefined;
  if (
    watch("identificationType") === "1" ||
    watch("identificationType") === "4"
  ) {
    if (typeof errors.identificationNo?.message === "string") {
      identificationNoHelperText = errors.identificationNo.message;
    } else if (watch("identificationNo")?.length === 12 && !userICCorrect) {
      identificationNoHelperText = t("IcDoesNotExist");
    }
  } else if (typeof errors.identificationNo?.message === "string") {
    identificationNoHelperText = errors.identificationNo.message;
  }

  let nameHelperText: string | undefined = undefined;
  if (
    watch("identificationType") === "1" ||
    watch("identificationType") === "4"
  ) {
    if (typeof errors.name?.message === "string") {
      nameHelperText = errors.name.message;
    } else if (
      watch("identificationNo")?.length === 12 &&
      watch("name")?.trim() !== undefined &&
      !userNameMatchIC
    ) {
      nameHelperText = t("invalidName");
    }
  } else if (typeof errors.name?.message === "string") {
    nameHelperText = errors.name.message;
  }

  useEffect(() => {
    if (Number(getValues("identificationType")) === 1) {
      setValue("nationalityStatus", 1);
    } else {
      setValue("nationalityStatus", 2);
    }
  }, [watch("identificationType")]);

  return (
    <Box sx={{ display: "flex", gap: 3 }}>
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 2 },
          border: "1px solid #D9D9D9",
          backgroundColor: "#fff",
          borderRadius: "14px",
          width: "100%",
        }}
      >
        <form
          style={{
            // border: "1px solid #D9D9D9",
            // backgroundColor: "#FCFCFC",
            borderRadius: "14px",
            width: "100%",
            gap: 16,
            display: "grid",
          }}
          noValidate
          onSubmit={handleSubmit(onSubmit)}
        >
          <Box
            sx={{
              background: "white",
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 4,
              py: 3,
            }}
          >
            <Typography
              variant="h6"
              component="h2"
              sx={{
                fontWeight: "bold",
                fontSize: 14,
                color: "var(--primary-color)",
                mb: 2,
              }}
            >
              {t("positionInfo")}
            </Typography>

            <Grid container>
              <Controller
                name="designationCode"
                rules={{
                  required: t("requiredValidation"),
                }}
                defaultValue={parseInt(getValues("designationCode"))}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view || isManager}
                      required
                      {...field}
                      type="select"
                      label={t("position")}
                      error={!!errors.designationCode?.message}
                      value={parseInt(getValues("designationCode"))}
                      options={OrganisationPositions.map((position) => ({
                        ...position,
                        label: t(position.label),
                      }))}
                    />
                  );
                }}
              />
            </Grid>
          </Box>
          <Box
            sx={{
              background: "white",
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 4,
              py: 3,
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("personalInfo")}
              </Typography>
            </Box>
            <Grid item sm={12}>
              <Controller
                name="identificationType"
                control={control}
                rules={{
                  required: t("requiredValidation"),
                }}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      required
                      {...field}
                      error={!!error}
                      helperText={error?.message}
                      label={t("idType")}
                      type="select"
                      options={IdTypes.map((item) => ({
                        ...item,
                        label: t(item.label),
                      }))}
                      value={watch("identificationType")}
                      onChange={(e) => {
                        const inputType = getValues("identificationType");
                        let value = e.target.value;
                        if (
                          (inputType !== "1" && value === "1") ||
                          (inputType !== "4" && value === "4")
                        ) {
                          setValue("identificationNo", "");
                        }

                        setValue(field.name, value);
                      }}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                name="identificationNo"
                rules={{
                  required: t("fieldRequired"),
                  validate: (value) => {
                    const type = getValues("identificationType");
                    if ((type === "1" || type === "4") && value.length !== 12) {
                      return t("fieldRequired");
                    }
                    return true;
                  },
                }}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      label={t("idNumber")}
                      error={!!identificationNoHelperText}
                      helperText={identificationNoHelperText}
                      inputProps={
                        getValues("identificationType") === "1" ||
                        getValues("identificationType") === "4"
                          ? {
                              inputMode: "numeric",
                              pattern: "[0-9]*",
                              maxLength: 12,
                              minLength: 12,
                            }
                          : undefined
                      }
                      onChange={(e) => {
                        const inputType = getValues("identificationType");
                        let value = e.target.value;

                        if (inputType === "1" || inputType === "4") {
                          value = value.replace(/\D/g, "").slice(0, 12);
                        }

                        setValue(field.name, value);
                      }}
                    />
                  );
                }}
              />
              <Controller
                name="titleCode"
                rules={{
                  required: t("requiredValidation"),
                }}
                control={control}
                defaultValue={getValues("titleCode")}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!error}
                      helperText={error?.message}
                      label={t("title")}
                      type="select"
                      options={ListGelaran}
                      value={getValues("titleCode")}
                    />
                  );
                }}
              />
              <Controller
                name="name"
                rules={{
                  required: t("requiredValidation"),
                }}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!nameHelperText}
                      helperText={nameHelperText}
                      label={t("fullName")}
                    />
                  );
                }}
              />
              <Controller
                name="gender"
                control={control}
                rules={{
                  required: t("requiredValidation"),
                }}
                defaultValue={getValues("gender")}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    disabled={view}
                    {...field}
                    required
                    label={t("gender")}
                    type="select"
                    error={!!error}
                    helperText={error?.message}
                    options={ListGender.map((gender) => ({
                      ...gender,
                      label: t(gender.label),
                    }))}
                  />
                )}
              />
              <Controller
                name="nationalityStatus"
                control={control}
                rules={{
                  required: t("requiredValidation"),
                }}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      required
                      {...field}
                      value={Number(watch("nationalityStatus"))}
                      label={t("citizenship")}
                      type="select"
                      error={!!error}
                      helperText={error?.message}
                      options={CitizenshipStatus.map((item) => ({
                        ...item,
                        label: t(item.label),
                      }))}
                      disabled
                    />
                  );
                }}
              />
              <Controller
                name="dateOfBirth"
                rules={{
                  required: t("requiredValidation"),
                }}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      error={!!error}
                      helperText={error?.message}
                      {...field}
                      label={t("dateOfBirth")}
                      type="date"
                      onChange={(newValue) =>
                        setValue("dateOfBirth", newValue.target.value)
                      }
                      value={
                        getValues("dateOfBirth")
                          ? dayjs(getValues("dateOfBirth")).format("DD-MM-YYYY")
                          : ""
                      }
                    />
                  );
                }}
              />
              <Controller
                name="placeOfBirth"
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      label={t("placeOfBirth")}
                    />
                  );
                }}
              />

              <Controller
                name="residentialAddress"
                rules={{
                  required: t("requiredValidation"),
                }}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!error}
                      helperText={error?.message}
                      label={t("residentialAddress")}
                    />
                  );
                }}
              />

              <Controller
                name="residentialStateCode"
                control={control}
                rules={{
                  required: t("requiredValidation"),
                }}
                defaultValue={getValues("residentialStateCode")}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    disabled={view}
                    required
                    {...field}
                    label={t("state")}
                    type="select"
                    error={!!error}
                    helperText={error?.message}
                    onChange={(e) =>
                      setValue("residentialStateCode", e.target.value)
                    }
                    value={parseInt(getValues("residentialStateCode"))}
                    options={addressData
                      .filter((item: any) => item.pid === MALAYSIA)
                      .map((item: any) => ({
                        label: item.name,
                        value: item.id,
                      }))}
                  />
                )}
              />

              <Controller
                name="residentialDistrictCode"
                control={control}
                defaultValue={getValues("residentialDistrictCode")}
                rules={{
                  required: t("requiredValidation"),
                }}
                render={({ field, fieldState: { error } }) => (
                  <Input
                    disabled={view}
                    {...field}
                    required
                    label={t("district")}
                    error={!!error}
                    helperText={error?.message}
                    type="select"
                    value={parseInt(getValues("residentialDistrictCode"))}
                    onChange={(e) =>
                      setValue("residentialDistrictCode", e.target.value)
                    }
                    options={addressData
                      .filter(
                        (item: any) =>
                          item.pid === parseInt(watch("residentialStateCode"))
                      )
                      .map((item: any) => ({
                        label: item.name,
                        value: item.id,
                      }))}
                  />
                )}
              />

              <Controller
                name="residentialCity"
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return <Input {...field} disabled={view} label={t("city")} />;
                }}
              />

              <Controller
                name="residentialPostcode"
                rules={{
                  required: t("idNumberRequired"),
                  maxLength: {
                    value: 5,
                    message: t("postcodeHelper"),
                  },
                  minLength: {
                    value: 5,
                    message: t("postcodeHelper"),
                  },
                  pattern: {
                    value: /^\d+$/,
                    message: t("numbersOnly"),
                  },
                }}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      onChange={(e) => {
                        if (/^\d{0,5}$/.test(e.target.value)) {
                          field?.onChange(e.target.value);
                        }
                      }}
                      error={!!error}
                      helperText={error?.message}
                      label={t("postcode")}
                    />
                  );
                }}
              />

              <Controller
                name="email"
                rules={{
                  required: t("requiredValidation"),
                }}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      required
                      {...field}
                      error={!!error}
                      helperText={error?.message}
                      label={t("email")}
                      type="email"
                    />
                  );
                }}
              />

              <Controller
                name="phoneNumber"
                rules={{
                  required: t("requiredValidation"),
                }}
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      required
                      error={!!error}
                      helperText={error?.message}
                      label={t("phoneNumber")}
                      inputMode={"numeric"}
                      onInput={(e) => {
                        const input = e.target as HTMLInputElement;
                        input.value = input.value
                          .replace(/\D/g, "")
                          .slice(0, 12);
                      }}
                    />
                  );
                }}
              />

              <Controller
                name="telephoneNumber"
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      error={!!errors.telephoneNumber?.message}
                      label={t("homeNumber")}
                      inputMode={"numeric"}
                      onInput={(e) => {
                        const input = e.target as HTMLInputElement;
                        input.value = input.value
                          .replace(/\D/g, "")
                          .slice(0, 12);
                      }}
                    />
                  );
                }}
              />
            </Grid>
          </Box>
          <Box
            sx={{
              background: "white",
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 4,
              py: 3,
            }}
          >
            <Controller
              name="jobCode"
              defaultValue={getValues("jobCode")}
              control={control}
              rules={{
                required: t("requiredValidation"),
              }}
              render={({ field, fieldState: { error } }) => {
                return (
                  <Input
                    disabled={view}
                    required
                    {...field}
                    error={!!error}
                    helperText={error?.message}
                    label={t("occupation")}
                    type="select"
                    value={getValues("jobCode")}
                    options={occupationList}
                  />
                );
              }}
            />
            <Box>
              <span
                style={{
                  color: "red",
                  fontWeight: "500",
                  alignContent: "center",
                  fontSize: "12px",
                }}
              >
                {t("peringatan")}:
              </span>
              <span
                style={{
                  color: "#666666",
                  fontWeight: "500",
                  alignContent: "center",
                  fontSize: "12px",
                }}
              >
                {t("maklumatMajikanWarning").replace(/\*/g, " ")}
              </span>
            </Box>
          </Box>
          <Box
            sx={{
              background: "white",
              border: "1px solid rgba(0, 0, 0, 0.12)",
              borderRadius: "14px",
              p: 4,
              py: 3,
            }}
          >
            <Box sx={{ mb: 2 }}>
              <Typography
                variant="h6"
                component="h2"
                sx={{
                  fontWeight: "bold",
                  fontSize: 14,
                  color: "var(--primary-color)",
                }}
              >
                {t("jobInformation")}
              </Typography>
            </Box>

            <Grid item sm={12}>
              <Controller
                name="employerName"
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      {...field}
                      error={!!errors.employerName?.message}
                      label={t("employerName")}
                      disabled={
                        watch("jobCode") == "Pesara" ||
                        watch("jobCode") == "Tidak Bekerja" ||
                        view
                      }
                    />
                  );
                }}
              />
              <Controller
                name="employerAddressStatus"
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      error={!!errors.employerAddressStatus?.message}
                      label={t("workAddress")}
                      type={"select"}
                      options={[
                        {
                          value: "dalam",
                          label: "Dalam Negara",
                        },
                        {
                          value: "luar",
                          label: "Luar Negara",
                        },
                      ]}
                    />
                  );
                }}
              />
              {watch("employerAddressStatus") !== "dalam" ? (
                <Controller
                  name="employerCountryCode"
                  control={control}
                  render={({ field, fieldState: { error } }) => {
                    return (
                      <Input
                        {...field}
                        error={!!errors.employerCountryCode?.message}
                        label={t("country")}
                        type="select"
                        value={parseInt(getValues("employerCountryCode"))}
                        disabled={
                          watch("jobCode") == "Pesara" ||
                          watch("jobCode") == "Tidak Bekerja" ||
                          view
                        }
                        options={addressData
                          .filter((item: any) => item.pid === 0)
                          .map((item: any) => ({
                            label:
                              item.name.charAt(0) +
                              item.name.slice(1).toLowerCase(),
                            value: item.id,
                          }))}
                      />
                    );
                  }}
                />
              ) : null}
              <Controller
                name="employerAddress"
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      {...field}
                      error={!!errors.employerAddress?.message}
                      multiline
                      rows={4}
                      disabled={
                        watch("jobCode") == "Pesara" ||
                        watch("jobCode") == "Tidak Bekerja" ||
                        view
                      }
                    />
                  );
                }}
              />
              {watch("employerAddressStatus") === "dalam" && (
                <>
                  <Controller
                    name="employerStateCode"
                    control={control}
                    render={({ field, fieldState: { error } }) => {
                      return (
                        <Input
                          {...field}
                          disabled={
                            watch("jobCode") == "Pesara" ||
                            watch("jobCode") == "Tidak Bekerja" ||
                            view
                          }
                          error={!!errors.employerStateCode?.message}
                          label={t("state")}
                          type="select"
                          fullWidth
                          multiline
                          rows={3}
                          options={addressData
                            ?.filter((item: any) => item.pid == MALAYSIA)
                            .map((item: any) => ({
                              label: item.name,
                              value: "" + item.id,
                            }))}
                        />
                      );
                    }}
                  />

                  <Controller
                    name="employerDistrict"
                    control={control}
                    render={({ field, fieldState: { error } }) => {
                      return (
                        <Input
                          {...field}
                          error={!!errors.employerDistrict?.message}
                          label={t("district")}
                          disabled={
                            watch("jobCode") == "Pesara" ||
                            watch("jobCode") == "Tidak Bekerja" ||
                            view
                          }
                          type="select"
                          fullWidth
                          multiline
                          rows={3}
                          options={addressData
                            ?.filter(
                              (item: any) =>
                                item.pid == watch("employerStateCode")
                            )
                            .map((item: any) => ({
                              label: item.name,
                              value: "" + item.id,
                            }))}
                        />
                      );
                    }}
                  />

                  <Controller
                    name="employerCity"
                    control={control}
                    render={({ field, fieldState: { error } }) => {
                      return (
                        <Input
                          {...field}
                          error={!!errors.employerCity?.message}
                          label={t("city")}
                          disabled={
                            watch("jobCode") == "Pesara" ||
                            watch("jobCode") == "Tidak Bekerja" ||
                            view
                          }
                        />
                      );
                    }}
                  />

                  <Controller
                    name="employerPostcode"
                    rules={{
                      maxLength: {
                        value: 5,
                        message: t("postcodeHelper"),
                      },
                      pattern: {
                        value: /^\d+$/,
                        message: t("numbersOnly"),
                      },
                    }}
                    control={control}
                    render={({ field, fieldState: { error } }) => {
                      return (
                        <Input
                          disabled={view}
                          {...field}
                          onChange={(e) => {
                            if (/^\d{0,5}$/.test(e.target.value)) {
                              field?.onChange(e.target.value);
                            }
                          }}
                          error={!!errors.employerPostcode?.message}
                          label={t("postcode")}
                        />
                      );
                    }}
                  />
                </>
              )}
              <Controller
                name="noTelP"
                control={control}
                render={({ field, fieldState: { error } }) => {
                  return (
                    <Input
                      disabled={view}
                      {...field}
                      error={!!errors.noTelP?.message}
                      label={t("officePhoneNumber")}
                      inputMode={"numeric"}
                      onInput={(e) => {
                        const input = e.target as HTMLInputElement;
                        input.value = input.value
                          .replace(/\D/g, "")
                          .slice(0, 12);
                      }}
                    />
                  );
                }}
              />
            </Grid>
            <Box
              sx={{
                display: "flex",
                justifyContent: "flex-end",
                mt: 2,
                gap: 2,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>

              {!view ? (
                <ButtonPrimary
                  disabled={
                    isUpdateAJK ||
                    mutate.isLoading ||
                    ((Number(watch("identificationType")) === 1 ||
                      Number(watch("identificationType")) === 4) &&
                      Number(watch("nationalityStatus")) === 1 &&
                      (watch("identificationNo")?.length < 12 ||
                        !userICCorrect ||
                        !userNameMatchIC))
                  }
                  type="submit"
                >
                  {t("save")}
                </ButtonPrimary>
              ) : null}
            </Box>
          </Box>
        </form>
      </Box>
    </Box>
  );
};

export default CreateAjk;
