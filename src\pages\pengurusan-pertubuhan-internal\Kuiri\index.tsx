import { useEffect } from "react";
import { NavLink, useNavigate } from "react-router-dom";
import { Outlet } from "react-router-dom";
import { useTranslation } from "react-i18next";

import { Box } from "@mui/material";
import WrapContent from "../View/WrapContent";
import Menu from "./Menu";
import { SocietyQueryPendingCountResponseBodyGet } from "../../../types";
import useQuery from "@/helpers/hooks/useQuery";
import { PermissionNames, pageAccessEnum } from "@/helpers";
import AuthHelper from "@/helpers/authHelper";

const Kuiri = <
  QueryPendingData extends SocietyQueryPendingCountResponseBodyGet = SocietyQueryPendingCountResponseBodyGet
>() => {
  const navigate = useNavigate();
  const { t } = useTranslation();

  const { data: queryPendingResponse, isLoading } = useQuery<{
    data: QueryPendingData;
  }>({
    url: "society/roDecision/getAllPendingCount/query",
  });
  const queryPendingData = queryPendingResponse?.data.data ?? null;

  //PERMISSION FOR KEPUTUSAN PERTUBUHAN / KUIRI TAB
  const hasKaunterPermission = AuthHelper.hasPageAccess(
    PermissionNames.KELULUSAN.label,
    pageAccessEnum.Read
  );

  useEffect(() => {
    if (!hasKaunterPermission) {
      navigate("/pengurus-pertubuhan/maklumat-pertubuhan");
    }
  }, [hasKaunterPermission, navigate]);

  const tab = [
    {
      name: t("pendaftaranPertubuhanInduk"),
      slug: "pendaftaran-pertubuhan-induk",
      number: queryPendingData?.societyRegistrationQueryPendingCount ?? 0,
    },
    {
      name: t("pendaftaranCawangan"),
      slug: "pendaftaran-cawangan",
      number: queryPendingData?.branchRegistrationQueryPendingCount ?? 0,
    },
    {
      name: t("pindaanUndangUndangInduk"),
      slug: "pindaan-undang-undang-induk",
      number: queryPendingData?.societyAmendmentQueryPendingCount ?? 0,
    },
    {
      name: t("rayuan"),
      slug: "rayuan",
      number: queryPendingData?.societyAppealQueryPendingCount ?? 0,
    },
    {
      name: t("pembaharuanSetiausaha"),
      slug: "pembaharuan-setiausaha",
      number: queryPendingData?.societyPrincipalSecretaryPendingCount ?? 0,
    },
  ];

  if (hasKaunterPermission) {
    return (
      <Box>
        <WrapContent title={t("kuiri")}>
          <Box
            sx={{
              display: "grid",
              gridTemplateColumns: "repeat(auto-fit, minmax(180px, 1fr))",
              gap: "16px",
            }}
          >
            {tab.map((data, index) => {
              return (
                <NavLink
                  key={`tab-query-${index}`}
                  end={index === 0}
                  to={`/pengurus-pertubuhan/keputusan-pertubuhan/kuiri${
                    data.slug === "pendaftaran-pertubuhan-induk"
                      ? ""
                      : `/${data.slug}`
                  }`}
                  style={{ textDecoration: "none" }}
                >
                  {({ isActive }) => (
                    <Menu
                      data={data}
                      isActive={isActive}
                      onClick={() => {}}
                      isLoading={isLoading}
                    />
                  )}
                </NavLink>
              );
            })}
          </Box>
        </WrapContent>

        <Outlet context={{ queryPendingData }} />
      </Box>
    );
  }
  return null;
};

export default Kuiri;
