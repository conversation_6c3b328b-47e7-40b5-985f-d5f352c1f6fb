import { Box, Typography } from "@mui/material"
import { useTranslation } from "react-i18next"

export interface FeedbackResponseBodyGetOnlyTitle {
  topic: string
  referenceNo: string
}

export interface CardAduanTitleSectionProps<
  Data extends FeedbackResponseBodyGetOnlyTitle = FeedbackResponseBodyGetOnlyTitle
> {
  /**
   * @default null
   */
  data?: null | Data

  /**
   * @default true
   */
  loading?: boolean
}

export const CardAduanTitleSection = <
  Data extends FeedbackResponseBodyGetOnlyTitle = FeedbackResponseBodyGetOnlyTitle,
  PropType extends CardAduanTitleSectionProps<Data> = CardAduanTitleSectionProps<Data>
>({ data = null, loading = true }: PropType) => {
  const { t } = useTranslation();

  return (
    <Box
      sx={{
        borderRadius: "1rem",
        backgroundColor: "white",
        boxShadow: "0 0 0.75rem rgba(234, 232, 232, 0.4)",
        padding: "1rem",
        display: "flex",
        flexDirection: "column",
        rowGap: "1rem",
      }}
    >
      <Box
        sx={theme => ({
          borderRadius: "1rem",
          backgroundColor: theme.palette.primary.main,
          padding: "1.5rem 2.5rem",
          display: "flex",
          flexDirection: "column",
          rowGap: "0.5rem",
          color: "white"
        })}
      >
        <Typography>{data?.topic ?? t("complaintOfOrganizationalMisconduct")}</Typography>
        <Typography>{data?.referenceNo ?? "ADUAN/SEL/22/2025"}</Typography>
      </Box>
    </Box>
  )
}
