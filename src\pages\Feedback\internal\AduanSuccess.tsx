import { Box, Button, Typography, useTheme } from "@mui/material"
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { formatDate } from "@/helpers";

export const FeedbackInternalAduanSuccess = () => {
  const theme = useTheme();
  const { t } = useTranslation();
  const navigate = useNavigate();

  const primary = theme.palette.primary.main;

  const handleButtonClicked = () => {
    navigate("../view/100", { relative: "path" });
  }


  return (
    <Box
      sx={{
        borderRadius: "1rem",
        backgroundColor: "white",
        boxShadow: "0 0 0.75rem rgba(234, 232, 232, 0.4)",
        padding: "1rem",
        display: "flex",
        flexDirection: "column",
        rowGap: "1rem"
      }}
    >
      <Box
        sx={{
          border: "1px solid #DADADA",
          borderRadius: "0.5rem",
          padding: "1.5rem"
        }}
      >
        <Typography
          color={primary}
          sx={{
            fontSize: "24px",
            fontWeight: "medium",
            marginBottom: "3rem"
          }}
        >
          {`${t("yourComplaintHasBeenSuccessfullySubmitted")}!`}
        </Typography>
        <Typography
          sx={{
            fontSize: "14px",
            color: "#666",
            "& > p": {
              mt: "0px !important",
            },
            "& > p.mb-0": {
              mb: "0px !important",
            },
            ".light": {
              fontWeight: 300
            }
          }}
        >
          <p>Nombor Rujukan Aduan : <span className="light">ADUAN/BP/1/2025</span></p>
          <br />
          <p className="mb-0">Tarikh : <span className="light">{formatDate("2025-09-12")}</span></p>
          <p>Masa : <span className="light">3:45 Petang</span></p>
          <br />
          <p className="light mb-0">{t("yourComplaintWillBeInvestigatedWithinDay", { count: 179 })}</p>
          <p className="light">{t("pleaseSaveThisComplaintReferenceNumber")}</p>
          <br />
          <p className="light">{`${t("thankyou")}!`}</p>
        </Typography>
      </Box>
      <div style={{
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        columnGap: "0.5rem",
      }}>
        <Button
          sx={{
            textTransform: "capitalize",
            minWidth: "7.5rem",
            color: "white"
          }}
          variant="contained"
          onClick={handleButtonClicked}
        >
          {t("next")}
        </Button>
      </div>
    </Box>
  )
}
