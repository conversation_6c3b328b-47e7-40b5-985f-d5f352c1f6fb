import { useTranslation } from "react-i18next";
import { Form, useFormikContext } from "formik";

import { Box, Button, CircularProgress, Grid, Typography, useTheme } from "@mui/material"
import { TextFieldControllerFormik } from "@/components/input";

import { type EnforcementLiabilityRestrictionRequestBodyCheck } from "@/pages/penguatkuasaan/internal/sekatan-liabiliti/Create";

export const FormEnforcementLiabilityRestrictionCheckNameOrMyKad = <
  RequestBody extends EnforcementLiabilityRestrictionRequestBodyCheck = EnforcementLiabilityRestrictionRequestBodyCheck
>() => {
  const theme = useTheme();
  const { t } = useTranslation();
  const { isSubmitting, isValid } = useFormikContext<RequestBody>();

  const primary = theme.palette.primary.main;
  const error = theme.palette.error.main;
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };

  return (
    <Form>
      <Box
        sx={{
          border: "0.5px solid #dadada",
          borderRadius: "10px",
          padding: "1.5rem"
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "1.5rem"
          }}
        >
          <Typography
            color={primary}
            sx={{
              fontSize: 14,
              fontWeight: "medium",
            }}
          >
            {t("semakanIndividu")}
          </Typography>
        </div>
        <Grid container spacing={2}>
          <Grid item md={4} xs={12}>
            <Typography sx={labelStyle}>
              {t("myKadNo")} <span style={{ color: error }}>*</span>
            </Typography>
          </Grid>
          <Grid item md={8} xs={12}>
            <TextFieldControllerFormik
              name="identificationNo"
              placeholder="XXXXXX-XX-XXXX"
              helperTextComponentPlacement="INSIDE"
            />
          </Grid>
          <Grid item md={4} xs={12}>
            <Typography sx={labelStyle}>
              {t("name")}
            </Typography>
          </Grid>
          <Grid item md={8} xs={12}>
            <TextFieldControllerFormik
              name="name"
              placeholder="Nama Pemegang Jawatan"
              helperTextComponentPlacement="INSIDE"
              helperTextFallbackValue={null}
            />
          </Grid>
        </Grid>
      </Box>
      <div style={{
        display: "flex",
        alignItems: "flex-end",
        justifyContent: "flex-end",
        marginTop: "1rem",
        columnGap: "0.5rem"
      }}>
        <Button
          type="submit"
          sx={{
            textTransform: "capitalize",
            minWidth: "7.5rem"
          }}
          variant="contained"
          disabled={!isValid || isSubmitting}
        >
          {isSubmitting ? <CircularProgress /> : t("semak")}
        </Button>
      </div>
    </Form>
  )
}
