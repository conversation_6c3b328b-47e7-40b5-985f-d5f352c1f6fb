import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { BaseRecord, CreateResponse } from '@refinedev/core';

interface JawatankuasaAJKFormState {
  type: string | number | null;
  savedMeetingDate: string | null;
  savedMeetingDetail: any;
  appointmentDate: string | null;
  uploadedIds: any[];
  citizenAJKs: CreateResponse<BaseRecord> | undefined[];
  nonCitizenAJKs: any[];
  branchId: number | null;
  isSimpan: boolean;
}

const initialState: JawatankuasaAJKFormState = {
  type: null,
  savedMeetingDate: "",
  savedMeetingDetail: null,
  appointmentDate: "",
  uploadedIds: [],
  citizenAJKs: [],
  nonCitizenAJKs: [],
  isSimpan: false,
  branchId: null
};

export const jawatankuasaAJKFormSlice = createSlice({
  name: "jawatankuasaAJKForm",
  initialState,
  reducers: {
    setJawatankuasaAJKFormValues: (
      state,
      action: PayloadAction<Partial<JawatankuasaAJKFormState>>
    ) => {
      return { ...state, ...action.payload };
    },
    resetJawatankuasaAJKFormValues: () => initialState,
  },
});

export const {
  setJawatankuasaAJKFormValues,
  resetJawatankuasaAJKFormValues,
} = jawatankuasaAJKFormSlice.actions;

export default jawatankuasaAJKFormSlice.reducer;
