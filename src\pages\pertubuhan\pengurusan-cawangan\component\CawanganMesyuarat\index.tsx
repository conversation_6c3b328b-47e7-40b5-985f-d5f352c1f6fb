// Update the custom theme with <PERSON>pins font
const theme = createTheme({
  components: {
    MuiCssBaseline: {
      styleOverrides: `
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

        * {
          font-family: 'Poppins', Arial, sans-serif !important;
        }
      `,
    },
  },
  typography: {
    fontFamily: "Poppins, Arial, sans-serif",
    h6: {
      fontSize: "16px",
      fontWeight: "bold",
    },
    body1: {
      fontSize: "14px",
    },
  },
});

import React, { useEffect, useState } from "react";
import dayjs from "dayjs";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next"; 
import { FieldValues } from "react-hook-form";
import {
  Box,
  Typography, 
  ThemeProvider,
  createTheme,
  CssBaseline, 
  But<PERSON>,
  Divider, 
} from "@mui/material";
import { ButtonPrimary } from "@/components/button";
import { useCustom } from "@refinedev/core";
import { API_URL } from "@/api";
import { useForm } from "@refinedev/react-hook-form"; 
import { EyeIcon } from "@/components/icons";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import {
  ApplicationStatusEnum,
  COMMITTEE_TASK_TYPE,
  MeetingTypeOption,
} from "@/helpers/enums"; 
import ALiranTugas from "../../AliranTugas";
import { DataTable, IColumn } from "@/components";
import { useBranchContext } from "@/pages/pertubuhan/BranchProvider";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

const CawanganMesyuarat: React.FC = () => {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();

  const {
    fetchAliranTugasAccess,
    isAliranModuleAccess,
    branchId,
    isBlackListed,
    isAuthorized,
  } = useBranchContext();

  const { setValue, watch } = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 5,
      searchQuery: undefined,
    },
  });

  const page = watch("page");
  const pageSize = watch("pageSize");

  const {
    data,
    isLoading: isMeetingLoading,
    refetch,
  } = useCustom({
    url: `${API_URL}/society/meeting/findByBranchId/${branchId}`,
    method: "get",
    config: {
      headers: {
        portal: localStorage.getItem("portal"),
        authorization: `Bearer ${localStorage.getItem("refine-auth")}`,
      },
    },
  });

  const columns: IColumn[] = [
    {
      field: "meetingType",
      headerName: t("meetingType"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        return MeetingTypeOption.find(
          (option) => option.value === parseInt(params.row.meetingType)
        )?.label;
      },
    },
    {
      field: "meetingPlace",
      headerName: t("meetingPlace"),
      flex: 1,
      align: "center",
      renderCell: (params: any) =>
        params.row.meetingPlace ? params.row.meetingPlace : "-",
    },
    {
      field: "meetingDate",
      headerName: t("meetingDate"),
      flex: 1,
      align: "center",
      renderCell: (params: any) =>
        dayjs(params.row.meetingDate).format("DD/MM/YYYY"),
    },
    {
      field: "status",
      headerName: t("status"),
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const isActive =
          params.row.status === "11" || params.row.status === "001";
        return (
          <Typography
            className="status-pertubuhan-text"
            sx={{
              backgroundColor: "#fff",
              border: `2px solid ${
                isActive ? "var(--success)" : "var(--error)"
              }`,
              textAlign: "center",
              padding: "4px 8px",
              borderRadius: "20px",
              fontSize: "14px",
              color: "#666666",
              fontWeight: "normal",
            }}
          >
            {isActive
              ? "Selesai"
              : t(
                  ApplicationStatusEnum[
                    (params.row.status as keyof typeof ApplicationStatusEnum) ||
                      "0"
                  ]
                )}
          </Typography>
        );
      },
    },
    {
      field: "actions",
      headerName: "",
      flex: 1,
      align: "center",
      renderCell: (params: any) => {
        const row = params.row;

        return (
          <Link to={`${row.id}`}>
            <EyeIcon />
          </Link>
        );
      },
    },
  ];

  const listData = data?.data?.data || [];
  const totalList = data?.data?.data?.length || [];

  const handleDaftarMesyuarat = () => {
    navigate("create");
  };

  useEffect(() => {
    refetch();
  }, [watch("searchQuery")]);

  const location = useLocation();
  const disabled = location.state?.disabled ?? false;

  useEffect(() => {
    fetchAliranTugasAccess(COMMITTEE_TASK_TYPE.PENGURUSAN_MESYUARAT);
  }, []);

  const isAccessible = !isBlackListed && (isAuthorized || isAliranModuleAccess);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <Box
        sx={{
          p: { xs: 1, sm: 2, md: 3 },
          backgroundColor: "white",
          borderRadius: "14px",
          mb: 2,
        }}
      >
        <Box
          sx={{
            width: "80%",
            display: "flex",
            alignItems: "center",
            borderRadius: "8px",
            backgroundColor: "#FFFFFF",
            marginInline: "auto",
            marginTop: "12px",
            marginBottom: "12px",
            boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.1)",
            padding: "4px",
            gap: "4px",
          }}
        >
          <Button
            variant="text"
            startIcon={
              <img
                src={"/filter-icon.svg"}
                alt="Filter Icon"
                width="18"
                height="18"
              />
            }
            sx={{
              flex: 1,
              height: "36px",
              color: "#6B7280",
              textTransform: "none",
              textWrap: "nowrap",
              "&:hover": {
                backgroundColor: "#F9FAFB",
              },
            }}
          >
            {t("filterBy")}
          </Button>

          {[
            t("meetingType"),
            t("meetingPlace"),
            t("meetingDate"),
            t("status"),
          ].map((text, index) => (
            <React.Fragment key={text}>
              <Divider
                orientation="vertical"
                flexItem
                sx={{ backgroundColor: "#E5E7EB" }}
              />
              <Button
                variant="text"
                endIcon={<KeyboardArrowDownIcon />}
                sx={{
                  flex: 2,
                  height: "36px",
                  color: "#6B7280",
                  textTransform: "none",
                  width: "150px",
                  justifyContent: "space-between",
                  "&:hover": {
                    backgroundColor: "#F9FAFB",
                  },
                }}
              >
                {text}
              </Button>
            </React.Fragment>
          ))}
        </Box>

        <DataTable
          columns={columns}
          rows={listData}
          page={page}
          rowsPerPage={pageSize}
          totalCount={totalList}
          onPageChange={(newPage) => setValue("page", newPage)}
          onPageSizeChange={(newPageSize) => {
            setValue("page", 1);
            setValue("pageSize", newPageSize);
          }}
          isLoading={isMeetingLoading}
          customNoDataText={t("noRecordForStatus")}
        />
      </Box>
      {isAccessible ? (
        <>
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              backgroundColor: "white",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                border: "1px solid rgba(0, 0, 0, 0.12)",
                borderRadius: "14px",
                p: 3,
              }}
            >
              <Typography variant="subtitle1" sx={sectionStyle}>
                {t("organizationMeeting")}
              </Typography>

              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{ color: "#666666", fontSize: 14 }}
                >
                  {t("tambahMesyuarat")}
                </Typography>

                <ButtonPrimary
                  disabled={disabled}
                  onClick={handleDaftarMesyuarat}
                >
                  {t("add")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>

          {isAccessible && isAuthorized ? (
            <ALiranTugas module={COMMITTEE_TASK_TYPE.PENGURUSAN_MESYUARAT} />
          ) : null}
        </>
      ) : (
        <></>
      )}
    </ThemeProvider>
  );
};

export default CawanganMesyuarat;
