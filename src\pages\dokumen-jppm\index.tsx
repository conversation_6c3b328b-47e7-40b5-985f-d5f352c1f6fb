import { globalStyles } from "@/helpers";

import { Box, Typography, Grid } from "@mui/material";
import DocumentCard from "./Card";

const DokumenJPPM = () => {
  const classes = globalStyles();

  return (
    <>
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          width: "100%",
          height: "100vh",
          padding: "0 90px",
          color: "#fff",
          backgroundImage: 'url("/banner-4.jpg")',
          backgroundSize: "cover",
          backgroundRepeat: "no-repeat",
          backgroundPosition: "bottom",
          textShadow: "0px 4px 4px rgba(0, 0, 0, 0.5)",
        }}
      >
        <Typography fontWeight={500} fontSize="40px">
          Dokumen JPPM
        </Typography>
      </Box>

      <Box sx={{ bgcolor: "#F4F7FA", py: 8 }}>
        <Box maxWidth="xl" mx="auto" px={2}>
          <Typography
            variant="h5"
            align="center"
            fontWeight="bold"
            gutterBottom
            color="#666666"
            mb={4}
          >
            Dokumen Rasmi JP<PERSON>
          </Typography>
          <Typography
            variant="body1"
            align="center"
            mb={4}
            color="#666666"
            maxWidth="1120px"
            mx="auto"
          >
            Dokumen Rasmi JPPM menyediakan tatacara pengurusan pertubuhan, dan
            akta pertubuhan 1966 sebagai rujukan penting bagi pengurusan dan
            pematuhan pertubuhan di bawah seliaan Jabatan Pendaftaran Pertubuhan
            Malaysia (JPPM).
          </Typography>

          <Box className={classes.section}>
            <Box className={classes.sectionBox}>
              <Typography className="title" mb={2}>
                Senarai Dokumen
              </Typography>

              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <DocumentCard
                    title="Akta Pertubuhan 1966"
                    subtitle="Akta Pertubuhan • 23/01/2020"
                    formats={["PDF Format", "eBook Format"]}
                    size="182 B"
                  />
                </Grid>
                <Grid item xs={12} md={6}>
                  <DocumentCard
                    title="Panduan Pengurusan Pertubuhan"
                    subtitle="Panduan Pengurusan Pertubuhan • 22/01/2020"
                    formats={["PDF Format", "eBook Format"]}
                    size="198 B"
                  />
                </Grid>
              </Grid>
            </Box>
          </Box>
        </Box>
      </Box>
    </>
  );
};

export default DokumenJPPM;
