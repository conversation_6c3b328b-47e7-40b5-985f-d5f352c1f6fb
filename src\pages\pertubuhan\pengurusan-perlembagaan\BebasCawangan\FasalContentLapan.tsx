import {
  Box,
  Checkbox,
  FormControlLabel,
  Grid,
  TextField,
  Theme,
  Typography,
  useMediaQuery,
  TextareaAutosize,
  MenuItem,
  Select,
  FormControl,
  FormHelperText,
} from "@mui/material";
import { useTranslation } from "react-i18next";
import { useEffect, useRef, useState } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useCreate, useCustom, useCustomMutation } from "@refinedev/core";
import { API_URL } from "../../../../api";
import { ButtonOutline, ButtonPrimary } from "../../../../components/button";
import { handleSaveContent } from "../../pengurusan-pertubuhan/perlembagaan/helper/handleSaveContent";
import { useSelector } from "react-redux";
import { capitalizeWords, getLocalStorage } from "../../../../helpers/utils";
import { ClauseProps } from "../UpdatePindaanPerlembagaan";
import FasalNameCom from "@/components/FasalBebasComponent/FasalNameCom";
import EditableFasalTextArea from "@/components/FasalBebasComponent/EditableFasalTextArea";
import ContentBox from "@/components/FasalBebasComponent/ContentBox";
import ReminderEditable from "@/components/FasalBebasComponent/ReminderEditable";
import CheckContent from "@/components/FasalBebasComponent/CheckContent";

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

interface FasalContentSatuProps {
  activeStep: number;
  setActiveStep: React.Dispatch<React.SetStateAction<number>>;
  clause: ClauseProps;
  asalData: string;
  name: string;
}
export const FasalContentLapanBebasCawangan: React.FC<
  FasalContentSatuProps
> = ({ activeStep, setActiveStep, clause, asalData, name }) => {
  const requiredText = [
    "<<tempoh pelantikan jawatankuasa>>",
    "<<jenis mesyuarat agung>>",
    "<<jawatan Pengerusi Cawangan>>",
    "<<bilangan Naib Pengerusi Cawangan>>",
    "<<jawatan Naib Pengerusi Cawangan>>",
    "<<jawatan Setiausaha Cawangan>>",
    "<<bilangan pen. SU Cawangan>>",
    "<<jawatan Penolong Setiausaha Cawangan>>",
    "<<jawatan Bendahari Cawangan>>",
    "<<bilangan pen. bendahari cawangan>>",
    "<<jawatan Penolong Bendahari Cawangan>>",
    "<<bilangan ajk cawangan>>",
    "<<jawatan Ahli Jawatankuasa Biasa Cawangan>>",
    "<<tempoh pelantikan jawatankuasa>>",
    "<<kekerapan mesyuarat>>",
  ];
  const { t, i18n } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const navigate = useNavigate();
  const fasalItems = useSelector((state: { fasal: any }) => state.fasal.data);

  const [clauseContentId, setClauseContentId] = useState("");
  const [namaPertubuhan, setNamaPertubuhan] = useState("");
  const [tempohJawatan, setTempohJawatan] = useState(t("setahun"));
  const [pemilihanAjk, setPemilihanAjk] = useState(t("annual"));
  const [lainlain, setLainLain] = useState("");
  const [kekerapan, setKekerapan] = useState("4");
  const [tempohPelucutan, setTempohPelucutan] = useState("0");
  const [tempohPelucutanWaktu, setTempohPelucutanWaktu] = useState(t("day"));
  const [pengerusi, setPengerusi] = useState(t("branchChairman"));
  const [jumlahPengerusi, setJumlahPengerusi] = useState<any>("1");
  const [timbalan, setTimbalan] = useState(t("timbalanPengerusi"));
  const [jumlahTimbalan, setJumlahTimbalan] = useState<any>("0");
  const [naib, setNaib] = useState(t("naibPengerusi"));
  const [jumlahNaib, setJumlahNaib] = useState<any>("0");
  const [setiaUsaha, setSetiaUsaha] = useState(t("branchSecretary"));
  const [jumlahSetiaUsaha, setJumlahSetiaUsaha] = useState<any>("1");
  const [penolongSetiaUsaha, setPenolongSetiaUsaha] = useState(
    t("asistantSecretary")
  );
  const [jumlahPenolongSetiaUsaha, setJumlahPenolongSetiaUsaha] =
    useState<any>("0");
  const [bendahari, setBendahari] = useState(t("bendahariCawangan"));
  const [jumlahBendahari, setJumlahBendahari] = useState<any>("1");
  const [penolongBendahari, setPenolongBendahari] = useState(
    t("asistantTreasurer")
  );
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const [jumlahPenolongBendahari, setJumlahPenolongBendahari] =
    useState<any>("0");
  const [ahliBiasa, setAhliBiasa] = useState(t("ordinaryCommitteeMember"));
  const [jumlahAhliBiasa, setJumlahAhliBiasa] = useState<any>("0");
  const [dataId, setDataId] = useState<number | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [societyId, setSocietyId] = useState<any>(null);
  const [clauseContentEditable, setClauseContentEditable] = useState(
    clause.clauseContent
  );
  const [checked, setChecked] = useState(false);
  const [clauseNo, setClauseNo] = useState("");
  const [clauseName, setClauseName] = useState("");
  const handleChangeCheckbox = (event: any) => {
    setChecked(event.target.checked);
  };

  useEffect(() => {
    if (isEdit) {
      setChecked(true);
    }
  }, [isEdit]);

  const { id, clauseId } = useParams();
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  useEffect(() => {
    if (societyDataRedux) {
      setSocietyId(societyDataRedux.id);
      setNamaPertubuhan(societyDataRedux.societyName);
    }
  }, [societyDataRedux]);

  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
  };
  const { mutate: createClauseContent, isLoading: isCreatingContent } =
    useCreate();
  const { mutate: editClauseContent, isLoading: isEditingContent } =
    useCustomMutation();

  const validateForm = () => {
    const errors: { [key: string]: string } = {};

    if (!tempohJawatan) {
      errors.tempohJawatan = t("fieldRequired");
    }
    if (!pemilihanAjk) {
      errors.pemilihanAjk = t("fieldRequired");
    }
    if (!kekerapan) {
      errors.kekerapan = t("fieldRequired");
    }
    if (!jumlahPengerusi) {
      errors.jumlahPengerusi = t("fieldRequired");
    }
    if (!pengerusi) {
      errors.pengerusi = t("fieldRequired");
    }
    if (!timbalan) {
      errors.timbalan = t("fieldRequired");
    }
    if (!jumlahSetiaUsaha) {
      errors.jumlahSetiaUsaha = t("fieldRequired");
    }
    if (!setiaUsaha) {
      errors.setiaUsaha = t("fieldRequired");
    }
    if (!jumlahBendahari) {
      errors.jumlahBendahari = t("fieldRequired");
    }
    if (!bendahari) {
      errors.bendahari = t("fieldRequired");
    }

    return errors;
  };

  const handleChange = (e: any) => {
    setClauseContentEditable(e.target.value);
  };

  useEffect(() => {
    if (clause) {
      //const clause = JSON.parse(clauseValue);
      setDataId(clause.id);
      if (clause.clauseNo) {
        setClauseNo(clause.clauseNo);
      }
      if (clause.clauseName) {
        setClauseName(clause.clauseName);
      }
      if (clause.clauseContentId) {
        //setClauseContent(clause.clauseContent)
        setClauseContentId(clause.clauseContentId);
      }
      //setNamaPertubuhan(clause.societyName);
      const fieldMappings: Record<string, (value: string) => void> = {
        "Tempoh Pelantikan Jawatankuasa": setTempohJawatan,
        "Jenis Mesyuarat Agung": (value: string) => {
          if (value && value !== t("annual") && value !== t("biennial")) {
            setPemilihanAjk(t("lainLain"));
            setLainLain(value);
          } else {
            setPemilihanAjk(value);
          }
        },
        "Kekerapan Mesyuarat Jawatankuasa": setKekerapan,
        "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa":
          setTempohPelucutan,
        "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa":
          setTempohPelucutanWaktu,
        Pengerusi: setPengerusi,
        "Bilangan Pengerusi": setJumlahPengerusi,
        "Timbalan Pengerusi": setTimbalan,
        "Bilangan Timbalan Pengerusi": setJumlahTimbalan,
        "Naib Pengerusi": setNaib,
        "Bilangan Naib Pengerusi": setJumlahNaib,
        Setiausaha: setSetiaUsaha,
        "Bilangan Setiausaha": setJumlahSetiaUsaha,
        "Penolong Setiausaha": setPenolongSetiaUsaha,
        "Bilangan Penolong Setiausaha": setJumlahPenolongSetiaUsaha,
        Bendahari: setBendahari,
        "Bilangan Bendahari": setJumlahBendahari,
        "Penolong Bendahari": setPenolongBendahari,
        "Bilangan Penolong Bendahari": setJumlahPenolongBendahari,
        "Ahli Jawatankuasa Biasa": setAhliBiasa,
        "Bilangan Ahli Jawatankuasa Biasa": setJumlahAhliBiasa,
      };

      if (clause.constitutionValues) {
        clause.constitutionValues.forEach((item: any) => {
          const setter = fieldMappings[item.titleName];
          if (setter && item.definitionName) {
            setter(item.definitionName);
          }
        });
      }

      setClauseContentEditable(
        clause.clauseModifyContent
          ? clause.clauseModifyContent
          : clause.clauseContent
      );
      setIsEdit(clause.edit);
    }
  }, [clause]);

  const amendmentId = getLocalStorage("amendmentId", null);
  const isViewMode = getLocalStorage("isViewMode", false);
  let clauseContent = clauseContentEditable;
  clauseContent = clauseContent.replaceAll(
    /<<tempoh pelantikan jawatankuasa>>/gi,
    `<b>${tempohJawatan || "<<tempoh pelantikan jawatankuasa>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<jenis mesyuarat agung>>/gi,
    `<b>${pemilihanAjk || "<<jenis mesyuarat agung>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<kekerapan mesyuarat>>/gi,
    `<b>${kekerapan || "<<kekerapan mesyuarat>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<tempoh membela diri pelucutan>>/gi,
    `<b>${tempohPelucutan || "<<tempoh membela diri pelucutan>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<notis panggilan mesyuarat>>/gi, `<b>${notisPanggilanMesyuarat || '<<notis panggilan mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Pengerusi Cawangan>>/gi,
    `<b>${pengerusi || "<<jawatan Pengerusi Cawangan>>"}</b>`
  );
  //clauseContent = clauseContent.replaceAll(/<<jenis mesyuarat>>/gi, `<b>${jumlahPengerusi || '<<jenis mesyuarat>>'}</b>`);
  clauseContent = clauseContent.replaceAll(
    /<<jawatan Timbalan Pengerusi Cawangan>>/gi,
    `<b>${timbalan || "<<jawatan Timbalan Pengerusi Cawangan>>"}</b>`
  );
  clauseContent = clauseContent.replaceAll(
    /<<bilangan Timbalan Pengerusi Cawangan>>/gi,
    `<b>${jumlahTimbalan || "<<bilangan Timbalan Pengerusi Cawangan>>"}</b>`
  );

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Bendahari Cawangan>>/gi,
    `<b>${bendahari || "<<jawatan Bendahari Cawangan>>"}</b>`
  );

  clauseContent = clauseContent.replaceAll(
    /<<jawatan Setiausaha Cawangan>>/gi,
    `<b>${setiaUsaha || "<<jawatan Setiausaha Cawangan>>"}</b>`
  );
  //
  if (Number(jumlahNaib) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Naib Pengerusi Cawangan>>/gi,
      `<b>${naib || "<<jawatan Naib Pengerusi Cawangan>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan Naib Pengerusi Cawangan>>/gi,
      `<b>${
        Number(jumlahNaib) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahNaib} orang` || "<<bilangan Naib Pengerusi Cawangan>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*ii\.\s*<<bilangan Naib Pengerusi Cawangan>> <<jawatan Naib Pengerusi Cawangan>>\s*[\r\n]?/gim,
      "    "
    );
  }
  //
  if (Number(jumlahPenolongSetiaUsaha) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Setiausaha Cawangan>>/gi,
      `<b>${
        penolongSetiaUsaha || "<<jawatan Penolong Setiausaha Cawangan>>"
      }</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. SU Cawangan>>/gi,
      `<b>${
        Number(jumlahPenolongSetiaUsaha) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongSetiaUsaha} orang` ||
            "<<bilangan pen. SU Cawangan>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*iv\.\s*<<bilangan pen. SU Cawangan>> <<jawatan Penolong Setiausaha Cawangan>>\s*[\r\n]?/gim,
      "    "
    );
  }

  //
  if (Number(jumlahPenolongBendahari) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Penolong Bendahari Cawangan>>/gi,
      `<b>${penolongBendahari || "<<jawatan Penolong Bendahari Cawangan>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan pen. bendahari Cawangan>>/gi,
      `<b>${
        Number(jumlahPenolongBendahari) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahPenolongBendahari} orang` ||
            "<<bilangan pen. bendahari Cawangan>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vi\.\s*<<bilangan pen. bendahari Cawangan>>\s*(?:orang\s+)?<<jawatan Penolong Bendahari Cawangan>>\s*[\r\n]?/gim,
      "    "
    );
  }
  //

  if (Number(jumlahAhliBiasa) > 0) {
    clauseContent = clauseContent.replaceAll(
      /<<jawatan Ahli Jawatankuasa Biasa Cawangan>>/gi,
      `<b>${ahliBiasa || "<<jawatan Ahli Jawatankuasa Biasa Cawangan>>"}</b>`
    );
    clauseContent = clauseContent.replaceAll(
      /<<bilangan ajk Cawangan>>/gi,
      `<b>${
        Number(jumlahAhliBiasa) === 1
          ? capitalizeWords(t("seorang"))
          : `${jumlahAhliBiasa} orang` || "<<bilangan ajk Cawangan>>"
      }</b>`
    );
  } else {
    clauseContent = clauseContent.replace(
      /^\s*vii\.\s*<<bilangan ajk Cawangan>> <<jawatan Ahli Jawatankuasa Biasa Cawangan>>\s*[\r\n]?/gim,
      ""
    );
  }

  clauseContent = clauseContent.replace("<<jawatan telah diubahsuai>>", "");
  clauseContent = clauseContent.replace(
    "<<penambahan jawatan diubahsuai>>",
    ``
  );

  clauseContent = clauseContent.replaceAll(/<</gi, "<b>&lt;&lt;");
  clauseContent = clauseContent.replaceAll(/>>/gi, "&gt;&gt;</b>");

  clauseContent = renumberRomanList(clauseContent);

  function renumberRomanList(content: any) {
    const romanNumerals = [
      "i",
      "ii",
      "iii",
      "iv",
      "v",
      "vi",
      "vii",
      "viii",
      "ix",
      "x",
    ];
    let index = 0;

    return content.replace(
      /^(\s*)(?:i{1,3}|iv|v?i{0,3}|ix|x)\.\s/gim,
      (_: any, indent: any) => {
        const roman = romanNumerals[index++] || `${index}.`;
        return `${indent}${roman}. `;
      }
    );
  }

  const handlerChangeClauseContentEditable = (val: string) => {
    setClauseContentEditable(val);
  };
  return (
    <>
      <Grid container>
        {/* name section */}
        <FasalNameCom clauseId={clauseId} name={name} />
        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Box
              sx={{
                backgroundColor: "#FFFFFF",
                borderRadius: "14px",
              }}
            >
              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  backgroundColor: "#FFFFFF",
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mb: 2,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {t("positionOfAuthority")}
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={4}>
                    <Typography sx={labelStyle}>
                      {t("electionPeriod")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <FormControl fullWidth required>
                      <Select
                        size="small"
                        value={tempohJawatan}
                        displayEmpty
                        onChange={(e) =>
                          setTempohJawatan(e.target.value as string)
                        }
                      >
                        <MenuItem value={t("setahun")}>{t("setahun")}</MenuItem>
                        <MenuItem value={t("duaTahun")}>
                          {t("duaTahun")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={4}>
                    <Typography sx={labelStyle}>
                      {t("jenisMesyuaratAgung")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={2}>
                    <FormControl fullWidth required>
                      <Select
                        size="small"
                        value={pemilihanAjk}
                        displayEmpty
                        onChange={(e) => {
                          setPemilihanAjk(e.target.value as string);

                          if (e.target.value === t("annual")) {
                            setLainLain("");
                          } else if (e.target.value === t("biennial")) {
                            setLainLain("");
                          }
                        }}
                      >
                        <MenuItem value={t("annual")}>{t("annual")}</MenuItem>
                        <MenuItem value={t("biennial")}>
                          {t("biennial")}
                        </MenuItem>
                        <MenuItem value={t("lainLain")}>
                          {t("lainLain")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                </Grid>
                <Grid container spacing={2} sx={{ mt: 0 }}>
                  {pemilihanAjk === t("lainLain") ? (
                    <>
                      <Grid item xs={12} md={4}></Grid>
                      <Grid item xs={12} md={2}>
                        <TextField
                          type="text"
                          size="small"
                          placeholder="Yearly, Biannually, Tri-tahunan"
                          fullWidth
                          required
                          value={lainlain}
                          onChange={(e) => {
                            setLainLain(e.target.value as string);
                          }}
                        />
                      </Grid>
                    </>
                  ) : null}
                </Grid>
                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={4}>
                    <Typography sx={labelStyle}>
                      {t("meetingFrequency")}
                    </Typography>
                  </Grid>
                  <Grid item xs={12} md={4}>
                    <TextField
                      disabled={isViewMode}
                      type="number"
                      onKeyDown={(e) => {
                        if (
                          e.key.toLowerCase() === "e" ||
                          e.key === "E" ||
                          e.key === "+" ||
                          e.key === "-"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      size="small"
                      placeholder="4"
                      fullWidth
                      required
                      value={kekerapan}
                      onChange={(e) => setKekerapan(e.target.value)}
                      InputProps={{
                        endAdornment: (
                          <Typography sx={{ ...labelStyle, mt: 1 }}>
                            {t("times")}
                          </Typography>
                        ),
                        inputProps: {
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item />
                </Grid>
              </Box>
 
              <Box
                sx={{
                  p: { xs: 1, sm: 2, md: 3 },
                  backgroundColor: "#FFFFFF",
                  border: "1px solid #D9D9D9",
                  borderRadius: "14px",
                  mb: 2,
                }}
              >
                <Typography variant="subtitle1" sx={sectionStyle}>
                  {t("ahliJawatanKuasa")}
                </Typography>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={2}>
                    <TextField
                      onKeyDown={(e) => {
                        if (
                          e.key.toLowerCase() === "e" ||
                          e.key === "E" ||
                          e.key === "+" ||
                          e.key === "-"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      size="small"
                      placeholder="0"
                      fullWidth
                      disabled
                      sx={{ background: "#E8E9E8" }}
                      required
                      value={jumlahPengerusi}
                      onChange={(e) => setJumlahPengerusi(e.target.value)}
                      type="number"
                      InputProps={{
                        inputProps: {
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth required>
                      <Select
                        disabled={isViewMode}
                        size="small"
                        value={pengerusi}
                        displayEmpty
                        onChange={(e) => setPengerusi(e.target.value as string)}
                      >
                        <MenuItem value={t("branchChairman")}>
                          {t("branchChairman")}{" "}
                          <Typography sx={{ display: "inline", color: "red" }}>
                            *
                          </Typography>
                        </MenuItem>
                        <MenuItem value={t("presidenCawangan")}>
                          {t("presidenCawangan")}{" "}
                          <Typography sx={{ display: "inline", color: "red" }}>
                            *
                          </Typography>
                        </MenuItem>
                        <MenuItem value={t("thepresidentBranch")}>
                          {t("thepresidentBranch")}{" "}
                          <Typography sx={{ display: "inline", color: "red" }}>
                            *
                          </Typography>
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item />
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={2}>
                    <TextField
                      disabled={isViewMode}
                      size="small"
                      placeholder="0"
                      fullWidth
                      required
                      value={jumlahNaib}
                      onChange={(e) => setJumlahNaib(e.target.value)}
                      type="number"
                      InputProps={{
                        inputProps: {
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth required>
                      <Select
                        disabled={isViewMode}
                        size="small"
                        value={naib}
                        displayEmpty
                        onChange={(e) => setNaib(e.target.value as string)}
                      >
                        <MenuItem value={t("timbalanPengerusiCawangan")}>
                          {t("timbalanPengerusiCawangan")}
                        </MenuItem>
                        <MenuItem value={t("timbalanPresidenCawangan")}>
                          {t("timbalanPresidenCawangan")}
                        </MenuItem>
                        <MenuItem
                          value={`${t("timbalanyYangDipertua")} ${t(
                            "fasalCawangan"
                          )}`}
                        >
                          {t("timbalanyYangDipertua")} {t("fasalCawangan")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item />
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={2}>
                    <TextField
                      onKeyDown={(e) => {
                        if (
                          e.key.toLowerCase() === "e" ||
                          e.key === "E" ||
                          e.key === "+" ||
                          e.key === "-"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      size="small"
                      placeholder="0"
                      fullWidth
                      required
                      disabled
                      sx={{ background: "#E8E9E8" }}
                      value={jumlahSetiaUsaha}
                      onChange={(e) => setJumlahSetiaUsaha(e.target.value)}
                      type="number"
                      InputProps={{
                        inputProps: {
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth required>
                      <Select
                        disabled={isViewMode}
                        size="small"
                        value={setiaUsaha}
                        displayEmpty
                        onChange={(e) =>
                          setSetiaUsaha(e.target.value as string)
                        }
                      >
                        <MenuItem value={t("branchSecretary")}>
                          {t("branchSecretary")}{" "}
                          <Typography sx={{ display: "inline", color: "red" }}>
                            *
                          </Typography>
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item />
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={2}>
                    <TextField
                      disabled={isViewMode}
                      onKeyDown={(e) => {
                        if (
                          e.key.toLowerCase() === "e" ||
                          e.key === "E" ||
                          e.key === "+" ||
                          e.key === "-"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      size="small"
                      placeholder="0"
                      fullWidth
                      required
                      value={jumlahPenolongSetiaUsaha}
                      onChange={(e) =>
                        setJumlahPenolongSetiaUsaha(e.target.value)
                      }
                      type="number"
                      InputProps={{
                        inputProps: {
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth required>
                      <Select
                        size="small"
                        value={penolongSetiaUsaha}
                        displayEmpty
                        onChange={(e) =>
                          setPenolongSetiaUsaha(e.target.value as string)
                        }
                      >
                        <MenuItem value={t("penolongSetiausahaCawangan")}>
                          {t("penolongSetiausahaCawangan")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item />
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={2}>
                    <TextField
                      onKeyDown={(e) => {
                        if (
                          e.key.toLowerCase() === "e" ||
                          e.key === "E" ||
                          e.key === "+" ||
                          e.key === "-"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      size="small"
                      placeholder="0"
                      fullWidth
                      required
                      disabled
                      sx={{ background: "#E8E9E8" }}
                      value={jumlahBendahari}
                      onChange={(e) => setJumlahBendahari(e.target.value)}
                      type="number"
                      InputProps={{
                        inputProps: {
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth required>
                      <Select
                        disabled={isViewMode}
                        size="small"
                        value={bendahari}
                        displayEmpty
                        onChange={(e) => setBendahari(e.target.value as string)}
                      >
                        <MenuItem value={t("bendahariCawangan")}>
                          {t("bendahariCawangan")}{" "}
                          <Typography sx={{ display: "inline", color: "red" }}>
                            *
                          </Typography>
                        </MenuItem>
                        {/* <MenuItem value={t("honoraryTreasurer")}>
                          {t("honoraryTreasurer")}{" "}
                          <Typography sx={{ display: "inline", color: "red" }}>
                            *
                          </Typography>
                        </MenuItem> */}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item />
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={2}>
                    <TextField
                      disabled={isViewMode}
                      onKeyDown={(e) => {
                        if (
                          e.key.toLowerCase() === "e" ||
                          e.key === "E" ||
                          e.key === "+" ||
                          e.key === "-"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      size="small"
                      placeholder="0"
                      fullWidth
                      required
                      value={jumlahPenolongBendahari}
                      onChange={(e) =>
                        setJumlahPenolongBendahari(e.target.value)
                      }
                      type="number"
                      InputProps={{
                        inputProps: {
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth required>
                      <Select
                        disabled={isViewMode}
                        size="small"
                        value={penolongBendahari}
                        displayEmpty
                        onChange={(e) =>
                          setPenolongBendahari(e.target.value as string)
                        }
                      >
                        <MenuItem value={t("penolongBendahariCawangan")}>
                          {t("penolongBendahariCawangan")}
                        </MenuItem>
                        {/* <MenuItem value={t("honoraryAssistantTreasurer")}>
                          {t("honoraryAssistantTreasurer")}
                        </MenuItem> */}
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item />
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={2}>
                    <TextField
                      disabled={isViewMode}
                      onKeyDown={(e) => {
                        if (
                          e.key.toLowerCase() === "e" ||
                          e.key === "E" ||
                          e.key === "+" ||
                          e.key === "-"
                        ) {
                          e.preventDefault();
                        }
                      }}
                      size="small"
                      placeholder="0"
                      fullWidth
                      required
                      value={jumlahAhliBiasa}
                      onChange={(e) => setJumlahAhliBiasa(e.target.value)}
                      type="number"
                      InputProps={{
                        inputProps: {
                          inputMode: "numeric",
                          pattern: "[0-9]*",
                          min: 0,
                        },
                      }}
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControl fullWidth required>
                      <Select
                        disabled={isViewMode}
                        size="small"
                        value={ahliBiasa}
                        displayEmpty
                        onChange={(e) => setAhliBiasa(e.target.value as string)}
                      >
                        <MenuItem value={t("ordinaryCommitteeMember")}>
                          {t("ordinaryCommitteeMember")}
                        </MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>

                  <Grid item />
                </Grid>

                <Grid container spacing={2} sx={{ mt: 0 }}>
                  <Grid item xs={12} md={8}>
                    <Typography
                      sx={{
                        fontWeight: "500 !important",
                        color: "#666666",
                        border: "1px solid #DADADA",
                        borderRadius: "5px",
                        py: 1,
                        display: "flex",
                        justifyContent: "center",
                      }}
                    >
                      {parseInt(jumlahPengerusi || 0) +
                        parseInt(jumlahTimbalan || 0) +
                        parseInt(jumlahNaib || 0) +
                        parseInt(jumlahSetiaUsaha || 0) +
                        parseInt(jumlahPenolongSetiaUsaha || 0) +
                        parseInt(jumlahBendahari || 0) +
                        parseInt(jumlahPenolongBendahari || 0) +
                        parseInt(jumlahAhliBiasa || 0)}{" "}
                      Bilangan Ahli Jawatankuasa
                    </Typography>
                  </Grid>
                  <Grid item />
                </Grid>
              </Box>
            </Box>
          </Box>
        </Grid>

        <Grid item xs={12}>
          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <ReminderEditable />
            <EditableFasalTextArea
              requiredFieldText={requiredText}
              clauseContentEditable={clauseContentEditable}
              setClauseContentEditable={handlerChangeClauseContentEditable}
            />
          </Box>

          <Box
            sx={{
              p: { xs: 2 },
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
            }}
          >
            <ContentBox clauseContent={clauseContent} />
            <CheckContent checked={checked} onChange={handleChangeCheckbox} />
            {/* submit */}
            <Grid
              item
              xs={12}
              sx={{
                mt: 2,
                display: "flex",
                justifyContent: "flex-end",
                gap: 1,
              }}
            >
              <ButtonOutline onClick={() => navigate(-1)}>
                {t("back")}
              </ButtonOutline>
              <ButtonPrimary
                variant="contained"
                sx={{
                  width: isMobile ? "100%" : "auto",
                  display: isViewMode ? "none" : "block",
                }}
                onClick={() => {
                  const errors = validateForm();
                  if (Object.keys(errors).length > 0) {
                    setFormErrors(errors);
                    return;
                  }

                  handleSaveContent({
                    i18n,
                    societyId: id,
                    societyName: namaPertubuhan,
                    amendmentId: amendmentId,
                    clauseContentId,
                    clauseNo: clauseNo,
                    clauseName: clauseName,
                    dataId,
                    isEdit,
                    createClauseContent,
                    editClauseContent,
                    description: clauseContent,
                    modifiedTemplate: clauseContentEditable,
                    constitutionValues: [
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohJawatan,
                        titleName: "Tempoh Pelantikan Jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: lainlain ? lainlain : pemilihanAjk,
                        titleName: "Jenis Mesyuarat Agung",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: kekerapan,
                        titleName: "Kekerapan Mesyuarat Jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohPelucutan,
                        titleName:
                          "Bilangan tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: tempohPelucutanWaktu,
                        titleName:
                          "Tempoh membela diri/ meminta penjelasan dari penggantungan/ pelucutan jawatankuasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: pengerusi,
                        titleName: "Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahPengerusi,
                        titleName: "Bilangan Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: timbalan,
                        titleName: "Timbalan Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahTimbalan,
                        titleName: "Bilangan Timbalan Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: naib,
                        titleName: "Naib Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahNaib,
                        titleName: "Bilangan Naib Pengerusi",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: setiaUsaha,
                        titleName: "Setiausaha",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahSetiaUsaha,
                        titleName: "Bilangan Setiausaha",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: penolongSetiaUsaha,
                        titleName: "Penolong Setiausaha",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahPenolongSetiaUsaha,
                        titleName: "Bilangan Penolong Setiausaha",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: bendahari,
                        titleName: "Bendahari",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahBendahari,
                        titleName: "Bilangan Bendahari",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: penolongBendahari,
                        titleName: "Penolong Bendahari",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahPenolongBendahari,
                        titleName: "Bilangan Penolong Bendahari",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: ahliBiasa,
                        titleName: "Ahli Jawatankuasa Biasa",
                      },
                      {
                        constitutionContentId: null,
                        societyName: namaPertubuhan,
                        definitionName: jumlahAhliBiasa,
                        titleName: "Bilangan Ahli Jawatankuasa Biasa",
                      },
                    ],
                    clause: "clause6",
                    clauseCount: 6,
                  });
                }}
                disabled={isCreatingContent || isEditingContent || !checked}
              >
                {isCreatingContent || isEditingContent
                  ? t("saving")
                  : t("save")}
              </ButtonPrimary>
            </Grid>
          </Box>
        </Grid>
      </Grid>
    </>
  );
};

export default FasalContentLapanBebasCawangan;
