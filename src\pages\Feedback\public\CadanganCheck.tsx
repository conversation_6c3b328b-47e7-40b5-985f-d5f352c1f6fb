import { Box, Fade, FormControl, FormHelperText, Grid, MenuItem, Select, Table, TableBody, TableCell, TableHead, TableRow, TextField, Theme, Typography, useMediaQuery } from "@mui/material"
import { ButtonPrimary, FeedbackPublicSelectLanguage } from "@/components"
import { FeedbackStatus, IdTypes, urlParams, useQuery } from "@/helpers";
import { useTranslation } from "react-i18next";
import { ChangeEvent, useState } from "react";
import { IAddressNode } from "@/types";
import { API_URL } from "@/api";

enum TypeEnum {
  MAKLUM_BALAS = "jenis_MaklumBalas",
  ADUAN = "jenis_Aduan",
  ISU_SISTEM = "jenis_IsuSistem",
  KEPUASAN_PELANGAN = "jenis_KepuasanPelangan",
}

export const FeedbackPublicCadanganCheck = () => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery((theme: Theme) =>
    theme.breakpoints.down("sm")
  );
  const [formData, setFormData] = useState({
    refNo: "",
    idType: "",
    idNo: "",
    phoneNumber: "",
  });
  const [idType, setIdType] = useState("");
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState([]);
  const [showData, setShowData] = useState(false);
  const [formErrors, setFormErrors] = useState<{ [key: string]: string }>({});
  const { data: addressList, isLoading: isAddressLoading } = useQuery<{ data: IAddressNode[] }>({
    url: "society/admin/address/list"
  });

  const addressData = addressList?.data?.data ?? [];
  const sectionStyle = {
    color: "var(--primary-color)",
    marginBottom: "16px",
    borderRadius: "16px",
    fontSize: "14px",
    fontWeight: "500 !important",
  };
  const labelStyle = {
    fontSize: "14px",
    color: "#666666",
    fontWeight: "400 !important",
    display: "flex",
    alignItems: "center",
  };

  const getStateName = (stateCode: any) => {
    const stateName = addressData.filter((i) => i.id == stateCode);
    return stateName[0]?.name;
  };
  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevState) => ({
      ...prevState,
      [name]: value,
    }));
    setFormErrors((prev) => ({ ...prev, [name]: "" }));
  };
  const validateForm = () => {
    const errors: { [key: string]: string } = {};
    if (!formData.idType) errors.idType = t("requiredValidation");
    if (!formData.idNo) errors.idNo = t("requiredValidation");

    return errors;
  };
  const handleSave = async () => {
    const errors = validateForm();
    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }
    const data = { ...formData };

    setLoading(true);
    setShowData(false);

    const url = urlParams(`${API_URL}/complaint/feedback/search`, {
      pageSize: 10,
      pageNo: 1,
      identificationNo: data.idNo,
      id: data.refNo,
      phoneNumber: data.phoneNumber,
    });

    try {
      const response = await fetch(url, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      const responseData = await response.json();
      if (responseData?.status === "SUCCESS") {
        setTableData(responseData?.data?.data);
        console.log(responseData?.data?.data);
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoading(false);
      setShowData(true);
    }
  };
  function getTypeEnumValue(key: string) {
    const result = TypeEnum?.[key as keyof typeof TypeEnum] ?? null;
    return result ? t(result) : "-";
  }
  const getStatusName = (status: any) => {
    const statusName = FeedbackStatus.filter((i) => i.value == status);
    return t(statusName[0]?.label);
  };

  return (
    <Box>
      <FeedbackPublicSelectLanguage />
      <Fade in={true} timeout={500}>
        <Box
          sx={{ mt: 2, backgroundColor: "white", p: 3, borderRadius: "15px" }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("checkFeedbacks")}
            </Typography>

            <Grid container spacing={2} sx={{ pl: 2, mb: 6, mt: 2 }}>
              {/* Reference Number */}
              <Grid item xs={12} sm={4} sx={labelStyle}>
                <Typography>{t("reference_number")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  fullWidth
                  name="refNo"
                  value={formData.refNo}
                  onChange={handleInputChange}
                  error={!!formErrors.refNo}
                  helperText={formErrors.refNo}
                />
              </Grid>
              {/* Identification type */}
              <Grid item xs={12} sm={4} sx={labelStyle}>
                <Typography>
                  {t("idType")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <FormControl
                  fullWidth
                  required
                  error={!!formErrors.idType}
                >
                  <Select
                    value={idType}
                    displayEmpty
                    required
                    onChange={(e) => {
                      setIdType(e.target.value);
                      setFormData((prevState) => ({
                        ...prevState,
                        idType: e.target.value,
                      }));
                      setFormErrors((prev) => ({ ...prev, idType: "" }));
                    }}
                  >
                    <MenuItem value="" disabled>
                      {t("pleaseSelect")}
                    </MenuItem>
                    {IdTypes.map((item, index) => {
                      return (
                        <MenuItem value={item?.value} key={index}>
                          {t(item?.label)}
                        </MenuItem>
                      );
                    })}
                  </Select>

                  {formErrors.idType && (
                    <FormHelperText>{formErrors.idType}</FormHelperText>
                  )}
                </FormControl>
              </Grid>
              {/* ID Number */}
              <Grid item xs={12} sm={4} sx={labelStyle}>
                <Typography>
                  {t("idNumber")} <span style={{ color: "red" }}>*</span>
                </Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  fullWidth
                  required
                  name="idNo"
                  value={formData.idNo}
                  onChange={handleInputChange}
                  error={!!formErrors.idNo}
                  helperText={formErrors.idNo}
                />
              </Grid>
              {/* Phone number */}
              <Grid item xs={12} sm={4} sx={labelStyle}>
                <Typography>{t("phoneNumber")}</Typography>
              </Grid>
              <Grid item xs={12} sm={8}>
                <TextField
                  fullWidth
                  required
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  inputProps={{ inputMode: "numeric", pattern: "[0-9]*" }}
                  onChange={(e: ChangeEvent<HTMLInputElement>) => {
                    const value = e.target.value;
                    if (/^\d*$/.test(value)) {
                      handleInputChange(e);
                    }
                  }}
                  error={!!formErrors.phoneNumber}
                  helperText={formErrors.phoneNumber}
                />
              </Grid>
            </Grid>
          </Box>

          <Grid
            item
            xs={12}
            sx={{
              mt: 2,
              display: "flex",
              flexDirection: isMobile ? "column" : "row",
              justifyContent: "flex-end",
              gap: 1,
            }}
          >
            <ButtonPrimary
              variant="contained"
              sx={{
                width: isMobile ? "100%" : "auto",
              }}
              onClick={handleSave}
              disabled={loading}
            >
              {t("semak")}
            </ButtonPrimary>
          </Grid>
        </Box>
      </Fade>

      {showData ? (
        <Box
          sx={{
            backgroundColor: "white",
            p: 3,
            borderRadius: "15px",
            mt: 3,
          }}
        >
          <Box
            sx={{
              p: { xs: 1, sm: 2, md: 3 },
              border: "1px solid #D9D9D9",
              borderRadius: "14px",
              mb: 2,
            }}
          >
            <Typography variant="subtitle1" sx={sectionStyle}>
              {t("feedbackList")}
            </Typography>

            <Box sx={{ overflowX: "auto", width: "100%" }}>
              {tableData?.length == 0 ? (
                <Typography
                  fontSize="16px"
                  fontWeight={600}
                  textAlign="center"
                  lineHeight="24px"
                  marginTop="18px"
                  color={"#000"}
                >
                  {t("noData")}
                </Typography>
              ) : null}
              {tableData?.length > 0 ? (
                <Table sx={{ mt: "20px" }}>
                  <TableHead
                    sx={{
                      "& th": {
                        textAlign: "center",
                      },
                    }}
                  >
                    <TableRow sx={{ backgroundColor: "#fff" }}>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("idNumberPlaceholder")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("jenisCadanganMaklumBalas")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("tajukButiran")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("lokasi")}
                      </TableCell>
                      <TableCell sx={{ fontWeight: "bold" }}>
                        {t("status")}
                      </TableCell>
                    </TableRow>
                  </TableHead>

                  {tableData?.length > 0
                    ? tableData.map((item, index) => {
                        const {
                          id,
                          identificationNo,
                          type,
                          title,
                          stateCode,
                          status,
                        } = item;
                        return (
                          <TableBody
                            sx={{
                              background: "#fff",
                              "& td": {
                                textAlign: "center",
                              },
                            }}
                            key={index + id}
                          >
                            <TableRow>
                              <TableCell>{identificationNo}</TableCell>
                              <TableCell>
                                {getTypeEnumValue(type)}
                              </TableCell>
                              <TableCell
                                sx={{ textTransform: "uppercase" }}
                              >
                                {title}
                              </TableCell>
                              <TableCell>
                                {getStateName(stateCode)}
                              </TableCell>
                              <TableCell>
                                {getStatusName(status)}
                              </TableCell>
                            </TableRow>
                          </TableBody>
                        );
                      })
                    : null}
                </Table>
              ) : null}
            </Box>
          </Box>
        </Box>
      ) : null}
    </Box>
  )
}
