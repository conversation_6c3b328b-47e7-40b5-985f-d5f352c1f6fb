import { Box, Typography } from "@mui/material";
import { useEffect, useRef, useState } from "react";
import { useTranslation } from "react-i18next";

interface fasalEditProps {
  clauseContentEditable: string;
  requiredFieldText: string[];
  setClauseContentEditable: (val: string) => void;
}

const sectionStyle = {
  color: "var(--primary-color)",
  marginBottom: "16px",
  borderRadius: "16px",
  fontSize: "14px",
  fontWeight: "500 !important",
};

export const EditableFasalTextArea: React.FC<fasalEditProps> = ({
  clauseContentEditable,
  requiredFieldText,
  setClauseContentEditable,
}) => {
  const placeholders = requiredFieldText;
  const { t } = useTranslation();
  const editableDivRef = useRef<HTMLDivElement>(null);
  const [initialized, setInitialized] = useState(false);

  const splitTextByPlaceholders = (text: string) => {
    const parts: { text: string; isPlaceholder: boolean }[] = [];
    let lastIndex = 0;

    const placeholderPositions = placeholders
      .map((placeholder) => {
        const index = text.indexOf(placeholder);
        return index >= 0 ? { index, placeholder } : null;
      })
      .filter(Boolean)
      .sort((a, b) => a!.index - b!.index);

    placeholderPositions.forEach((pos) => {
      if (!pos) return;

      if (pos.index > lastIndex) {
        parts.push({
          text: text.substring(lastIndex, pos.index),
          isPlaceholder: false,
        });
      }

      parts.push({
        text: pos.placeholder,
        isPlaceholder: true,
      });

      lastIndex = pos.index + pos.placeholder.length;
    });

    if (lastIndex < text.length) {
      parts.push({
        text: text.substring(lastIndex),
        isPlaceholder: false,
      });
    }
    console.log(parts);
    return parts;
  };

  const renderToDOM = (content: string) => {
    if (!editableDivRef.current) return;

    const parts = splitTextByPlaceholders(content);
    editableDivRef.current.innerHTML = "";

    parts.forEach((part) => {
      if (part.isPlaceholder) {
        const span = document.createElement("span");
        span.textContent = part.text;
        span.contentEditable = "false";
        span.style.background = "var(--primary-color)";
        span.style.padding = "2px 5px";
        span.style.color = "#fff";
        span.style.borderRadius = "10px";
        span.style.userSelect = "none";
        span.style.display = "inline";
        editableDivRef.current?.appendChild(span);
      } else {
        const textNode = document.createTextNode(part.text);
        editableDivRef.current?.appendChild(textNode);
      }
    });
  };

  const validatePlaceholders = () => {
    if (!editableDivRef.current) return;
    const currentContent = editableDivRef.current.textContent || "";
    setClauseContentEditable(currentContent);

    const isMissingPlaceholder = placeholders.some(
      (p) => !currentContent.includes(p)
    );

    if (isMissingPlaceholder) {
      renderToDOM(currentContent);
    }
  };

  useEffect(() => {
    if (!initialized && clauseContentEditable) {
      renderToDOM(clauseContentEditable);
      setInitialized(true);
    }
  }, [clauseContentEditable, initialized]);

  useEffect(() => {
    if (
      initialized &&
      editableDivRef.current &&
      clauseContentEditable !== editableDivRef.current.textContent
    ) {
      renderToDOM(clauseContentEditable);
    }
  }, [clauseContentEditable, initialized]);

  useEffect(() => {
    if (!initialized || !editableDivRef.current) return;

    const observer = new MutationObserver((mutations) => {
      mutations.forEach(() => validatePlaceholders());
    });

    observer.observe(editableDivRef.current, {
      childList: true,
      subtree: true,
      characterData: true,
    });

    return () => observer.disconnect();
  }, [initialized]);

  return (
    <Box
      sx={{
        p: { xs: 1, sm: 2, md: 3 },
        border: "1px solid #D9D9D9",
        backgroundColor: "#FFFFFF",
        borderRadius: "14px",
      }}
    >
      <Box
        sx={{
          backgroundColor: "#FFFFFF",
          borderRadius: "14px",
        }}
      >
        <Typography sx={{ mb: 1, ...sectionStyle }}>
          {t("pleaseUpdateFasalText")}
        </Typography>
        <Box
          sx={{
            backgroundColor: "#FFFFFF",
            borderRadius: "14px",
          }}
        >
          <Box
            sx={{
              p: { xs: 1 },
              border: "1px solid #D9D9D9",
              backgroundColor: "#FFFFFF",
              borderRadius: "14px",
            }}
          >
            <Box
              ref={editableDivRef}
              contentEditable
              onInput={validatePlaceholders}
              sx={{
                padding: 2,
                whiteSpace: "pre-wrap",
                overflowY: "auto",
                outline: "none",
                "&:focus": { borderColor: "primary.main" },
              }}
            />
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default EditableFasalTextArea;
