import React, { useCallback, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { FieldValues, useForm } from "react-hook-form";
import {
  debounce,
  ApplicationStatusEnum,
  useMutation,
  useQuery,
  useQueryFilterForm,
} from "@/helpers";
import { getUserPermission } from "@/redux/userReducer";

import {
  Box,
  IconButton,
  InputAdornment,
  TextField,
  Typography,
} from "@mui/material";
import FilterBar, { FilterOption } from "@/components/filter";
import {
  DialogConfirmation,
  DataTable,
  IColumn,
  ButtonPrimary,
} from "@/components";

import SearchIcon from "@mui/icons-material/Search";
import { EditIcon, EyeIcon, FilterIcon, TrashIcon } from "@/components/icons";

import { IApiPaginatedResponse, ILiquidationFeedbackPaging } from "@/types";

const actionIconMap: Record<number, React.ReactNode> = {
  1: <EditIcon />,
  2: <EyeIcon />,
  3: <EyeIcon />,
  4: <EyeIcon />,
  23: <EditIcon />,
  36: <EditIcon />,
};

const LandingPembubaran: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { id } = useParams();
  const navigate = useNavigate();
  // @ts-ignore
  const societyDataRedux = useSelector((state) => state.societyData.data);

  const isManager = useSelector(getUserPermission);
  const isBlacklisted = societyDataRedux?.subStatusCode === "003";

  const isAccessible = !isBlacklisted && isManager;
  const isMyLanguage = i18n.language === "my";

  const [selectedLiquidationId, setSelectedLiquidationId] = useState<
    number | null
  >(null);
  const [isSuccessDeleteLiquidation, setIsSuccessDeletedLiquidation] =
    useState(false);

  const [selectedFilters, setSelectedFilters] = useState<
    Record<string, string>
  >({
    dateApplied: "appliedYear",
    submissionDate: "submissionYear",
    decisionDate: "decisionYear",
    status: "status",
  });
  const [availableYear, setAvailableYear] = useState<FilterOption[]>([]);

  const formMethods = useForm<FieldValues>({
    defaultValues: {
      page: 1,
      pageSize: 10,
      applicantName: undefined,
      createdYear: undefined,
      submissionYear: undefined,
      decisionYear: undefined,
      status: undefined,
    },
  });
  const { setValue, watch } = formMethods;

  const { buildFilters } = useQueryFilterForm({
    formMethods,
    baseFilters: [
      {
        field: "societyId",
        value: id,
        operator: "eq",
      },
    ],
  });

  watch("applicantName");
  const page = watch("page");
  const pageSize = watch("pageSize");

  const columns: IColumn[] = [
    {
      field: "applicantName",
      headerName: t("namePemohon"),
      flex: 1,
      align: "center",
    },
    {
      field: "createdDate",
      headerName: t("dateApplied"),
      flex: 1,
      align: "center",
    },
    {
      field: "submissionDate",
      headerName: t("submissionDate"),
      flex: 1,
      align: "center",
    },
    {
      field: "decisionDate",
      headerName: t("decisionDate"),
      flex: 1,
      align: "center",
    },
    {
      field: "applicationStatus",
      headerName: "Status",
      flex: 1,
      align: "center",
      renderCell: ({ row }: any) => {
        return ApplicationStatusEnum[row.applicationStatusCode];
      },
    },
    {
      field: "actions",
      headerName: "",
      renderCell: ({ row }) => {
        const isSecretary = row.isSecretary === 1;
        const hasFeedback = row.feedback !== null;

        return (
          <>
            {isAccessible ? (
              <>
                <IconButton
                  onClick={() => {
                    // if (isSecretary || hasFeedback) {
                    //   navigate(`${row.liquidationId}`);
                    // } else {
                    //   navigate(`${row.liquidationId}/feedback`);
                    // }

                    navigate(`${row.id}`);
                  }}
                  sx={{
                    padding: 0,
                  }}
                >
                  {row ? actionIconMap[row.applicationStatusCode] : null}
                </IconButton>

                {row.applicationStatusCode === 1 && (
                  <IconButton
                    onClick={() => setSelectedLiquidationId(row.id)}
                    sx={{ p: 0.5 }}
                  >
                    <TrashIcon sx={{ color: "#FF0000" }} />
                  </IconButton>
                )}
              </>
            ) : (
              ""
            )}
          </>
        );
      },
    },
  ];

  const statusOptions = [
    { value: 2, label: "Menunggu Keputusan" },
    { value: 1, label: "Belum dihantar" },
    { value: 3, label: "Lulus" },
    { value: 4, label: "Tolak" },
    { value: 5, label: "MENUNGGU_BAYARAN_KAUNTER" },
    { value: 6, label: "MENUNGGU_BAYARAN_ONLINE" },
    { value: 11, label: "AKTIF" },
    { value: 41, label: "MENUNGGU_ULASAN_AGENSI_LUAR" },
  ];

  const filterOptions = {
    dateApplied: availableYear,
    submissionDate: availableYear,
    decisionDate: availableYear,
    status: statusOptions,
  };

  const handleSelectedFiltersChange = (
    updatedFilters: Record<string, string>
  ) => {
    setSelectedFilters(updatedFilters);
  };

  const onFilterChange = (filter: string, value: number | string) => {
    setValue("page", 1);
    switch (filter) {
      case "submissionDate":
        setValue("submissionYear", value);
        break;
      case "dateApplied":
        setValue("createdYear", value);
        break;
      case "decisionDate":
        setValue("decisionYear", value);
        break;
      case "status":
        setValue("status", value);
        break;
    }
  };

  const { fetch: deleteLiquidation, isLoading: isDeletingLiquidation } =
    useMutation({
      url: `society/liquidate/delete`,
      method: "put",
      onSuccess: () => {
        setIsSuccessDeletedLiquidation(true);
        fetchLiquidationList();
      },
    });

  const {
    data: liquidationListResponse,
    refetch: fetchLiquidationList,
    isLoading: isLoadingLiquidationList,
  } = useQuery({
    url: "society/liquidate/paging",
    filters: buildFilters(pageSize, page),
    onSuccess: (data) => {
      const availableYears: string[] =
        data?.data?.data?.availableCreatedYears ?? [];
      const availableYearsOptions = availableYears.map((year) => ({
        value: year,
        label: year,
      }));

      setAvailableYear(availableYearsOptions);
    },
  });

  const totalList =
    liquidationListResponse?.data?.data?.liquidationGetResponseList?.total ?? 0;
  const liquidationList: ILiquidationFeedbackPaging[] =
    liquidationListResponse?.data?.data?.liquidationGetResponseList?.data ?? [];

  const handleChangePage = (newPage: number) => setValue("page", newPage);

  const handleChangePageSize = (newPageSize: number) =>
    setValue("pageSize", newPageSize);

  const handleSearchApplicantName = useCallback(
    debounce(
      ({
        target: { value },
      }: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        setValue("applicantName", value || undefined);
      },
      1000
    ),
    []
  );

  const handleCloseDialog = () => {
    setSelectedLiquidationId(null);
    setIsSuccessDeletedLiquidation(false);
  };

  const handleDeleteLiquidation = () =>
    deleteLiquidation({
      id: selectedLiquidationId,
    });

  return (
    <>
      <Box
        sx={{
          backgroundColor: "white",
          padding: "45px 35px",
          borderRadius: "15px",
          marginBottom: 1,
          boxShadow: "0px 12px 12px 0px #EAE8E866",
        }}
      >
        <TextField
          fullWidth
          variant="outlined"
          placeholder={t("namePemohon")}
          sx={{
            display: "block",
            boxSizing: "border-box",
            width: "90%",
            height: "40px",
            marginInline: "auto",
            marginTop: "12px",
            background: "rgba(132, 132, 132, 0.3)",
            opacity: 0.5,
            border: "1px solid rgba(102, 102, 102, 0.8)",
            borderRadius: "10px",
            "& .MuiOutlinedInput-root": {
              height: "40px",
              "& fieldset": {
                border: "none",
              },
            },
          }}
          onChange={handleSearchApplicantName}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <SearchIcon sx={{ color: "#9CA3AF", marginLeft: "8px" }} />
              </InputAdornment>
            ),
          }}
        />

        <FilterBar
          filterOptions={filterOptions}
          onFilterChange={onFilterChange}
          selectedFilters={selectedFilters}
          onSelectedFiltersChange={handleSelectedFiltersChange}
        />

        <DataTable
          columns={columns}
          rows={liquidationList}
          page={page}
          rowsPerPage={pageSize}
          totalCount={totalList}
          onPageChange={handleChangePage}
          onPageSizeChange={handleChangePageSize}
          isLoading={isLoadingLiquidationList}
        />
      </Box>
      {isAccessible ? (
        <>
          <Box
            sx={{
              backgroundColor: "white",
              padding: "13px 16px",
              borderRadius: "15px",
              marginBottom: 1,
              boxShadow: "0px 12px 12px 0px #EAE8E866",
            }}
          >
            <Box
              sx={{
                width: "100%",
                borderRadius: "10px",
                border: "0.5px solid #DADADA",
                padding: "22px",
              }}
            >
              <Typography
                fontSize="14px"
                color="var(--primary-color)"
                fontWeight="500 !important"
                marginBottom="20px"
              >
                {t("liquidation")}
              </Typography>

              <Box
                sx={{
                  width: "100%",
                  display: "flex",
                  alignItems: "center",
                  justifyContent: "space-between",
                }}
              >
                <Typography
                  fontSize="14px"
                  color="#666666"
                  fontWeight="400 !important"
                >
                  {t("applicationForDissolution")}
                </Typography>

                <ButtonPrimary
                  onClick={() =>
                    navigate(
                      `/pertubuhan/society/${id}/senarai/pembubaran/create`
                    )
                  }
                >
                  {t("mohon")}
                </ButtonPrimary>
              </Box>
            </Box>
          </Box>

          <DialogConfirmation
            open={!!selectedLiquidationId}
            onConfirmationText={t("confirmDeleteApplication")}
            onSuccessText={
              isMyLanguage
                ? "Pembubaran berjaya dipadam"
                : "Liquidataion was successfully deleted."
            }
            isSuccess={isSuccessDeleteLiquidation}
            isMutating={isDeletingLiquidation}
            onClose={handleCloseDialog}
            onAction={handleDeleteLiquidation}
          />
        </>
      ) : (
        <></>
      )}
    </>
  );
};

export default LandingPembubaran;
