import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  TextField,
  Grid,
  Paper,
  IconButton,
} from "@mui/material";
import { API_URL } from "../../api";
import SentimentVeryDissatisfiedIcon from "@mui/icons-material/SentimentVeryDissatisfied";
import SentimentSatisfiedIcon from "@mui/icons-material/SentimentSatisfied";
import SentimentVerySatisfiedIcon from "@mui/icons-material/SentimentVerySatisfied";
import { ButtonPrimary } from "../../components/button";

enum TypeEnum {
  MAKLUM_BALAS = "Feedback",
  ADUAN = "Complaint",
  ISU_SISTEM = "System Issue",
  KEPUASAN_PELANGAN = "Customer satisfaction",
}

enum SatisfactoryEnum {
  TIDAK_MEMUASKAN = "TIDAK_MEMUASKAN",
  MEMUASKAN = "MEMUASKAN",
  SANGAT_MEMUASKAN = "SANGAT_MEMUASKAN",
}

type ModalProps = {
  open: boolean;
  onClose: () => void;
};

const FeedbackDialog: React.FC<ModalProps> = ({ open, onClose }) => {
  const { t } = useTranslation();

  const [isSubmitted, setIsSubmitted] = useState(false);
  const [selectedFeedback, setSelectedFeedback] = useState<String | null>(null);
  const [comment, setComment] = useState("");

  const identificationNo = localStorage.getItem("tmp-identificationNo") || "";

  const postFeedback = async () => {
    try {
      const response = await fetch(`${API_URL}/complaint/feedback/create`, {
        method: "post",
        headers: {
          portal: localStorage.getItem("portal") || "",
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          type: "KEPUASAN_PELANGAN",
          satisfaction: selectedFeedback,
          details: comment,
          identificationNo: JSON.parse(identificationNo),
        }),
      });

      const result = await response.json();

      if (result.status === "SUCCESS") {
        setIsSubmitted(true);
      } else {
        setIsSubmitted(false);
        console.error("API Error:", result);
      }
    } catch (error) {
      setIsSubmitted(false);
      console.error("Error fetching society data:", error);
    }
  };

  const handleSubmit = () => {
    postFeedback();
  };

  useEffect(() => {
    if (isSubmitted) {
      setTimeout(() => {
        onClose();
      }, 10000);
    }
  }, [isSubmitted]);

  return (
    <>
      <Dialog
        open={open}
        onClose={onClose}
        maxWidth="sm"
        fullWidth
        sx={{
          "& .MuiDialog-paper": {
            minHeight: "300px",
          },
        }}
      >
        {isSubmitted ? (
          <>
            <DialogContent>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "column",
                  justifyContent: "center",
                  textAlign: "center",
                  alignItems: "center",
                }}
              >
                <Typography
                  variant="h6"
                  sx={{ mt: 6, mb: 2, color: "var(--primary-color)" }}
                >
                  Terima kasih atas maklum balas anda!
                </Typography>
                <IconButton>
                  <img
                    src={"/appreciated-emoji-icon.svg"}
                    alt="emoji-icon"
                    width="140px"
                    height="140px"
                  />
                </IconButton>
              </Box>
            </DialogContent>
          </>
        ) : (
          <>
            {" "}
            <DialogTitle
              align="center"
              sx={{ py: 3, color: "var(--primary-color)" }}
            >
              {t("feedbackDialog_title")}
            </DialogTitle>
            <DialogContent>
              <Typography align="center" sx={{ pb: 3 }}>
                {t("feedbackDialog_subtext1")}
              </Typography>
              <Typography align="center" sx={{ pb: 3 }}>
                {t("feedbackDialog_subtext2")}
              </Typography>
              <Grid container spacing={2} justifyContent="center">
                <Grid item>
                  <Paper
                    onClick={() =>
                      setSelectedFeedback(SatisfactoryEnum.TIDAK_MEMUASKAN)
                    }
                    elevation={
                      selectedFeedback === SatisfactoryEnum.TIDAK_MEMUASKAN
                        ? 4
                        : 1
                    }
                    sx={{
                      padding: 2,
                      textAlign: "center",
                      cursor: "pointer",
                      backgroundColor:
                        selectedFeedback === SatisfactoryEnum.TIDAK_MEMUASKAN
                          ? "rgba(65, 195, 195, 0.5)"
                          : "rgba(65, 195, 195, 0.2)",
                    }}
                  >
                    <SentimentVeryDissatisfiedIcon
                      color="disabled"
                      fontSize="large"
                    />
                    <Typography>{t("tidakMemuaskan")}</Typography>
                  </Paper>
                </Grid>
                <Grid item>
                  <Paper
                    onClick={() =>
                      setSelectedFeedback(SatisfactoryEnum.MEMUASKAN)
                    }
                    elevation={
                      selectedFeedback === SatisfactoryEnum.MEMUASKAN ? 4 : 1
                    }
                    sx={{
                      padding: 2,
                      textAlign: "center",
                      cursor: "pointer",
                      backgroundColor:
                        selectedFeedback === SatisfactoryEnum.MEMUASKAN
                          ? "rgba(65, 195, 195, 0.5)"
                          : "rgba(65, 195, 195, 0.2)",
                    }}
                  >
                    <SentimentSatisfiedIcon color="disabled" fontSize="large" />
                    <Typography>{t("memuaskan")}</Typography>
                  </Paper>
                </Grid>
                <Grid item>
                  <Paper
                    onClick={() =>
                      setSelectedFeedback(SatisfactoryEnum.SANGAT_MEMUASKAN)
                    }
                    elevation={
                      selectedFeedback === SatisfactoryEnum.SANGAT_MEMUASKAN
                        ? 4
                        : 1
                    }
                    sx={{
                      padding: 2,
                      textAlign: "center",
                      cursor: "pointer",
                      backgroundColor:
                        selectedFeedback === SatisfactoryEnum.SANGAT_MEMUASKAN
                          ? "rgba(65, 195, 195, 0.5)"
                          : "rgba(65, 195, 195, 0.2)",
                    }}
                  >
                    <SentimentVerySatisfiedIcon
                      color="disabled"
                      fontSize="large"
                    />
                    <Typography>{t("sangatMemuaskan")}</Typography>
                  </Paper>
                </Grid>
              </Grid>
              <Box
                sx={{
                  display: "flex",
                  flexDirection: "row",
                  gap: 2,
                }}
              >
                <Typography
                  sx={{
                    m: "auto",
                  }}
                >
                  {t("remarks")}:
                </Typography>
                <TextField
                  fullWidth
                  margin="normal"
                  multiline
                  rows={3}
                  variant="outlined"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                />
              </Box>
            </DialogContent>
            <DialogActions
              sx={{
                alignSelf: "center",
                mb: 2,
              }}
            >
              <ButtonPrimary
                onClick={handleSubmit}
                disabled={!selectedFeedback}
              >
                {t("hantar")}
              </ButtonPrimary>
            </DialogActions>
          </>
        )}
      </Dialog>
    </>
  );
};

export default FeedbackDialog;
